#!/bin/bash

# ProductVideo Deployment Script
# Supports single machine deployment with all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="productvideo"
DOMAIN=${DOMAIN:-"localhost"}
ENVIRONMENT=${ENVIRONMENT:-"production"}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-$(openssl rand -base64 32)}
REDIS_PASSWORD=${REDIS_PASSWORD:-$(openssl rand -base64 32)}
JWT_SECRET=${JWT_SECRET:-$(openssl rand -base64 64)}

echo -e "${BLUE}🚀 ProductVideo Deployment Script${NC}"
echo -e "${BLUE}===================================${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if user is in docker group
    if ! groups $USER | grep &>/dev/null '\bdocker\b'; then
        print_warning "User $USER is not in the docker group. You may need to run with sudo."
    fi
    
    print_status "Prerequisites check passed ✓"
}

# Create environment file
create_env_file() {
    print_status "Creating environment configuration..."
    
    cat > .env << EOF
# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=${ENVIRONMENT}
DEBUG=false
BASE_URL=https://${DOMAIN}
FRONTEND_URL=https://${DOMAIN}
SECRET_KEY=${JWT_SECRET}
JWT_SECRET_KEY=${JWT_SECRET}

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://app_user:${POSTGRES_PASSWORD}@postgres:5432/ecommerce_db
POSTGRES_DB=ecommerce_db
POSTGRES_USER=app_user
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
REDIS_PASSWORD=${REDIS_PASSWORD}

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
STORAGE_PROVIDER=local
LOCAL_STORAGE_PATH=/app/storage
LOCAL_STORAGE_BASE_URL=https://${DOMAIN}/storage
S3_BUCKET_NAME=productvideo-videos

# =============================================================================
# OAUTH CONFIGURATION
# =============================================================================
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# =============================================================================
# SHOPIFY CONFIGURATION
# =============================================================================
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=noreply@${DOMAIN}

# =============================================================================
# MONITORING
# =============================================================================
LOG_LEVEL=INFO
GRAFANA_ADMIN_PASSWORD=admin123
EOF

    print_status "Environment file created ✓"
}

# Setup directories
setup_directories() {
    print_status "Setting up directories..."
    
    # Create required directories
    mkdir -p logs
    mkdir -p storage
    mkdir -p nginx/ssl
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    
    # Set permissions
    chmod 755 logs storage
    
    print_status "Directories created ✓"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certs() {
    if [[ "$DOMAIN" == "localhost" ]]; then
        print_status "Generating self-signed SSL certificates for development..."
        
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/server.key \
            -out nginx/ssl/server.crt \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=${DOMAIN}"
        
        print_warning "Using self-signed certificates. For production, use Let's Encrypt."
    else
        print_status "For production deployment, please setup Let's Encrypt certificates"
        print_status "Run: sudo certbot --nginx -d ${DOMAIN}"
    fi
}

# Create monitoring configuration
setup_monitoring() {
    print_status "Setting up monitoring configuration..."
    
    # Prometheus configuration
    cat > monitoring/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
EOF

    # Grafana datasource
    cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    print_status "Monitoring configuration created ✓"
}

# Build and start services
deploy_services() {
    print_status "Building and starting services..."
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build --no-cache
    
    # Start services
    docker-compose up -d
    
    print_status "Services started ✓"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for database..."
    until docker-compose exec -T postgres pg_isready -U app_user -d ecommerce_db; do
        sleep 2
    done
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    until docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    
    # Wait for backend
    print_status "Waiting for backend..."
    until curl -f http://localhost:8000/health; do
        sleep 5
    done
    
    print_status "All services are ready ✓"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    docker-compose exec backend python -m alembic upgrade head
    
    print_status "Database migrations completed ✓"
}

# Create admin user
create_admin_user() {
    print_status "Creating admin user..."
    
    read -p "Enter admin email: " ADMIN_EMAIL
    read -s -p "Enter admin password: " ADMIN_PASSWORD
    echo
    
    docker-compose exec backend python -c "
import asyncio
from modules.auth.service import auth_service
from modules.auth.schemas import UserCreate
from src.core.db.database import get_db

async def create_admin():
    async for db in get_db():
        user_create = UserCreate(
            email='${ADMIN_EMAIL}',
            password='${ADMIN_PASSWORD}',
            first_name='Admin',
            role='admin',
            is_verified=True
        )
        try:
            user = await auth_service.create_user(db, user_create)
            print(f'Admin user created: {user.email}')
        except Exception as e:
            print(f'Error creating admin user: {e}')
        break

asyncio.run(create_admin())
"
    
    print_status "Admin user created ✓"
}

# Display deployment information
show_deployment_info() {
    echo
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo
    echo -e "${YELLOW}Access URLs:${NC}"
    echo -e "Frontend:    https://${DOMAIN}"
    echo -e "Backend API: https://${DOMAIN}/api"
    echo -e "Grafana:     https://${DOMAIN}:3000 (admin/admin123)"
    echo -e "Prometheus:  https://${DOMAIN}:9090"
    echo -e "Mailhog:     https://${DOMAIN}:8025"
    echo
    echo -e "${YELLOW}Credentials:${NC}"
    echo -e "Database Password: ${POSTGRES_PASSWORD}"
    echo -e "Redis Password:    ${REDIS_PASSWORD}"
    echo -e "JWT Secret:        ${JWT_SECRET}"
    echo
    echo -e "${YELLOW}Useful Commands:${NC}"
    echo -e "View logs:         docker-compose logs -f"
    echo -e "Restart services:  docker-compose restart"
    echo -e "Stop services:     docker-compose down"
    echo -e "Update services:   docker-compose pull && docker-compose up -d"
    echo
    echo -e "${GREEN}Save these credentials in a secure location!${NC}"
}

# Main deployment flow
main() {
    check_prerequisites
    create_env_file
    setup_directories
    generate_ssl_certs
    setup_monitoring
    deploy_services
    wait_for_services
    run_migrations
    
    # Ask if user wants to create admin user
    read -p "Do you want to create an admin user? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_admin_user
    fi
    
    show_deployment_info
}

# Handle script arguments
case "${1:-}" in
    "install")
        main
        ;;
    "update")
        print_status "Updating services..."
        docker-compose pull
        docker-compose up -d
        print_status "Update completed ✓"
        ;;
    "backup")
        print_status "Creating backup..."
        mkdir -p backups
        docker-compose exec -T postgres pg_dump -U app_user ecommerce_db > "backups/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        tar -czf "backups/storage_backup_$(date +%Y%m%d_%H%M%S).tar.gz" storage/
        print_status "Backup created ✓"
        ;;
    "restore")
        if [[ -z "${2:-}" ]]; then
            print_error "Please specify backup file: ./deploy.sh restore backup_file.sql"
            exit 1
        fi
        print_status "Restoring from backup: $2"
        docker-compose exec -T postgres psql -U app_user -d ecommerce_db < "$2"
        print_status "Restore completed ✓"
        ;;
    "logs")
        docker-compose logs -f "${2:-}"
        ;;
    "status")
        docker-compose ps
        ;;
    "stop")
        docker-compose down
        ;;
    "restart")
        docker-compose restart "${2:-}"
        ;;
    *)
        echo "Usage: $0 {install|update|backup|restore|logs|status|stop|restart}"
        echo
        echo "Commands:"
        echo "  install  - Full installation and setup"
        echo "  update   - Update services to latest versions"
        echo "  backup   - Create database and storage backup"
        echo "  restore  - Restore from backup file"
        echo "  logs     - View service logs"
        echo "  status   - Show service status"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart services"
        exit 1
        ;;
esac
