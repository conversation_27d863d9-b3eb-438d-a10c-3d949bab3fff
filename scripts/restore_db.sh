#!/bin/bash

# Script to restore the PostgreSQL database to the Docker Compose service
set -e

DB_CONTAINER="db" # Or whatever your db service container name is (e.g., e-commerce-db-1)
DB_NAME="ecommerce_db"
DB_USER="app_user"

if [ -z "$1" ]; then
  echo "Usage: $0 <path_to_sql_dump_file>"
  exit 1
fi

RESTORE_FILE="$1"

if [ ! -f "$RESTORE_FILE" ]; then
  echo "Error: Dump file '$RESTORE_FILE' not found!"
  exit 1
fi

echo "Restoring database '$DB_NAME' in service '$DB_CONTAINER' from '$RESTORE_FILE'..."

# Stop the API service temporarily to avoid conflicts during restore
echo "Stopping API service..."
docker compose stop api

# Ensure DB service is running
echo "Ensuring database service '$DB_CONTAINER' is running..."
docker compose up -d "$DB_CONTAINER"

# Clear existing data (optional, but good for a clean restore)
echo "Disconnecting all existing connections to the database..."
docker compose exec -T $DB_CONTAINER psql -U $DB_USER -d postgres -c "SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '$DB_NAME' AND pid <> pg_backend_pid();"

echo "Dropping existing database and recreating..."
docker compose exec -T $DB_CONTAINER dropdb -U $DB_USER $DB_NAME || true # Allow failure if db doesn't exist
docker compose exec -T $DB_CONTAINER createdb -U $DB_USER $DB_NAME

# Restore the database
cat "$RESTORE_FILE" | docker compose exec -T -i $DB_CONTAINER psql -U $DB_USER -d $DB_NAME

echo "Database restore complete from: $RESTORE_FILE"

# Start the API service again
echo "Starting API service..."
docker compose start api

echo "Remember to run 'docker compose up --build' if you've changed the backend Dockerfile."
