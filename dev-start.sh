#!/bin/bash

# Simple Local Development Startup Script
# Starts frontend on localhost:3000 without Docker dependencies

set -e

echo "🚀 Starting E-commerce Development Environment (Local Mode)..."

# Function to kill processes on specific ports
kill_port() {
    local port=$1
    echo "🔍 Checking for processes on port $port..."
    
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        echo "⚠️  Found processes on port $port: $pids"
        echo "🔪 Killing processes: $pids"
        kill -9 $pids 2>/dev/null || true
        sleep 1
    else
        echo "✅ Port $port is free"
    fi
}

# Kill any processes on port 3000
kill_port 3000

# Ensure environment files exist
if [ ! -f frontend/.env ]; then
    echo "📝 Creating frontend/.env from example..."
    cp frontend/.env.example frontend/.env
fi

if [ ! -f backend/.env ]; then
    echo "📝 Creating backend/.env from example..."
    cp backend/.env.example backend/.env
fi

echo "🧹 Cleaning up..."
sleep 1

# Start frontend development server
echo "🎨 Starting Frontend on localhost:3000..."
cd frontend && npm run dev &
FRONTEND_PID=$!

echo "⏳ Waiting for frontend to start..."
sleep 5

# Check if frontend is running
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is running"
else
    echo "⚠️  Frontend may still be starting..."
fi

echo ""
echo "🎉 Development Environment Ready!"
echo ""
echo "📱 Frontend:     http://localhost:3000"
echo "🔧 Note: Backend services require Docker or manual setup"
echo ""
echo "🛑 To stop: Press Ctrl+C or run: kill $FRONTEND_PID"
echo ""

# Wait for user to stop
trap "echo '🛑 Stopping services...'; kill $FRONTEND_PID 2>/dev/null || true; exit 0" INT TERM

# Keep script running
wait $FRONTEND_PID
