# ProductVideo Deployment Guide

## 🚀 Single Machine Deployment (Recommended)

This guide covers deploying the entire ProductVideo stack on a single machine using Docker Compose.

### Prerequisites

- Ubuntu 20.04+ or similar Linux distribution
- Docker and Docker Compose installed
- At least 4GB RAM, 2 CPU cores
- 50GB+ disk space

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes
```

### 2. Application Setup

```bash
# Clone repository
git clone <your-repo-url>
cd e-commerce

# Create environment file
cp .env.example .env

# Edit environment variables
nano .env
```

### 3. Environment Configuration

Create `.env` file in the root directory:

```env
# Database
DATABASE_URL=************************************************/ecommerce_db

# Redis
REDIS_URL=redis://redis:6379/0

# Storage (Local MinIO)
STORAGE_PROVIDER=s3
S3_BUCKET_NAME=ecommerce-videos
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin123
AWS_REGION=us-east-1
S3_ENDPOINT_URL=http://minio:9000

# Shopify
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key

# OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Email
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>

# Environment
ENVIRONMENT=production
DEBUG=false
```

### 4. Deploy Application

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f

# Check specific service logs
docker-compose logs -f api
docker-compose logs -f worker
```

### 5. Initial Setup

```bash
# Run database migrations
docker-compose exec api python -m alembic upgrade head

# Create admin user (optional)
docker-compose exec api python -c "
from modules.auth.service import create_admin_user
import asyncio
asyncio.run(create_admin_user('<EMAIL>', 'admin123'))
"

# Setup MinIO bucket (should be automatic)
docker-compose exec minio-setup mc ls myminio/
```

### 6. Verify Deployment

- **Frontend**: http://your-server-ip
- **Backend API**: http://your-server-ip/api/health
- **MinIO Console**: http://your-server-ip:9001
- **Grafana**: http://your-server-ip:3000 (admin/admin123)
- **Prometheus**: http://your-server-ip:9090
- **Mailhog**: http://your-server-ip:8025

## 🌐 Google Cloud Platform Deployment

### Option 1: Single VM on Compute Engine

```bash
# Create VM instance
gcloud compute instances create productvideo-app \
    --zone=us-central1-a \
    --machine-type=e2-standard-4 \
    --boot-disk-size=100GB \
    --boot-disk-type=pd-ssd \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --tags=http-server,https-server

# SSH into instance
gcloud compute ssh productvideo-app --zone=us-central1-a

# Follow single machine deployment steps above
```

### Option 2: Cloud Run + Cloud SQL + Redis

```bash
# Create Cloud SQL instance
gcloud sql instances create productvideo-db \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=us-central1

# Create database
gcloud sql databases create ecommerce_db --instance=productvideo-db

# Create Redis instance
gcloud redis instances create productvideo-redis \
    --size=1 \
    --region=us-central1

# Build and deploy backend
gcloud builds submit --tag gcr.io/PROJECT_ID/productvideo-backend backend/
gcloud run deploy productvideo-backend \
    --image gcr.io/PROJECT_ID/productvideo-backend \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated

# Build and deploy frontend
gcloud builds submit --tag gcr.io/PROJECT_ID/productvideo-frontend frontend/
gcloud run deploy productvideo-frontend \
    --image gcr.io/PROJECT_ID/productvideo-frontend \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated
```

## 🔧 Production Optimizations

### 1. SSL/TLS Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Firewall Configuration

```bash
# UFW setup
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 3. Monitoring Setup

```bash
# Install node_exporter for system metrics
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.6.1.linux-amd64.tar.gz
sudo cp node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/
sudo useradd --no-create-home --shell /bin/false node_exporter
sudo chown node_exporter:node_exporter /usr/local/bin/node_exporter

# Create systemd service
sudo tee /etc/systemd/system/node_exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

### 4. Backup Strategy

```bash
# Database backup script
cat > /home/<USER>/backup-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T db pg_dump -U app_user ecommerce_db > /home/<USER>/backups/db_backup_$DATE.sql
find /home/<USER>/backups -name "db_backup_*.sql" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup-db.sh

# Add to crontab
echo "0 2 * * * /home/<USER>/backup-db.sh" | crontab -
```

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting**

   ```bash
   docker-compose logs service-name
   docker-compose restart service-name
   ```

2. **Database connection issues**

   ```bash
   docker-compose exec db psql -U app_user -d ecommerce_db
   ```

3. **Redis connection issues**

   ```bash
   docker-compose exec redis redis-cli ping
   ```

4. **Storage issues**
   ```bash
   docker-compose exec minio mc ls myminio/
   ```

### Performance Tuning

1. **Increase worker replicas**

   ```yaml
   worker:
     deploy:
       replicas: 4 # Increase based on CPU cores
   ```

2. **Database optimization**

   ```bash
   # Add to postgres environment
   POSTGRES_SHARED_PRELOAD_LIBRARIES=pg_stat_statements
   POSTGRES_MAX_CONNECTIONS=200
   ```

3. **Redis optimization**
   ```bash
   # Add to redis command
   redis-server --maxmemory 1gb --maxmemory-policy allkeys-lru
   ```

## 📊 Monitoring & Maintenance

### Health Checks

```bash
# Check all services
curl http://localhost/health
curl http://localhost/api/health

# Check individual components
docker-compose exec api python -c "from src.core.db.database import engine; print('DB OK')"
docker-compose exec redis redis-cli ping
```

### Log Management

```bash
# View aggregated logs
docker-compose logs -f --tail=100

# Log rotation setup
sudo tee /etc/logrotate.d/docker-compose > /dev/null <<EOF
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF
```

This deployment guide provides everything needed to run ProductVideo on a single machine or scale to cloud infrastructure.
tructure.
