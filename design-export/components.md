# Component Style Guidelines (Non‑Prescriptive)

Use these guidelines to match the look and feel while keeping your existing component APIs and structure intact. Adjust class names/utilities to your stack as needed.

General
- Colors: primary green (#22C55E) with hover (#16A34A) and focus ring (#15803D). Neutrals from gray scale; strong text ≈ gray‑800, muted ≈ gray‑500.
- Typography: system UI stack; base size 14px with 20px line height; weights 400/500/600.
- Radii: small controls ~4–6px; larger containers ~8px; chips/pills are full.
- Focus: visible 2px ring using primary focus color; offset 2px where appropriate.
- Motion: subtle color/opacity transitions ~200–300ms; spinners with border‑t transparent.

Buttons
- Primary: solid primary background, white text, rounded (~8px), clear hover and focus states.
- Secondary/ghost: neutral surfaces (light gray or transparent) with subtle hover and visible focus.

Tabs
- Indicate active via color + 2px bottom border; inactive are muted neutral with hover brighten.

Inputs & Checkboxes
- Inputs: light neutral surface, clear border, rounded corners, visible focus ring; compact variants allowed.
- Checkbox: square ~20px with 4px radius, neutral border, checkmark icon visible on selected, focus ring.

Chips/Tags
- Neutral pill background, medium text, small colored dot (category color) with subtle border for contrast.

Cards/Thumbnails
- Rounded container on neutral surface; selection uses a semi‑transparent blue overlay; generating state uses a subtle blue overlay with spinner; failed state uses red.

Popovers/Menus
- White/dark surfaces with subtle border and shadow; hover state darkens slightly; small neutral footer actions.

Tables/Lists
- Sticky headers on subtle surface; row dividers; hover row tint; selected row uses subtle background.

Scrollbar (asset row)
- Thin height; light track with darker thumb (dark mode flips); rounded corners.

Dark Mode
- Class‑based (`html.dark`), mirrors light with appropriate neutrals; ensure contrast meets AA.
