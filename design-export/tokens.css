:root {
  /* Primary */
  --color-primary: #22C55E;
  --color-primary-hover: #16A34A;
  --color-primary-focus: #15803D;

  /* Neutrals (light) */
  --color-bg: #FFFFFF;
  --color-surface: #FFFFFF;
  --color-surface-subtle: #F3F4F6; /* gray-100 */
  --color-header: #F9FAFB; /* gray-50 */
  --color-border: #E5E7EB; /* gray-200 */
  --color-border-strong: #D1D5DB; /* gray-300 */
  --color-text: #111827; /* gray-900 */
  --color-text-strong: #1F2937; /* gray-800 */
  --color-text-muted: #6B7280; /* gray-500 */

  /* Semantic */
  --blue-100: #DBEAFE;
  --blue-500: #3B82F6;
  --blue-700: #1D4ED8;
  --blue-900: #1E3A8A;
  --green-100: #DCFCE7;
  --green-500: #22C55E;
  --red-100: #FEE2E2;
  --red-500: #EF4444;
  --red-700: #B91C1C;
  --amber-100: #FEF3C7;
  --orange-100: #FFEDD5;
  --orange-200: #FED7AA;
  --orange-700: #C2410C;
  --orange-900: #7C2D12;
  --purple-500: #A855F7;
  --pink-500: #EC4899;
  --emerald-500: #10B981;

  /* Radii */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-full: 9999px;

  /* Focus */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: var(--color-primary-focus);

  /* Motion */
  --motion-fast: 200ms;
  --motion-slow: 300ms;
}

.dark {
  --color-bg: #111827; /* gray-900 */
  --color-surface: #111827;
  --color-surface-subtle: #1F2937; /* gray-800 */
  --color-header: rgba(55,65,81,0.5); /* gray-700/50 */
  --color-border: #374151; /* gray-700 */
  --color-border-strong: #4B5563; /* gray-600 */
  --color-text: #F3F4F6; /* gray-100 */
  --color-text-strong: #E5E7EB; /* gray-200 */
  --color-text-muted: #9CA3AF; /* gray-400 */
}

/* Utility: align with app's custom asset scrollbar */
.asset-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #a0aec0 #edf2f7; /* thumb track */
}
.dark .asset-scrollbar {
  scrollbar-color: #4a5568 #2d3748; /* thumb track */
}
.asset-scrollbar::-webkit-scrollbar { height: 6px; }
.asset-scrollbar::-webkit-scrollbar-track {
  background: #edf2f7;
  border-radius: 10px;
}
.dark .asset-scrollbar::-webkit-scrollbar-track { background: #2d3748; }
.asset-scrollbar::-webkit-scrollbar-thumb {
  background-color: #a0aec0;
  border-radius: 10px;
  border: 2px solid #edf2f7;
}
.dark .asset-scrollbar::-webkit-scrollbar-thumb {
  background-color: #4a5568;
  border-color: #2d3748;
}

/* Optional: a standard spinner using primary color */
.ds-spinner {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  border-width: 2px;
  border-style: solid;
  border-color: var(--color-primary);
  border-top-color: transparent;
  animation: ds-spin 1s linear infinite;
}

@keyframes ds-spin {
  to { transform: rotate(360deg); }
}

