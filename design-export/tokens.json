{"meta": {"name": "MediaStudio Design Export", "version": "1.0.0", "generatedAt": "2025-09-12T08:28:52Z", "source": "Tailwind CDN config in index.html + component class recipes"}, "theme": {"darkMode": {"strategy": "class", "className": "dark"}}, "colors": {"primary": {"default": "#22C55E", "hover": "#16A34A", "focus": "#15803D"}, "neutral": {"light": {"bg": "#FFFFFF", "surface": "#FFFFFF", "surfaceSubtle": "#F3F4F6", "header": "#F9FAFB", "border": "#E5E7EB", "borderStrong": "#D1D5DB", "text": "#111827", "textMuted": "#6B7280", "textStrong": "#1F2937"}, "dark": {"bg": "#111827", "surface": "#111827", "surfaceSubtle": "#1F2937", "header": "#37415180", "border": "#374151", "borderStrong": "#4B5563", "text": "#F3F4F6", "textMuted": "#9CA3AF", "textStrong": "#E5E7EB"}}, "semantic": {"blue": {"100": "#DBEAFE", "500": "#3B82F6", "700": "#1D4ED8", "900": "#1E3A8A"}, "green": {"100": "#DCFCE7", "500": "#22C55E", "900_50": "rgba(20,83,45,0.5)"}, "red": {"100": "#FEE2E2", "500": "#EF4444", "700": "#B91C1C", "900_50": "rgba(127,29,29,0.5)"}, "amber": {"100": "#FEF3C7", "900_50": "rgba(120,53,15,0.5)"}, "orange": {"100": "#FFEDD5", "200": "#FED7AA", "700": "#C2410C", "900": "#7C2D12"}, "purple": {"500": "#A855F7"}, "pink": {"500": "#EC4899"}, "emerald": {"500": "#10B981"}, "black": {"DEFAULT": "#000000"}}}, "typography": {"fontFamily": "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif", "baseSize": "14px", "lineHeight": "20px", "weights": {"regular": 400, "medium": 500, "semibold": 600}, "rendering": {"smoothing": "antialiased", "optimizeLegibility": true}}, "radii": {"xs": "4px", "sm": "6px", "md": "8px", "full": "9999px"}, "borders": {"width": "1px", "strongWidth": "2px"}, "focus": {"ringWidth": "2px", "ringColor": "var(--color-primary-focus)", "ringOffset": "2px"}, "motion": {"duration": "200ms", "durationSlow": "300ms"}, "components": {"button.primary": {"classes": "h-16 min-w-[240px] px-6 text-base font-semibold text-white bg-primary rounded-lg disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-focus dark:focus:ring-offset-gray-900 transition-all"}, "tab.primary": {"active": "text-primary border-b-2 border-primary", "inactive": "text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 border-b-2 border-transparent"}, "input.search.compact": {"classes": "h-6 px-2 py-0.5 text-[11px] leading-none rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 placeholder-gray-400 w-40 focus:outline-none focus:ring-1 focus:ring-primary"}, "checkbox.square": {"control": "appearance-none h-5 w-5 rounded-[4px] border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 peer hover:border-gray-600 dark:hover:border-gray-300", "icon": "absolute h-4 w-4 text-gray-400 dark:text-gray-500 peer-checked:opacity-100"}, "chip.base": {"classes": "inline-flex items-center space-x-1.5 text-xs font-medium px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200"}, "thumbnail.tile": {"container": "relative aspect-square rounded-md overflow-hidden group bg-gray-100 dark:bg-gray-700 flex items-center justify-center", "overlay.selected": "bg-blue-500/50", "overlay.generating": "bg-blue-500/15", "overlay.failed": "bg-red-500/15", "spinner": "animate-spin rounded-full border-2 border-blue-500 border-t-transparent"}, "prompt.surface": {"classes": "w-full min-h-32 p-2 text-sm text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700/50 rounded-md focus-within:ring-2 focus-within:ring-primary-focus focus-within:bg-white dark:focus-within:bg-gray-700 transition-colors"}, "popover.surface": {"classes": "rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg"}, "table.header": {"classes": "sticky top-0 z-10 bg-gray-50 dark:bg-gray-700/50 backdrop-blur-sm text-xs font-semibold uppercase text-gray-500 dark:text-gray-400"}}, "special": {"assetScrollbar": {"note": "See tokens.css for the exact CSS implementing the asset scrollbar styling for light/dark."}}}