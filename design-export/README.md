# MediaStudio Design Export

This folder exports the current front‑end design language so other repos/tools can consume it without scanning the app code. It includes minimal style tokens, CSS variables, and a simple prompt to guide style‑only alignment.

Contents
- `style-tokens.json` (preferred): Minimal style tokens (colors, typography, radii, focus, motion) for style‑only work.
- `tokens.json`: Full tokens (includes some class recipes) — optional reference.
- `tokens.css`: CSS variables for light/dark plus utility CSS for the asset scrollbar.
- `tailwind.config.snippet.js`: Tailwind color extension and dark‑mode strategy (if you use Tailwind).
- `components.md`: Non‑prescriptive style guidelines for common UI parts.
- `augmentation_prompt.txt`: Copy‑ready prompt to request a short, style‑only plan (no stack changes).

How to consume
- Style‑only: Read `style-tokens.json` for values and/or import `tokens.css` globally (after your reset) to get color variables and the asset scrollbar style.
  
  Example (Vite):
  
  ```js
  // main.tsx
  import './design-export/tokens.css';
  ```

- JSON tokens: Load `tokens.json` in your build/tooling to map tokens into your system, or to generate theme variables.

- Tailwind (optional): Merge `tailwind.config.snippet.js` into your Tailwind config (extend colors) and use `darkMode: 'class'` for identical behavior.

Dark mode
- Dark mode is class‑based (`html.dark`). Toggle by adding/removing the `dark` class to the root element.

Notes
- Scope is visual style only (colors, type, radii, focus, motion). No stack or architecture changes implied.
- These tokens mirror the live Tailwind/CSS values used in the app.
- No application code depends on this folder; it is a standalone export for external consumers.
