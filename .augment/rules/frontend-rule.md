---
type: "always_apply"
description: ">"
---

### 📝 **Shadcn UI Best Practices and Rules**

This document outlines the rules and best practices for building or modifying user interfaces using the **shadcn-ui** framework. All UI-related tasks should start with the tools available in the MCP server.

---

### ⚙️ **The Shadcn UI Workflow**

The process is divided into two main phases: **Planning** and **Implementation**.

#### **Phase 1: Planning**

The goal is to create a clear, no-code outline of the UI.

1.  **Discover Available Assets**:

    - Use `list_components()` and `list_blocks()` to find all available UI elements from the registry.

2.  **Map Requirements to Assets**:

    - Analyze the user request and match the required UI elements to the available components and blocks.

3.  **Prioritize Blocks**:

    - **Prefer `get_block(<name>)`** for complex, multi-component patterns like dashboards or login pages.
    - **Use `get_component(<name>)`** for smaller, specific needs like buttons or forms.

4.  **Produce a Clear Plan**:
    - The output should **only** include the page/route, section/slot, and the specific **component or block names** that will be used.
    - **Do not** include any implementation code in the planning phase.

---

#### **Phase 2: Implementation**

This phase involves retrieving and integrating the UI code.

1.  **Get a Demo First**:

    - Before using any component, always call `get_component_demo(<component_name>)` to understand its usage, required props, and structure.

2.  **Retrieve the Code**:

    - For a single component, use `get_component(<component_name>)`.
    - For a composite flow, use `get_block(<block_name>)`.

3.  **Implement Correctly**:

    - Mirror the usage pattern from the demo or block exactly, including structure, props, and accessibility.
    - **Don't improvise** unless absolutely necessary. Check for an existing block before manually composing components.

4.  **Composition and Styling**:
    - Compose using Shadcn primitives and patterns.
    - Avoid ad-hoc custom CSS unless it's required.
    - Preserve **TypeScript types**, **accessibility attributes**, and **keyboard interactions** from the demos.

---

### ✅ **Do's and 🚫 Don'ts**

- **Do**:

  - Route all Shadcn requests through the MCP server.
  - Prefer **Blocks** for complex, repeatable flows.
  - Keep the structure and props faithful to the demos.
  - Document assumptions and data requirements in your plan.

- **Don't**:
  - Skip the demo step.
  - Hand-roll patterns that already exist as a block.
  - Introduce custom styling that breaks Shadcn conventions.
  - Include implementation code in your planning output.

---

### 🚀 **Best Practices for Frontend and UI Development**

Beyond just using Shadcn UI, here are some general best practices for modern frontend development, covering architecture, state management, and aesthetics.

#### **1. Architecture and State Management**

- **Component-Based Architecture**: Build UIs from reusable, self-contained components. This improves maintainability and scalability. Libraries like **React**, **Vue**, and **Svelte** are built on this principle.
- **State Management**: For complex applications, use a dedicated state management library.
  - **React**: Use **Zustand** or **Jotai** for simple, fast state management. For more complex needs, **Redux Toolkit** is a powerful, opinionated choice.
  - **Vue**: Use **Pinia**, the official and modern choice for Vue 3.

#### **2. UI Libraries and Design Systems**

- **Choose a Solid Foundation**: A good UI library provides a set of pre-built, accessible, and themeable components.
  - **Shadcn UI**: Excellent for building **React** applications with **Tailwind CSS**. It provides a set of reusable components you can copy and paste into your project, giving you full control.
  - **Material UI (MUI)**: A comprehensive set of React components that implements Google's Material Design.
  - **Ant Design**: A React-based design system with a huge ecosystem and enterprise-level components.

#### **3. Styling and Theming**

- **Utility-First CSS**: Use a framework like **Tailwind CSS**. It speeds up development by allowing you to build UIs directly in your markup without writing custom CSS. Shadcn UI is built to work seamlessly with it.
- **CSS-in-JS**: Libraries like **Styled-Components** or **Emotion** allow you to write CSS directly in your JavaScript files, scoping styles to components and preventing style conflicts.
- **Theming**: Design systems should be easily themeable (e.g., light/dark mode, brand colors). Most modern libraries support this out of the box.

#### **4. Animations and Interactivity**

Animations can significantly improve the user experience by providing visual feedback and guiding the user's attention.

- **JavaScript Animation Libraries**:

  - **Framer Motion**: A powerful and declarative animation library for React. It's great for complex animations, gestures, and interactive components.
  - **React Spring**: A physics-based animation library that creates realistic, natural-feeling animations.
  - **GSAP (GreenSock Animation Platform)**: A high-performance, professional-grade animation library that works with any JavaScript framework.

- **CSS Animation Libraries**:
  - **Animate.css**: A simple, pre-built library of CSS animations. You mentioned `tw-animate-css`, which is a Tailwind CSS plugin based on this, making it easy to apply animations with utility classes.
  - **Tailwind CSS 4 with Plugins**: Tailwind's ecosystem includes many plugins for animations, transitions, and transforms, providing a native way to create interactive UIs.

#### **5. Accessibility (a11y)**

- **WCAG Guidelines**: Adhere to the Web Content Accessibility Guidelines. Ensure your UI is usable by people with disabilities.
- **Semantic HTML**: Use proper HTML tags (`<button>`, `<nav>`, `<main>`) to provide meaning to your content.
- **ARIA Attributes**: Use Accessible Rich Internet Applications (ARIA) attributes (`aria-label`, `role`, `tabindex`) when necessary to add semantic meaning to dynamic content and widgets.
- **Keyboard Navigation**: Ensure all interactive elements are reachable and usable via keyboard.

#### **6. Performance**

- **Code Splitting**: Break your JavaScript bundle into smaller chunks that are loaded on demand.
- **Image Optimization**: Use modern formats like WebP or AVIF and optimize image sizes to reduce load times.
- **Lazy Loading**: Defer the loading of off-screen components and images until they are needed.

#### **7. Development Server Rules**

- **Always use localhost:3000** - Never start additional instances on other ports. Kill existing processes if port 3000 is occupied before starting new ones.
- **Single instance policy** - Only one development server should be running at a time to avoid confusion and resource conflicts.
