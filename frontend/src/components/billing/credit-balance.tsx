"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CreditCard, Plus, Clock, AlertTriangle } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface CreditBalance {
  current_balance: number
  expires_at: string | null
  plan_tier: string
  monthly_grant: number
}

interface CreditBalanceProps {
  tenantId: string
  onPurchaseClick?: () => void
}

export function CreditBalance({ tenantId, onPurchaseClick }: CreditBalanceProps) {
  const [balance, setBalance] = useState<CreditBalance | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBalance()
  }, [tenantId])

  const fetchBalance = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/billing/tenants/${tenantId}/credits/balance`)
      if (!response.ok) throw new Error('Failed to fetch credit balance')
      const data = await response.json()
      setBalance(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Credit Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-24 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !balance) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Credit Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-600">
            {error || 'Unable to load credit balance'}
          </p>
        </CardContent>
      </Card>
    )
  }

  const isLowBalance = balance.current_balance < 1000
  const isExpiringSoon = balance.expires_at &&
    new Date(balance.expires_at).getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000 // 7 days

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Credit Balance
        </CardTitle>
        <CardDescription>
          Your current credits and subscription details
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-3xl font-bold">
              {balance.current_balance.toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">Available Credits</p>
          </div>
          <div className="flex gap-2">
            {isLowBalance && (
              <Badge variant="destructive">Low Balance</Badge>
            )}
            {isExpiringSoon && (
              <Badge variant="secondary">
                <Clock className="h-3 w-3 mr-1" />
                Expiring Soon
              </Badge>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Plan Tier</p>
            <p className="font-medium capitalize">{balance.plan_tier}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Monthly Grant</p>
            <p className="font-medium">{balance.monthly_grant.toLocaleString()} credits</p>
          </div>
        </div>

        {balance.expires_at && (
          <div className="text-sm">
            <p className="text-muted-foreground">Credits expire</p>
            <p className="font-medium">
              {formatDistanceToNow(new Date(balance.expires_at), { addSuffix: true })}
            </p>
          </div>
        )}

        <Button
          onClick={onPurchaseClick}
          className="w-full"
          variant={isLowBalance ? "default" : "outline"}
        >
          <Plus className="h-4 w-4 mr-2" />
          Purchase Credits
        </Button>
      </CardContent>
    </Card>
  )
}