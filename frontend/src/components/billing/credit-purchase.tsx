"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { CreditCard, ShoppingCart, CheckCircle, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface CreditPackage {
  id: string
  credits: number
  amount: number
  price_id: string
}

interface CreditPurchaseProps {
  tenantId: string
  packages: CreditPackage[]
  onPurchaseComplete?: () => void
}

export function CreditPurchase({ tenantId, packages, onPurchaseComplete }: CreditPurchaseProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedPackage, setSelectedPackage] = useState<CreditPackage | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const { toast } = useToast()

  const handlePurchase = async (pkg: CreditPackage) => {
    try {
      setIsProcessing(true)
      setSelectedPackage(pkg)

      // Create checkout session
      const response = await fetch(`/api/billing/tenants/${tenantId}/credits/purchase-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          package_id: pkg.id,
          success_url: `${window.location.origin}/dashboard?tab=billing&success=true`,
          cancel_url: `${window.location.origin}/dashboard?tab=billing&cancelled=true`,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create checkout session')
      }

      const sessionData = await response.json()

      // Redirect to Stripe Checkout
      window.location.href = sessionData.url

    } catch (error) {
      console.error('Purchase error:', error)
      toast({
        title: "Purchase Failed",
        description: "Unable to start checkout process. Please try again.",
        variant: "destructive",
      })
      setIsProcessing(false)
      setSelectedPackage(null)
    }
  }

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount / 100) // Assuming amount is in cents
  }

  const getCreditsPerDollar = (credits: number, amount: number) => {
    return (credits / (amount / 100)).toFixed(1)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="w-full">
          <ShoppingCart className="h-4 w-4 mr-2" />
          Purchase Credits
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Purchase Credits
          </DialogTitle>
          <DialogDescription>
            Choose a credit package to continue generating content without interruption.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {packages.map((pkg) => (
            <Card
              key={pkg.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedPackage?.id === pkg.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setSelectedPackage(pkg)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{pkg.credits.toLocaleString()} Credits</CardTitle>
                  {pkg.id.includes('popular') && (
                    <Badge variant="secondary">Popular</Badge>
                  )}
                </div>
                <CardDescription className="text-2xl font-bold text-primary">
                  {formatPrice(pkg.amount)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Credits:</span>
                    <span>{pkg.credits.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Per dollar:</span>
                    <span>{getCreditsPerDollar(pkg.credits, pkg.amount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Expires:</span>
                    <span>1 year</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Secure payment via Stripe
            </div>
            <div className="flex items-center gap-1 mt-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Credits never expire during subscription
            </div>
          </div>

          <Button
            onClick={() => selectedPackage && handlePurchase(selectedPackage)}
            disabled={!selectedPackage || isProcessing}
            size="lg"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : selectedPackage ? (
              <>
                Purchase {selectedPackage.credits.toLocaleString()} Credits
              </>
            ) : (
              'Select a Package'
            )}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground bg-muted p-3 rounded-md">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <div>
              <strong>Important:</strong> Purchased credits expire after 1 year. Credits from your subscription plan do not expire while your subscription is active.
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}