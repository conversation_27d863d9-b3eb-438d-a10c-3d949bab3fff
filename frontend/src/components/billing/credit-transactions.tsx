"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { History, TrendingUp, TrendingDown, Gift, CreditCard } from "lucide-react"
import { formatDistanceToNow, format } from "date-fns"

interface CreditTransaction {
  id: string
  type: string
  amount: number
  balance_after: number
  description: string
  created_at: string
  expires_at?: string
  metadata?: Record<string, any>
}

interface CreditTransactionsProps {
  tenantId: string
  limit?: number
}

export function CreditTransactions({ tenantId, limit = 20 }: CreditTransactionsProps) {
  const [transactions, setTransactions] = useState<CreditTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTransactions()
  }, [tenantId, limit])

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/billing/tenants/${tenantId}/credits/transactions?limit=${limit}`)
      if (!response.ok) throw new Error('Failed to fetch credit transactions')
      const data = await response.json()
      setTransactions(data.transactions || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'subscription_grant':
      case 'subscription_renewal':
        return <Gift className="h-4 w-4 text-green-600" />
      case 'purchase':
        return <CreditCard className="h-4 w-4 text-blue-600" />
      case 'usage':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'expiration':
        return <TrendingDown className="h-4 w-4 text-gray-600" />
      default:
        return <History className="h-4 w-4 text-gray-600" />
    }
  }

  const getTransactionBadgeVariant = (type: string) => {
    switch (type) {
      case 'subscription_grant':
      case 'subscription_renewal':
        return 'default'
      case 'purchase':
        return 'secondary'
      case 'usage':
        return 'destructive'
      case 'expiration':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const formatAmount = (amount: number) => {
    const sign = amount >= 0 ? '+' : ''
    return `${sign}${amount.toLocaleString()}`
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Credit History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex justify-between items-center">
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <History className="h-5 w-5" />
            Credit History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-600">{error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Credit History
        </CardTitle>
        <CardDescription>
          Recent credit transactions and usage
        </CardDescription>
      </CardHeader>
      <CardContent>
        {transactions.length === 0 ? (
          <p className="text-center text-muted-foreground py-8">
            No credit transactions yet
          </p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Balance</TableHead>
                  <TableHead className="text-right">Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(transaction.type)}
                        <Badge variant={getTransactionBadgeVariant(transaction.type)}>
                          {transaction.type.replace('_', ' ')}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="truncate" title={transaction.description}>
                        {transaction.description}
                      </div>
                      {transaction.expires_at && (
                        <div className="text-xs text-muted-foreground">
                          Expires {formatDistanceToNow(new Date(transaction.expires_at), { addSuffix: true })}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      <span className={transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {formatAmount(transaction.amount)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {transaction.balance_after.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-right text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(transaction.created_at), { addSuffix: true })}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}