import React, { useState, useRef, useEffect } from "react";
import { Asset } from "@/services/assetService";
import { cn } from "@/lib/utils";
import {
  Play,
  AlertCircle,
  Image as ImageIcon,
  CheckCircle,
} from "lucide-react";
import { Card } from "@/components/ui/card";

interface LazyThumbnailProps {
  asset: Asset;
  isSelected: boolean;
  onSelect: (asset: Asset, isMultiSelect: boolean) => void;
  isGenerated?: boolean;
  isFailedPlaceholder?: boolean;
  draggable?: boolean;
  showBorder?: boolean;
}

export const LazyThumbnail: React.FC<LazyThumbnailProps> = ({
  asset,
  isSelected,
  onSelect,
  isGenerated = false,
  isFailedPlaceholder = false,
  draggable = false,
  showBorder = true,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(asset, e.ctrlKey || e.metaKey);
  };

  const handleDragStart = (e: React.DragEvent) => {
    if (!draggable || asset.type === "video") {
      e.preventDefault();
      return;
    }

    setIsDragging(true);

    // Set drag data with comprehensive asset information - matching Thumbnail.tsx format
    const dragData = {
      type: "image",
      assetId: asset.id,
      filename: asset.filename,
      displayName: asset.displayName || asset.filename,
      url: asset.url,
      productId: asset.productId,
    };

    e.dataTransfer.setData("application/json", JSON.stringify(dragData));
    e.dataTransfer.setData("text/plain", asset.filename); // Fallback
    e.dataTransfer.effectAllowed = "copy";
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleImageLoad = () => {
    setIsLoaded(true);
    setIsError(false);
  };

  const handleImageError = () => {
    setIsError(true);
    setIsLoaded(false);
  };

  const isPlaceholder = asset.id.startsWith("temp_") || isFailedPlaceholder;
  const isVideo = asset.type === "video";

  return (
    <Card
      ref={containerRef}
      className={cn(
        "relative aspect-square overflow-hidden cursor-pointer transition-all duration-200 group rounded-none hover:shadow-none focus:ring-0 focus:ring-offset-0",
        showBorder
          ? isSelected
            ? "border-2 border-primary ring-2 ring-primary/20"
            : "border-2 border-border hover:border-border/80"
          : "border-none shadow-none hover:bg-transparent",
        isDragging && "opacity-50 scale-95",
        isFailedPlaceholder && showBorder && "border-destructive",
        draggable && "hover:shadow-lg"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable={draggable}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      title={`${asset.displayName || asset.filename}${isGenerated ? " (Generated)" : ""}`}
    >
      {/* Blurred placeholder background */}
      {!isLoaded && isInView && !isPlaceholder && (
        <div className="absolute inset-0 bg-gradient-to-br from-muted/50 to-muted/80 animate-pulse">
          <div className="w-full h-full bg-muted/20 backdrop-blur-sm flex items-center justify-center">
            <ImageIcon className="h-6 w-6 text-muted-foreground/50" />
          </div>
        </div>
      )}

      {/* Image/Video Content */}
      {isPlaceholder ? (
        <div className="w-full h-full bg-muted flex items-center justify-center">
          {isFailedPlaceholder ? (
            <AlertCircle className="h-8 w-8 text-destructive" />
          ) : (
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent" />
          )}
        </div>
      ) : (
        <>
          {isInView && (
            <img
              ref={imgRef}
              src={asset.url}
              alt={asset.displayName || asset.filename}
              className={cn(
                "w-full h-full object-contain transition-opacity duration-300",
                isLoaded ? "opacity-100" : "opacity-0"
              )}
              loading="lazy"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          )}

          {/* Error state with retry */}
          {isError && (
            <div className="absolute inset-0 bg-muted flex items-center justify-center">
              <div className="text-center">
                <AlertCircle className="h-6 w-6 text-destructive mx-auto mb-1" />
                <span className="text-xs text-muted-foreground mb-2 block">
                  Failed to load
                </span>
                <button
                  onClick={() => {
                    setIsError(false);
                    setIsLoaded(false);
                    // Force reload by changing the src
                    if (imgRef.current) {
                      const currentSrc = imgRef.current.src;
                      imgRef.current.src = "";
                      imgRef.current.src = currentSrc;
                    }
                  }}
                  className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded hover:bg-primary/90 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          )}

          {/* Video indicator */}
          {isVideo && isLoaded && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/20">
              <div className="bg-black/60 rounded-full p-2">
                <Play className="h-4 w-4 text-white fill-white" />
              </div>
            </div>
          )}

          {/* Generated badge */}
          {isGenerated && (
            <div className="absolute top-1 left-1">
              <div className="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded font-medium">
                AI
              </div>
            </div>
          )}

          {/* Selection indicator */}
          {isSelected && (
            <div className="absolute top-1 right-1">
              <div className="bg-primary text-primary-foreground rounded-full p-1">
                <CheckCircle className="h-3 w-3" />
              </div>
            </div>
          )}

          {/* Hover overlay */}
          {isHovered && !isPlaceholder && (
            <div className="absolute inset-0 bg-black/10 transition-opacity duration-200" />
          )}
        </>
      )}
    </Card>
  );
};
