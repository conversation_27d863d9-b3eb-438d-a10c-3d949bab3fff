import React, { useState, use<PERSON>emo, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Copy,
  Image,
  ChevronLeft,
  ChevronRight,
  Plus,
  Maximize2,
} from "lucide-react";
import { Asset } from "@/services/assetService";
import { Product } from "@/services/productService";
import { MainTab } from "@/types/mediaStudio";

import { RichPromptInput } from "./RichPromptInput";
import { Thumbnail } from "./Thumbnail";
import { LazyThumbnail } from "./LazyThumbnail";
import { ImageLightbox } from "./ImageLightbox";
import type { PromptWithImages } from "@/types/mediaStudio";
import { createPromptFromText } from "@/utils/promptUtils";

interface AttachedImage {
  id: string;
  url: string;
  filename: string;
  displayName?: string;
}

interface ProductGridProps {
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, string | PromptWithImages>;
  onPromptChange: (productId: string, value: PromptWithImages) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onSelectAllProducts: (isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  // Image attachment for generation composition
  attachedImages?: Record<string, AttachedImage[]>; // productId -> attached images
  onAttachImage?: (productId: string, image: AttachedImage) => void;
  onDetachImage?: (productId: string, imageId: string) => void;
  availableCollections?: Array<{ id: string; name: string; color: string }>;
  collectionFilters?: string[];
  onCollectionFiltersChange?: (filters: string[]) => void;
  searchQuery?: string;
  onSearchQueryChange?: (query: string) => void;
  filterSelectedOnly?: boolean;
  onFilterSelectedOnlyChange?: (value: boolean) => void;
  filterHasGenerated?: boolean;
  onFilterHasGeneratedChange?: (value: boolean) => void;
  sortMode?: "default" | "selected_first" | "generated_first";
  onSortModeChange?: (
    mode: "default" | "selected_first" | "generated_first"
  ) => void;
  productTotalCount?: number;
  onAssetDrop?: (assetId: string, targetProductId: string) => void;
}

const ProductRow: React.FC<{
  product: Product;
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, string | PromptWithImages>;
  onPromptChange: (productId: string, value: PromptWithImages) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  allAvailableAssets?: Asset[];
  allProducts?: Product[]; // All products for cross-product asset references
  attachedImages?: AttachedImage[];
  onAttachImage?: (productId: string, image: AttachedImage) => void;
  onDetachImage?: (productId: string, imageId: string) => void;
}> = ({
  product,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onCopyPromptToAll,
  generatedImages = {},
  allAvailableAssets = [],
  allProducts = [],
  attachedImages = [],
  onAttachImage,
  onDetachImage,
}) => {
  const isRowSelected = selectedProductIds.has(product.id);
  const hasPlaceholder = React.useMemo(
    () => product.assets.some((a) => a.id.startsWith("temp_")),
    [product.assets]
  );

  // Modal state for image preview
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);

  // Persistent state for selected image index per product
  const [selectedImageIndex, setSelectedImageIndex] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(`media-studio-preview-${product.id}`);
      return saved ? parseInt(saved, 10) : 0;
    }
    return 0;
  });

  const [thumbnailStartIndex, setThumbnailStartIndex] = useState(0);

  // Persist selected image index when it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        `media-studio-preview-${product.id}`,
        selectedImageIndex.toString()
      );
    }
  }, [selectedImageIndex, product.id]);

  const existingAssetIds = React.useMemo(
    () => new Set(product.assets.map((a) => a.id)),
    [product.assets]
  );

  const productGeneratedImages = useMemo(
    () => generatedImages[product.id] || [],
    [generatedImages, product.id]
  );

  const filteredGeneratedImages = useMemo(
    () => productGeneratedImages.filter((a) => !existingAssetIds.has(a.id)),
    [productGeneratedImages, existingAssetIds]
  );

  // Get all available assets for this product (original + generated)
  const allProductAssets = useMemo(() => {
    return [...product.assets, ...filteredGeneratedImages];
  }, [product.assets, filteredGeneratedImages]);

  return (
    <Card
      className={cn(
        // Subtle cards with medium-light corner radius (between md and lg), no shadows
        "transition-all duration-200 overflow-hidden flex flex-col border-0 shadow-none rounded-[7px]",
        isRowSelected ? "ring-2 ring-primary/20" : "",
        hasPlaceholder ? "animate-pulse" : ""
      )}
    >
      {/* Product Title Section */}
      <CardContent className="p-3 pb-1">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-foreground text-base leading-tight mb-1">
              {product.title}
            </h3>
            {/* Categories */}
            {(product.collections ?? []).length > 0 && (
              <div className="flex flex-wrap gap-1">
                {(product.collections ?? []).map((col) => (
                  <Badge
                    key={col.id}
                    variant="secondary"
                    className="text-xs px-2 py-0.5"
                  >
                    {col.name}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="flex items-center gap-3 ml-4">
            <div className="text-xs text-muted-foreground">
              {allProductAssets.length} asset
              {allProductAssets.length !== 1 ? "s" : ""}
            </div>
            <Checkbox
              checked={isRowSelected}
              onCheckedChange={(checked) =>
                onProductSelectionChange(product.id, checked as boolean)
              }
              className="w-4 h-4"
            />
          </div>
        </div>
      </CardContent>

      {/* Main Content Layout */}
      <div className="flex flex-1">
        {/* Left Side - Main Image with Thumbnails */}
        <div className="flex-1 flex flex-col">
          {/* Main Image Display */}
          <div className="relative aspect-square w-full group bg-muted/20">
            {allProductAssets.length > 0 ? (
              <>
                <Thumbnail
                  asset={allProductAssets[selectedImageIndex]}
                  isSelected={selectedAssetIds.has(
                    allProductAssets[selectedImageIndex].id
                  )}
                  onSelect={onAssetSelect}
                  showBorder={false}
                  draggable={true}
                  forceSquare={true}
                />

                {/* Bottom-right zoom button */}
                <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={() => setIsLightboxOpen(true)}
                    className="bg-black/70 hover:bg-black/90 text-white rounded-full p-2 shadow-lg hover:scale-110 transition-all duration-200"
                    title="View fullscreen"
                  >
                    <Maximize2 className="w-4 h-4" />
                  </button>
                </div>

                {/* Navigation Arrows - clearer and persistent */}
                {allProductAssets.length > 1 && (
                  <>
                    <button
                      onClick={() => {
                        const newIndex =
                          selectedImageIndex > 0
                            ? selectedImageIndex - 1
                            : allProductAssets.length - 1;
                        setSelectedImageIndex(newIndex);
                        // Adjust thumbnail view if needed
                        if (newIndex < thumbnailStartIndex) {
                          setThumbnailStartIndex(Math.max(0, newIndex));
                        } else if (newIndex >= thumbnailStartIndex + 5) {
                          setThumbnailStartIndex(
                            Math.min(allProductAssets.length - 5, newIndex - 4)
                          );
                        }
                      }}
                      className="absolute left-2 top-1/2 -translate-y-1/2 opacity-60 hover:opacity-100 transition-all duration-200 bg-black/70 hover:bg-black/90 text-white rounded-full p-2.5 shadow-md ring-1 ring-white/30 hover:scale-110"
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => {
                        const newIndex =
                          selectedImageIndex < allProductAssets.length - 1
                            ? selectedImageIndex + 1
                            : 0;
                        setSelectedImageIndex(newIndex);
                        // Adjust thumbnail view if needed
                        if (newIndex < thumbnailStartIndex) {
                          setThumbnailStartIndex(Math.max(0, newIndex));
                        } else if (newIndex >= thumbnailStartIndex + 5) {
                          setThumbnailStartIndex(
                            Math.min(allProductAssets.length - 5, newIndex - 4)
                          );
                        }
                      }}
                      className="absolute right-2 top-1/2 -translate-y-1/2 opacity-60 hover:opacity-100 transition-all duration-200 bg-black/70 hover:bg-black/90 text-white rounded-full p-2.5 shadow-md ring-1 ring-white/30 hover:scale-110"
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>
                  </>
                )}
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                <Image className="w-12 h-12" alt="No image available" />
              </div>
            )}
          </div>

          {/* Thumbnail Strip */}
          {allProductAssets.length > 0 && (
            <div className="relative group bg-muted/10">
              {/* Left fade mask */}
              {thumbnailStartIndex > 0 && (
                <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-muted/10 to-transparent z-10 pointer-events-none" />
              )}

              {/* Right fade mask */}
              {thumbnailStartIndex + 5 < allProductAssets.length && (
                <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-muted/10 to-transparent z-10 pointer-events-none" />
              )}

              <div
                className="grid grid-cols-5 gap-0 overflow-hidden transition-all duration-200 [&.drag-active]:bg-primary/10 [&.drag-active]:ring-2 [&.drag-active]:ring-primary/30"
                onDragOver={(e) => {
                  e.preventDefault();
                  e.currentTarget.classList.add("drag-active");
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  e.currentTarget.classList.remove("drag-active");
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.currentTarget.classList.remove("drag-active");

                  // Handle asset drop to thumbnail area
                  try {
                    const dragData = e.dataTransfer.getData("application/json");
                    if (dragData && onAssetDrop) {
                      const parsedData = JSON.parse(dragData);
                      if (parsedData.type === "image" && parsedData.assetId) {
                        onAssetDrop(parsedData.assetId, product.external_id);
                      }
                    }
                  } catch (error) {
                    console.error("Error handling asset drop:", error);
                  }
                }}
              >
                {(() => {
                  const visible = allProductAssets.slice(
                    thumbnailStartIndex,
                    thumbnailStartIndex + 5
                  );
                  const placeholders = Math.max(0, 5 - visible.length);
                  return (
                    <>
                      {visible.map((asset, index) => {
                        const actualIndex = thumbnailStartIndex + index;
                        const isActive = actualIndex === selectedImageIndex;
                        return (
                          <button
                            key={asset.id}
                            onClick={() => setSelectedImageIndex(actualIndex)}
                            className={cn(
                              "relative aspect-square transition-all duration-200",
                              isActive
                                ? "bg-primary/10 border-b-2 border-primary"
                                : "hover:bg-primary/5 border-b-2 border-transparent hover:border-primary/30"
                            )}
                          >
                            <LazyThumbnail
                              asset={asset}
                              isSelected={selectedAssetIds.has(asset.id)}
                              onSelect={() => {
                                // Change only the displayed main image; do not alter selection state
                                setSelectedImageIndex(actualIndex);
                              }}
                              showBorder={false}
                              draggable={true}
                            />
                          </button>
                        );
                      })}
                      {Array.from({ length: placeholders }).map((_, i) => (
                        <div
                          key={`thumb-placeholder-${i}`}
                          className="relative aspect-square border-b-2 border-transparent"
                        />
                      ))}
                    </>
                  );
                })()}
              </div>

              {/* Navigation arrows for thumbnails */}
              {allProductAssets.length > 5 && (
                <>
                  {thumbnailStartIndex > 0 && (
                    <button
                      onClick={() =>
                        setThumbnailStartIndex((prev) => Math.max(0, prev - 1))
                      }
                      className="absolute left-1 top-1/2 -translate-y-1/2 opacity-50 group-hover:opacity-100 transition-all duration-200 bg-black/70 hover:bg-black/90 text-white rounded-full p-1.5 shadow ring-1 ring-white/30 hover:scale-110"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </button>
                  )}
                  {thumbnailStartIndex + 5 < allProductAssets.length && (
                    <button
                      onClick={() =>
                        setThumbnailStartIndex((prev) =>
                          Math.min(allProductAssets.length - 5, prev + 1)
                        )
                      }
                      className="absolute right-1 top-1/2 -translate-y-1/2 opacity-50 group-hover:opacity-100 transition-all duration-200 bg-black/70 hover:bg-black/90 text-white rounded-full p-1.5 shadow ring-1 ring-white/30 hover:scale-110"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  )}
                </>
              )}

              {/* Removed numeric range indicator for cleaner UI */}
            </div>
          )}
        </div>

        {/* Right Side - Asset Grid / Drag Drop Area */}
        <div className="flex-1">
          <div
            className="w-full bg-muted/5 hover:bg-primary/5 transition-colors duration-200 group relative p-4"
            onDragOver={(e) => {
              e.preventDefault();
              e.currentTarget.classList.add("drag-active");
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.currentTarget.classList.remove("drag-active");
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.currentTarget.classList.remove("drag-active");

              // Handle image attachment for generation composition
              try {
                const dragData = e.dataTransfer.getData("application/json");
                if (dragData && onAttachImage) {
                  const parsedData = JSON.parse(dragData);
                  if (parsedData.type === "image" && parsedData.assetId) {
                    // Check if we have space (max 4 attachments)
                    if (attachedImages.length < 4) {
                      const attachedImage: AttachedImage = {
                        id: parsedData.assetId,
                        url: parsedData.url,
                        filename: parsedData.filename,
                        displayName: parsedData.displayName,
                      };
                      onAttachImage(product.id, attachedImage);
                    }
                  }
                }
              } catch (error) {
                console.error("Error handling image attachment:", error);
              }
            }}
          >
            {/* Composition Grid - 2x2 grid for attached images */}
            <div className="w-full h-full flex flex-col">
              <div className="flex-1 grid grid-cols-2 gap-3">
                {/* Show attached images and empty slots */}
                {Array.from({ length: 4 }).map((_, index) => {
                  const attachedImage = attachedImages[index];

                  if (attachedImage) {
                    return (
                      <div
                        key={`attached-${attachedImage.id}`}
                        className="aspect-square border-2 border-border rounded-lg overflow-hidden bg-background relative group"
                      >
                        <img
                          src={attachedImage.url}
                          alt={
                            attachedImage.displayName || attachedImage.filename
                          }
                          className="w-full h-full object-cover"
                        />
                        {/* Remove button */}
                        <button
                          onClick={() =>
                            onDetachImage?.(product.id, attachedImage.id)
                          }
                          className="absolute top-1 right-1 w-6 h-6 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-destructive/80"
                        >
                          ×
                        </button>
                      </div>
                    );
                  }

                  return (
                    <div
                      key={`slot-${index}`}
                      className="aspect-square border-2 border-dashed border-border/50 rounded-lg flex items-center justify-center bg-muted/20 group-[.drag-active]:border-primary/50 group-[.drag-active]:bg-primary/10 transition-colors duration-200"
                    >
                      <Plus className="w-8 h-8 text-muted-foreground/50 group-[.drag-active]:text-primary" />
                    </div>
                  );
                })}
              </div>
              <div className="mt-2 text-center text-xs text-muted-foreground">
                {attachedImages.length > 0
                  ? `${attachedImages.length}/4 images attached for generation`
                  : "Drag images from gallery to compose with product"}
              </div>
            </div>

            {/* Drag overlay - only shown when actively dragging */}
            <div className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary/30 rounded-lg opacity-0 group-[.drag-active]:opacity-100 transition-opacity duration-200 pointer-events-none">
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center text-primary">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mx-auto mb-2">
                    <Plus className="w-6 h-6" />
                  </div>
                  <p className="text-sm font-medium">Drop here to add</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Prompt Section */}
      <CardContent className="p-3 pt-1">
        <div className="space-y-2">
          {/* Rich Prompt Input */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-foreground">
                Generation Prompt
              </label>
              <div className="flex items-center gap-2">
                <div className="text-xs text-muted-foreground">
                  {(() => {
                    const prompt = prompts[product.id];
                    if (!prompt) return "0 characters";
                    // Handle both string (legacy) and PromptWithImages (new) formats
                    const text =
                      typeof prompt === "string" ? prompt : prompt.getText();
                    return `${text.length} characters`;
                  })()}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onCopyPromptToAll(product.id)}
                  className="text-xs h-7 px-2 hover:bg-primary/10 hover:text-primary-hover"
                  title="Copy prompt to all products"
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <RichPromptInput
              productId={product.id}
              productTitle={product.title}
              value={(() => {
                const prompt = prompts[product.id];
                if (!prompt) return createPromptFromText("");
                // Handle both string (legacy) and PromptWithImages (new) formats
                return typeof prompt === "string"
                  ? createPromptFromText(prompt)
                  : prompt;
              })()}
              onChange={onPromptChange}
              allProducts={allProducts}
              generatedImages={generatedImages}
            />
          </div>
        </div>
      </CardContent>

      {/* Image Lightbox */}
      <ImageLightbox
        isOpen={isLightboxOpen}
        onClose={() => setIsLightboxOpen(false)}
        assets={allProductAssets}
        currentIndex={selectedImageIndex}
        onIndexChange={setSelectedImageIndex}
        selectedAssetIds={selectedAssetIds}
        onAssetSelect={onAssetSelect}
      />
    </Card>
  );
};

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onSelectAllProducts,
  onCopyPromptToAll,
  generatedImages = {},
  availableCollections = [],
  collectionFilters = [],
  onCollectionFiltersChange,
  searchQuery = "",
  onSearchQueryChange,
  filterSelectedOnly = false,
  onFilterSelectedOnlyChange,
  filterHasGenerated = false,
  onFilterHasGeneratedChange,
  sortMode = "default",
  onSortModeChange,
  productTotalCount,
  attachedImages = {},
  onAttachImage,
  onDetachImage,
}) => {
  return (
    <div className="w-full">
      <div className="grid grid-cols-2 gap-4 p-4">
        {products.map((product) => (
          <ProductRow
            key={product.id}
            product={product}
            selectedAssetIds={selectedAssetIds}
            onAssetSelect={onAssetSelect}
            prompts={prompts}
            onPromptChange={onPromptChange}
            onTabChange={onTabChange}
            selectedProductIds={selectedProductIds}
            onProductSelectionChange={onProductSelectionChange}
            onCopyPromptToAll={onCopyPromptToAll}
            generatedImages={generatedImages}
            allAvailableAssets={[]}
            allProducts={products}
            attachedImages={attachedImages[product.id] || []}
            onAttachImage={onAttachImage}
            onDetachImage={onDetachImage}
          />
        ))}
      </div>
    </div>
  );
};
