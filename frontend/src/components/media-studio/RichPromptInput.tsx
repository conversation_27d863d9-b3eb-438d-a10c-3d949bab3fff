import React, { useState, useRef, useCallback, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Image, X, AtSign, Plus } from "lucide-react";
import type { PromptWithImages, AttachedImage } from "@/types/mediaStudio";
import type { Asset } from "@/services/mediaService";
import {
  createPromptFromText,
  parseTextWithMentions,
  insertMentionAtPosition,
  removeMention,
  assetToAttachedImage,
  populateImageData,
  generateDisplayName,
} from "@/utils/promptUtils";

interface Product {
  id: string;
  title: string;
  assets: Asset[];
}

interface RichPromptInputProps {
  productId: string;
  productTitle: string;
  value: PromptWithImages;
  onChange: (productId: string, value: PromptWithImages) => void;
  allProducts: Product[]; // All products with their assets
  generatedImages: Record<string, Asset[]>; // Generated assets by product ID
  className?: string;
}

export const RichPromptInput: React.FC<RichPromptInputProps> = ({
  productId,
  productTitle,
  value,
  onChange,
  allProducts,
  generatedImages,
  className,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showAssetPicker, setShowAssetPicker] = useState(false);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Unified state management for text and mentions
  const [internalPrompt, setInternalPrompt] = useState<PromptWithImages>(value);
  const [displayText, setDisplayText] = useState(value.getText());

  // Active mention detection state
  const [activeMention, setActiveMention] = useState<{
    start: number;
    end: number;
    query: string;
  } | null>(null);

  // Helper to get current cursor position
  const getCursorPosition = useCallback(() => {
    return textareaRef.current?.selectionStart || 0;
  }, []);

  // Phase 2: Smart mention detection
  const detectMentionContext = useCallback(
    (text: string, cursorPos: number) => {
      // Look backwards from cursor for @
      const beforeCursor = text.substring(0, cursorPos);
      const mentionMatch = beforeCursor.match(/@([a-zA-Z0-9][-a-zA-Z0-9_]*)$/);

      if (mentionMatch) {
        return {
          start: beforeCursor.length - mentionMatch[0].length,
          end: cursorPos,
          query: mentionMatch[1],
        };
      }
      return null;
    },
    []
  );

  // Sync internal state when prop value changes (from external updates)
  useEffect(() => {
    setInternalPrompt(value);
    setDisplayText(value.getText());
  }, [value]);

  // Create comprehensive list of all available assets with product context
  const availableImages = React.useMemo(() => {
    const images: AttachedImage[] = [];

    // Add original product assets
    allProducts.forEach((product) => {
      product.assets.forEach((asset) => {
        if (asset.type === "image") {
          images.push(assetToAttachedImage(asset, product.title));
        }
      });
    });

    // Add generated assets
    Object.entries(generatedImages).forEach(([productId, assets]) => {
      const product = allProducts.find((p) => p.id === productId);
      assets.forEach((asset) => {
        if (asset.type === "image") {
          const displayName = product
            ? generateDisplayName(asset, product.title) + " (Generated)"
            : generateDisplayName(asset) + " (Generated)";
          images.push({
            assetId: asset.id,
            filename: asset.filename,
            displayName,
            url: asset.url,
            productId: asset.productId,
          });
        }
      });
    });

    return images;
  }, [allProducts, generatedImages]);

  // Filter suggestions based on active mention query
  const filteredSuggestions = React.useMemo(() => {
    if (!activeMention?.query) return availableImages;

    const query = activeMention.query.toLowerCase();
    return availableImages.filter(
      (img) =>
        img.displayName?.toLowerCase().includes(query) ||
        img.filename.toLowerCase().includes(query)
    );
  }, [activeMention, availableImages]);

  // Phase 4: Visual mention highlighting
  const renderTextWithHighlights = useCallback(
    (text: string) => {
      if (!text) return "";

      const MENTION_REGEX = /@([a-zA-Z0-9][-a-zA-Z0-9_]*)/g;
      const parts: string[] = [];
      let lastIndex = 0;
      let match: RegExpExecArray | null;

      while ((match = MENTION_REGEX.exec(text)) !== null) {
        const [fullMatch, mentionName] = match;
        const start = match.index;

        // Add text before mention
        if (start > lastIndex) {
          parts.push(text.substring(lastIndex, start));
        }

        // Check if mention is valid
        const isValid = availableImages.some((img) => {
          const imgName =
            img.displayName?.toLowerCase().replace(/\s+/g, "-") || "";
          const fileName = img.filename.toLowerCase().replace(/\.[^/.]+$/, "");
          const mentionLower = mentionName.toLowerCase();

          return (
            imgName === mentionLower ||
            fileName === mentionLower ||
            imgName.includes(mentionLower) ||
            fileName.includes(mentionLower)
          );
        });

        // Add highlighted mention
        parts.push(
          `<span class="${
            isValid
              ? "text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/30"
              : "text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30"
          } px-1 rounded">${fullMatch}</span>`
        );

        lastIndex = start + fullMatch.length;
      }

      // Add remaining text
      if (lastIndex < text.length) {
        parts.push(text.substring(lastIndex));
      }

      return parts.join("");
    },
    [availableImages]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Memoize expensive image data population
  const populatedPrompt = React.useMemo(() => {
    return populateImageData(value, availableImages);
  }, [value, availableImages]);

  const attachedImages = React.useMemo(() => {
    return populatedPrompt.getAttachedImages();
  }, [populatedPrompt]);

  const promptText = React.useMemo(() => {
    return populatedPrompt.getText();
  }, [populatedPrompt]);

  const handleTextChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newText = e.target.value;
      const cursorPos = getCursorPosition();

      // Update display text immediately for responsive typing
      setDisplayText(newText);

      // Detect mention context for real-time suggestions
      const mentionContext = detectMentionContext(newText, cursorPos);
      setActiveMention(mentionContext);
      setShowAssetPicker(!!mentionContext);

      // Clear existing timeout
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      // Debounce the expensive processing and parent update
      saveTimeoutRef.current = setTimeout(() => {
        const newPrompt = parseTextWithMentions(newText, availableImages);
        setInternalPrompt(newPrompt);
        onChange(productId, newPrompt);
      }, 300);
    },
    [
      productId,
      onChange,
      availableImages,
      getCursorPosition,
      detectMentionContext,
    ]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      try {
        const jsonData = e.dataTransfer.getData("application/json");
        if (!jsonData) return;

        const dragData = JSON.parse(jsonData);
        if (dragData.type !== "image") return;

        const newImage: AttachedImage = {
          assetId: dragData.assetId,
          filename: dragData.filename,
          displayName: dragData.displayName,
          url: dragData.url,
          productId: dragData.productId,
        };

        // Check if image is already attached
        const isAlreadyAttached = attachedImages.some(
          (img) => img.assetId === newImage.assetId
        );

        if (!isAlreadyAttached) {
          // Get cursor position for insertion
          const textarea = textareaRef.current;
          let insertPosition = promptText.length; // Default to end

          if (textarea) {
            insertPosition = textarea.selectionStart;
          }

          const updatedPrompt = insertMentionAtPosition(
            populatedPrompt,
            insertPosition,
            newImage
          );
          onChange(productId, updatedPrompt);
        }
      } catch (error) {
        console.error("Failed to handle dropped image:", error);
      }
    },
    [productId, populatedPrompt, onChange, attachedImages, promptText]
  );

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setShowAssetPicker(false);
      setActiveMention(null);
    }
    // Note: @ detection is now handled in handleTextChange for real-time detection
  }, []);

  const handleAssetSelect = useCallback(
    (attachedImage: AttachedImage) => {
      if (!activeMention) return;

      // Replace the current mention query with the selected asset name
      const newText =
        displayText.substring(0, activeMention.start + 1) + // Keep @ and text before
        attachedImage.displayName?.toLowerCase().replace(/\s+/g, "-") + // Insert asset name
        displayText.substring(activeMention.end); // Keep text after

      setDisplayText(newText);

      // Update internal state
      const newPrompt = parseTextWithMentions(newText, availableImages);
      setInternalPrompt(newPrompt);
      onChange(productId, newPrompt);

      setShowAssetPicker(false);
      setActiveMention(null);
    },
    [activeMention, displayText, availableImages, productId, onChange]
  );

  const removeAttachedImage = useCallback(
    (assetId: string) => {
      const updatedPrompt = removeMention(populatedPrompt, assetId);
      onChange(productId, updatedPrompt);
    },
    [productId, populatedPrompt, onChange]
  );

  return (
    <div className={cn("space-y-3", className)}>
      {/* Attached Assets Display */}
      {attachedImages.length > 0 && (
        <div className="space-y-2">
          <div className="text-xs font-medium text-gray-600 dark:text-gray-400 flex items-center gap-1">
            <Image className="h-3 w-3" />
            Referenced Assets
          </div>
          <div className="flex flex-wrap gap-2">
            {attachedImages.map((image) => (
              <div
                key={image.assetId}
                className="inline-flex items-center gap-2 px-3 py-1.5 bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-lg border border-blue-200 dark:border-blue-800"
              >
                <div className="w-4 h-4 bg-blue-100 dark:bg-blue-800 rounded flex items-center justify-center flex-shrink-0">
                  <Image className="h-2.5 w-2.5 text-blue-600 dark:text-blue-300" />
                </div>
                <span className="font-medium truncate max-w-24">
                  {image.displayName}
                </span>
                <button
                  onClick={() => removeAttachedImage(image.assetId)}
                  className="ml-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5 transition-colors"
                  title="Remove reference"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Prompt Input */}
      <div className="relative">
        {/* Highlight overlay */}
        <div
          className="absolute inset-0 p-3 text-sm pointer-events-none whitespace-pre-wrap break-words overflow-hidden rounded-md"
          style={{
            fontFamily: "inherit",
            fontSize: "inherit",
            lineHeight: "inherit",
            color: "transparent",
          }}
          dangerouslySetInnerHTML={{
            __html: renderTextWithHighlights(displayText),
          }}
        />

        <textarea
          ref={textareaRef}
          value={displayText}
          onChange={handleTextChange}
          onKeyDown={handleKeyDown}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          placeholder={`e.g., A model wearing the ${productTitle.toLowerCase()} in a sunlit park...`}
          className={cn(
            // Align textarea styling with Input defaults for a cohesive look (no drop shadow)
            "w-full min-h-[100px] p-3 text-sm rounded-md border border-input bg-transparent text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary/50 focus:bg-background resize-none transition-all relative z-10",
            isDragOver &&
              "ring-2 ring-primary bg-primary/5 dark:bg-primary/10 border-primary/30"
          )}
          style={{ background: "transparent" }}
        />

        {/* Drag overlay */}
        {isDragOver && (
          <div className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-lg flex items-center justify-center">
            <div className="text-center text-blue-600 dark:text-blue-400">
              <Plus className="h-6 w-6 mx-auto mb-1" />
              <p className="text-sm font-medium">Drop to reference in prompt</p>
            </div>
          </div>
        )}

        {/* Asset Picker Dropdown */}
        {showAssetPicker && (
          <div className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto">
            <div className="p-3">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 flex items-center gap-1">
                <AtSign className="h-3 w-3" />
                Select asset to reference
              </div>
              {filteredSuggestions.length === 0 ? (
                <div className="text-xs text-gray-400 py-2">
                  {activeMention?.query
                    ? `No assets matching "${activeMention.query}"`
                    : "No assets available"}
                </div>
              ) : (
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {filteredSuggestions.slice(0, 12).map((attachedImage) => (
                    <button
                      key={attachedImage.assetId}
                      onClick={() => handleAssetSelect(attachedImage)}
                      className="w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-xs transition-colors"
                    >
                      <div className="w-6 h-6 bg-gray-100 dark:bg-gray-600 rounded flex items-center justify-center flex-shrink-0">
                        <Image className="h-3 w-3 text-gray-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {attachedImage.displayName}
                        </div>
                        <div className="text-gray-400 text-xs truncate">
                          {attachedImage.filename}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
