import React from "react";
import { Asset } from "@/services/assetService";
import { Thumbnail } from "./Thumbnail";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// Removed Card wrapper to match flat reference design
import { RefreshCw, Search, X, ArrowUpDown } from "lucide-react";

interface PreviewPaneProps {
  assets: Asset[];
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
}

export const PreviewPane: React.FC<PreviewPaneProps> = ({
  assets = [],
  loading = false,
  error = null,
  onRefresh,
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
}) => {
  const [typeFilter, setTypeFilter] = React.useState<"all" | "image" | "video">(
    "all"
  );
  const [sourceFilter, setSourceFilter] = React.useState<
    "all" | "generated" | "product"
  >("all");
  const [sortBy, setSortBy] = React.useState<
    "newest" | "oldest" | "name" | "type"
  >("newest");
  const [promptQuery, setPromptQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState<string>("");

  // Debounce query to reduce recompute churn
  React.useEffect(() => {
    const t = window.setTimeout(
      () => setDebouncedQuery(promptQuery.trim().toLowerCase()),
      200
    );
    return () => window.clearTimeout(t);
  }, [promptQuery]);

  // Intersection observer for infinite scroll
  const sentinelRef = React.useRef<HTMLDivElement | null>(null);
  React.useEffect(() => {
    if (!sentinelRef.current || !hasMore || isLoadingMore || !onLoadMore)
      return;

    const el = sentinelRef.current;
    const io = new IntersectionObserver(
      (entries) => {
        const e = entries[0];
        if (e && e.isIntersecting) {
          // Trigger load-more when sentinel enters view
          onLoadMore();
        }
      },
      { root: null, rootMargin: "200px 0px 0px 0px", threshold: 0 }
    );
    io.observe(el);
    return () => io.disconnect();
  }, [hasMore, isLoadingMore, onLoadMore]);

  const filteredAssets = React.useMemo(() => {
    const q = debouncedQuery;
    let filtered = assets.filter((a) => {
      const typeOk = typeFilter === "all" || a.type === typeFilter;
      const sourceOk =
        sourceFilter === "all" ||
        (sourceFilter === "generated" && a.sourceType === "ai_generated") ||
        (sourceFilter === "product" && a.sourceType === "product");
      const text = `${a.prompt || ""} ${a.filename || ""}`.toLowerCase();
      const queryOk = q === "" || text.includes(q);
      return typeOk && sourceOk && queryOk;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.id).getTime() - new Date(a.id).getTime(); // Assuming ID contains timestamp
        case "oldest":
          return new Date(a.id).getTime() - new Date(b.id).getTime();
        case "name":
          return (a.displayName || a.filename).localeCompare(
            b.displayName || b.filename
          );
        case "type":
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });

    return filtered;
  }, [assets, typeFilter, sourceFilter, sortBy, debouncedQuery]);

  const onSelect = React.useCallback(() => {}, []);

  const clearFilters = React.useCallback(() => {
    setTypeFilter("all");
    setSourceFilter("all");
    setSortBy("newest");
    setPromptQuery("");
  }, []);

  const hasActiveFilters =
    typeFilter !== "all" ||
    sourceFilter !== "all" ||
    sortBy !== "newest" ||
    promptQuery.trim() !== "";

  return (
    <div className="w-full h-full flex flex-col p-4 lg:p-6">
      {loading ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground text-sm">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading…
        </div>
      ) : error ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-amber-700 text-sm">
          {error}
        </div>
      ) : assets.length === 0 ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground text-sm">
          No generated assets yet.
        </div>
      ) : (
        <>
          {/* Filters toolbar - Two rows */}
          <div className="mb-3 space-y-2">
            {/* Row 1: Filter dropdowns */}
            <div className="flex items-center gap-2 flex-wrap">
              {/* Type Filter */}
              <Select
                value={typeFilter}
                onValueChange={(value: "all" | "image" | "video") =>
                  setTypeFilter(value)
                }
              >
                <SelectTrigger className="w-28 h-7 text-xs rounded-full [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="image">Images</SelectItem>
                  <SelectItem value="video">Videos</SelectItem>
                </SelectContent>
              </Select>

              {/* Source Filter */}
              <Select
                value={sourceFilter}
                onValueChange={(value: "all" | "generated" | "product") =>
                  setSourceFilter(value)
                }
              >
                <SelectTrigger className="w-32 h-7 text-xs rounded-full [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  <SelectItem value="generated">Generated</SelectItem>
                  <SelectItem value="product">Product</SelectItem>
                </SelectContent>
              </Select>

              {/* Sort Options */}
              <Select
                value={sortBy}
                onValueChange={(value: "newest" | "oldest" | "name" | "type") =>
                  setSortBy(value)
                }
              >
                <SelectTrigger className="w-24 h-7 text-xs rounded-full [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest</SelectItem>
                  <SelectItem value="oldest">Oldest</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="type">Type</SelectItem>
                </SelectContent>
              </Select>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="h-7 px-2 text-xs rounded-full hover:bg-muted/50 focus:ring-0 focus:ring-offset-0"
                  title="Clear all filters"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear
                </Button>
              )}
            </div>

            {/* Row 2: Search + Item count */}
            <div className="flex items-center gap-2">
              {/* Enhanced Search */}
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search by prompt or filename..."
                  value={promptQuery}
                  onChange={(e) => setPromptQuery(e.target.value)}
                  className="pl-10 h-7 text-sm"
                />
                {promptQuery && (
                  <button
                    onClick={() => setPromptQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>

              {/* Item count */}
              <span className="inline-flex items-center rounded-full bg-muted text-muted-foreground px-2.5 py-1 text-xs font-medium">
                {filteredAssets.length} items
              </span>
            </div>
          </div>

          {/* Simple Grid */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden min-h-0">
            <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pb-10">
              {filteredAssets.map((asset) => (
                <div key={asset.id} className="w-full aspect-square">
                  <Thumbnail
                    asset={asset}
                    isSelected={false}
                    onSelect={onSelect}
                    isGenerated={asset.sourceType === "ai_generated"}
                    showBorder={false}
                    draggable={true}
                  />
                </div>
              ))}
            </div>

            {/* Bottom sentinel for infinite scroll */}
            {hasMore && <div ref={sentinelRef} className="w-full h-10" />}

            {/* Loading more indicator */}
            {isLoadingMore && (
              <div className="absolute bottom-2 left-1/2 -translate-x-1/2 text-xs text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin inline mr-1" />
                Loading more…
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};
