import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface CollectionsFilterProps {
  options: Array<{ id: string; name: string; color: string }>;
  selectedIds: string[];
  onChange?: (selectedIds: string[]) => void;
  compact?: boolean;
}

export const CollectionsFilter: React.FC<CollectionsFilterProps> = ({
  options,
  selectedIds,
  onChange,
  compact = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const selectedOptions = (options || []).filter(option => selectedIds.includes(option.id));

  const handleToggleOption = (optionId: string) => {
    if (!onChange) return;

    const currentSelectedIds = selectedIds || [];
    const newSelectedIds = currentSelectedIds.includes(optionId)
      ? currentSelectedIds.filter(id => id !== optionId)
      : [...currentSelectedIds, optionId];

    onChange(newSelectedIds);
  };

  const handleRemoveOption = (optionId: string) => {
    if (!onChange) return;
    onChange((selectedIds || []).filter(id => id !== optionId));
  };

  const handleClearAll = () => {
    if (!onChange) return;
    onChange([]);
  };

  if (compact) {
    return (
      <div className="relative">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "px-2.5 py-1 text-xs rounded-full h-7 border",
            selectedIds.length > 0
              ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
              : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
          )}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
        >
          {`Collections${(selectedIds || []).length > 0 ? ` (${selectedIds.length})` : ''}`}
          <ChevronDown className="ml-1 h-3 w-3" />
        </Button>

        {isOpen && (
          <div className="absolute right-0 mt-1 w-64 z-20 rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg p-3">
            <div className="space-y-3">
              <div className="text-[11px] uppercase tracking-wide text-gray-500 dark:text-gray-400">
                Collections
              </div>

              <div className="max-h-48 overflow-y-auto space-y-2">
                {(options || []).map((option) => (
                  <label key={option.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={(selectedIds || []).includes(option.id)}
                      onChange={() => handleToggleOption(option.id)}
                      className="h-3.5 w-3.5"
                    />
                    <span className="text-sm text-gray-800 dark:text-gray-100">
                      {option.name}
                    </span>
                  </label>
                ))}
              </div>

              {(selectedIds || []).length > 0 && (
                <div className="flex items-center justify-between gap-2 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="button"
                    className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    onClick={handleClearAll}
                  >
                    Clear
                  </button>
                  <button
                    type="button"
                    className="text-xs text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100"
                    onClick={() => setIsOpen(false)}
                  >
                    Done
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Collections
        </label>
        {(selectedIds || []).length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAll}
            className="h-6 px-2 text-xs"
          >
            Clear all
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {selectedOptions.map((option) => (
          <Badge
            key={option.id}
            variant="secondary"
            className="flex items-center gap-1"
          >
            {option.name}
            <button
              type="button"
              onClick={() => handleRemoveOption(option.id)}
              className="ml-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <div className="grid grid-cols-2 gap-2">
        {(options || []).map((option) => (
          <label key={option.id} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={(selectedIds || []).includes(option.id)}
              onChange={() => handleToggleOption(option.id)}
              className="h-4 w-4"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {option.name}
            </span>
          </label>
        ))}
      </div>
    </div>
  );
};
