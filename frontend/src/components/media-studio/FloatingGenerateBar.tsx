import React from "react";
import { MODELS } from "./constants";
import type {
  GenerationMode,
  ImageSettings,
  VideoSettings,
} from "@/types/mediaStudio";
import { ModeToggle } from "./ModeToggle";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SparklesIcon, PlusIcon } from "./icons";
import {
  ModelSelect,
  BrandGIcon,
  FluxIcon,
  WanIcon,
  MultiRefIcon,
  OpenAIIcon,
} from "./ModelSelect";

interface FloatingGenerateBarProps {
  selectedCount: number;
  generationMode: GenerationMode;
  onModeChange: (mode: GenerationMode) => void;
  settings: ImageSettings | VideoSettings;
  setSettings: React.Dispatch<
    React.SetStateAction<ImageSettings | VideoSettings>
  >;
  selectedModelId: string;
  onModelChange: (modelId: string) => void;
  onGenerate?: () => void;
  generationBatch?: {
    batchId: string;
    total: number;
    completed: number;
    failed: number;
    status: string;
  } | null;
  // New props for UX behavior
  isInitiating?: boolean;
  initiationMessage?: string | null;
  overLimit?: boolean;
  overLimitMessage?: string;
  errorMessage?: string | null;
  onDismissMessage?: () => void;
  onDismissInitiationMessage?: () => void;
  // Small dev aid: show the latest request id so it is copyable without DevTools
  lastRequestId?: string | null;
}

export const FloatingGenerateBar: React.FC<FloatingGenerateBarProps> = ({
  selectedCount,
  generationMode,
  onModeChange,
  settings,
  setSettings,
  selectedModelId,
  onModelChange,
  onGenerate,
  generationBatch,
  isInitiating = false,
  initiationMessage,
  overLimit = false,
  overLimitMessage = "Select up to 5 products.",
  errorMessage,
  onDismissMessage,
  onDismissInitiationMessage,
  lastRequestId,
}) => {
  const [topPrompt, setTopPrompt] = React.useState<string>("");
  const selectorsRef = React.useRef<HTMLDivElement | null>(null);
  const [promptWidth, setPromptWidth] = React.useState<number | null>(null);

  React.useEffect(() => {
    const measure = () => {
      const el = selectorsRef.current;
      if (!el) return;
      const rect = el.getBoundingClientRect();
      const w = Math.max(el.scrollWidth || 0, rect?.width || 0);
      if (w) setPromptWidth(Math.max(320, Math.round(w)));
    };
    measure();
    const onResize = () => measure();
    window.addEventListener("resize", onResize);
    const id = window.setTimeout(measure, 0);
    // Observe selectors row width changes (e.g., when switching video/image)
    let ro: ResizeObserver | null = null;
    try {
      if (typeof ResizeObserver !== "undefined" && selectorsRef.current) {
        ro = new ResizeObserver(() => measure());
        ro.observe(selectorsRef.current);
      }
    } catch {}
    return () => {
      window.removeEventListener("resize", onResize);
      window.clearTimeout(id);
      try {
        if (ro && selectorsRef.current) ro.disconnect();
      } catch {}
    };
  }, [
    generationMode,
    selectedModelId,
    (settings as any)?.aspectRatio,
    (settings as any)?.resolution,
    (settings as any)?.quality,
  ]);
  const noSelection = selectedCount === 0;
  const isGenerateDisabled = noSelection || overLimit || isInitiating;
  // Video-specific model list (override when generationMode === 'video')
  const VIDEO_MODELS: {
    value: string;
    label: string;
    eta?: string;
    cost?: number;
  }[] = [
    { value: "veo-2.0-generate-001", label: "Veo 2", eta: "~20s", cost: 8 },
    {
      value: "veo-3.0-fast-generate-preview",
      label: "Veo 3 Fast (Preview)",
      eta: "~10s",
      cost: 10,
    },
    {
      value: "veo-3.0-generate-preview",
      label: "Veo 3 (Preview)",
      eta: "~20s",
      cost: 12,
    },
  ];


  const selectedModel =
    generationMode === "video"
      ? VIDEO_MODELS.find((m) => m.value === selectedModelId) || null
      : MODELS.find((m) => m.id === selectedModelId) || null;
  const eta =
    selectedModel && (selectedModel as any).eta
      ? (selectedModel as any).eta
      : "~?s";
  // Derive total time from per-asset ETA
  const parseSeconds = (s: string): number | null => {
    if (!s) return null;
    const m = String(s).match(/(\d+)/);
    if (!m) return null;
    const n = parseInt(m[1], 10);
    return isFinite(n) ? n : null;
  };
  const etaSeconds = parseSeconds(eta);
  const totalSeconds = etaSeconds != null ? etaSeconds * selectedCount : null;
  const costPerAsset =
    selectedModel && (selectedModel as any).cost !== undefined
      ? ((selectedModel as any).cost as number)
      : 0;
  const totalCost = selectedCount * costPerAsset;

  const handleSettingsChange = (
    field: keyof (ImageSettings | VideoSettings),
    value: string | number
  ) => {
    setSettings((prev: ImageSettings | VideoSettings) => ({
      ...prev,
      [field]: value,
    }));
  };

  const quality = "quality" in settings ? settings.quality : "Standard";
  // Image models list (curated). Only Nano Banana is selectable for now; others are disabled as "Coming soon".
  const IMAGE_MODELS = [
    {
      value: "gemini-2.5-flash-image-preview",
      title: "Nano Banana",
      subtitle: "Google's advanced image editing model",
      badge: "Premium",
      disabled: false,
      icon: <BrandGIcon className="h-5 w-5" />,
    },
    {
      value: "flux-kontext-max",
      title: "Flux Kontext Max",
      subtitle: "Edit with accuracy",
      badge: "Premium",
      disabled: true,
      comingSoon: true,
      icon: <FluxIcon className="h-5 w-5" />,
    },
    {
      value: "dall-e-3",
      title: "GPT Image",
      subtitle: "OpenAI's powerful image tool",
      badge: "Premium",
      disabled: true,
      comingSoon: true,
      icon: <OpenAIIcon className="h-5 w-5" />,
    },
    {
      value: "midjourney-v6",
      title: "Multi-Reference",
      subtitle: "Multiple edits in one shot",
      badge: "Premium",
      disabled: true,
      comingSoon: true,
      icon: <MultiRefIcon className="h-5 w-5" />,
    },
    {
      value: "wan-2.2",
      title: "WAN 2.2",
      subtitle: "High-fidelity cinematic visuals",
      badge: undefined,
      disabled: true,
      comingSoon: true,
      icon: <WanIcon className="h-5 w-5" />,
    },
  ];

  const modelOptions =
    generationMode === "video"
      ? VIDEO_MODELS
      : IMAGE_MODELS;

  const aspectRatioOptions = (
    generationMode === "image"
      ? ["1:1", "16:9", "9:16", "4:3", "3:4"]
      : ["16:9", "9:16"]
  ).map((ratio) => ({ value: ratio, label: ratio }));

  // Ensure a valid video model is selected when switching to video mode
  React.useEffect(() => {
    if (generationMode !== "video") return;
    const valid = VIDEO_MODELS.some((m) => m.value === selectedModelId);
    if (!valid && VIDEO_MODELS.length > 0) {
      onModelChange(VIDEO_MODELS[0].value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generationMode]);

  // Ensure a valid image model is selected when switching to image mode
  React.useEffect(() => {
    if (generationMode !== "image") return;
    const valid = IMAGE_MODELS.some(
      (m) => m.value === selectedModelId && !m.disabled
    );
    if (!valid && IMAGE_MODELS.length > 0) {
      const firstEnabled = IMAGE_MODELS.find((m) => !m.disabled);
      if (firstEnabled) onModelChange(firstEnabled.value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generationMode]);

  // Model constraints
  const isVeo2 =
    generationMode === "video" && selectedModelId.startsWith("veo-2.0");
  const isVeo3 =
    generationMode === "video" && selectedModelId.startsWith("veo-3.0");

  // Resolution options computed from model + aspect ratio
  const ar = (settings as any).aspectRatio as string;
  const currentResolution: string = (settings as any).resolution || "720p";
  const resolutionOptions = React.useMemo(() => {
    if (generationMode !== "video")
      return [] as { value: string; label: string }[];
    if (isVeo2) return ["720p"].map((r) => ({ value: r, label: r }));
    // Veo 3 / 3 Fast
    if (ar === "9:16") return ["720p"].map((r) => ({ value: r, label: r }));
    return ["720p", "1080p"].map((r) => ({ value: r, label: r }));
  }, [generationMode, isVeo2, ar]);

  // Coerce invalid resolution when constraints change
  React.useEffect(() => {
    if (generationMode !== "video") return;
    const allowed = resolutionOptions.map((o) => o.value);
    if (!allowed.includes(currentResolution) && allowed.length > 0) {
      handleSettingsChange("resolution" as any, allowed[0]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    generationMode,
    resolutionOptions.map((o) => o.value).join("|"),
    currentResolution,
    isVeo2,
    ar,
  ]);

  // Duration options for video
  const durationOptions = React.useMemo(
    () =>
      generationMode === "video"
        ? [
            { value: "5", label: "5s" },
            { value: "8", label: "8s" },
          ]
        : [],
    [generationMode]
  );
  const currentDuration = String((settings as any).duration ?? "8");
  React.useEffect(() => {
    if (generationMode !== "video") return;
    const allowed = durationOptions.map((o) => o.value);
    if (!allowed.includes(currentDuration) && allowed.length > 0) {
      handleSettingsChange("duration" as any, Number(allowed[0]));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    generationMode,
    currentDuration,
    durationOptions.map((o) => o.value).join("|"),
  ]);

  const qualityOptions = [
    { value: "Standard", label: "Standard" },
    { value: "High", label: "High" },
  ];

  const handleGenerate = () => {
    if (onGenerate && !isGenerateDisabled) {
      onGenerate();
    }
  };

  // Progress display
  // Multi-batch aggregation text is supplied by parent via generationBatch
  const progressText = generationBatch
    ? `${generationBatch.completed}/${generationBatch.total} completed${
        generationBatch.failed ? ` • ${generationBatch.failed} failed` : ""
      }`
    : "";

  const isProcessing =
    !!generationBatch &&
    generationBatch.completed < (generationBatch.total || 0);
  const isCompleted =
    !!generationBatch &&
    generationBatch.total > 0 &&
    generationBatch.completed >= generationBatch.total;

  // Toast notifications (stacked, auto-dismiss)
  type ToastType = "error" | "success" | "info" | "warn";
  type Toast = { id: number; type: ToastType; text: string; createdAt: number };
  const [toasts, setToasts] = React.useState<Toast[]>([]);
  const currentErrorToastId = React.useRef<number | null>(null);

  const addToast = React.useCallback(
    (type: ToastType, text: string, ms: number): number => {
      const id = Date.now() + Math.floor(Math.random() * 1000);
      const createdAt = Date.now();
      setToasts((prev) => [{ id, type, text, createdAt }, ...prev]);
      window.setTimeout(() => {
        setToasts((prev) => prev.filter((t) => t.id !== id));
        if (type === "error" && currentErrorToastId.current === id) {
          onDismissMessage && onDismissMessage();
        }
      }, ms);
      return id;
    },
    [onDismissMessage]
  );

  // Track changes to incoming props and convert to toasts
  const prevError = React.useRef<string | null>(null);
  React.useEffect(() => {
    if (errorMessage && prevError.current !== errorMessage) {
      const id = addToast("error", errorMessage, 8000);
      currentErrorToastId.current = id;
    }
    prevError.current = errorMessage || null;
  }, [errorMessage, addToast]);

  const prevInitMsg = React.useRef<string | null>(null);
  React.useEffect(() => {
    if (initiationMessage && prevInitMsg.current !== initiationMessage) {
      addToast("info", initiationMessage, 5000);
    }
    prevInitMsg.current = initiationMessage || null;
  }, [initiationMessage, addToast]);

  const prevOverLimit = React.useRef<boolean>(false);
  React.useEffect(() => {
    if (overLimit && !prevOverLimit.current && overLimitMessage) {
      addToast("warn", overLimitMessage, 4000);
    }
    prevOverLimit.current = !!overLimit;
  }, [overLimit, overLimitMessage, addToast]);

  const completionText = generationBatch
    ? `${generationBatch.completed}/${generationBatch.total} completed`
    : "";
  const completedToastGuard = React.useRef<number>(0);
  React.useEffect(() => {
    if (isCompleted) {
      const now = Date.now();
      if (now - completedToastGuard.current > 500) {
        addToast("success", `Generation finished. ${completionText}.`, 6000);
        completedToastGuard.current = now;
      }
    }
  }, [isCompleted, completionText, addToast]);

  return (
    <div className="fixed bottom-2 left-1/2 -translate-x-1/2 z-50">
      <Card className="backdrop-blur-sm shadow-2xl min-w-[720px]">
        <CardContent className="flex flex-col gap-3 p-3">
          {/* Toast stack: most recent on top */}
          {toasts.length > 0 && (
            <div className="w-full flex flex-col gap-2">
              {toasts
                .slice()
                .sort((a, b) => b.createdAt - a.createdAt)
                .map((t) => {
                  const base =
                    t.type === "error"
                      ? "bg-destructive/10 text-destructive border border-destructive/20"
                      : t.type === "success"
                        ? "bg-green-500/10 text-green-700 border border-green-500/20"
                        : t.type === "warn"
                          ? "bg-amber-500/10 text-amber-700 border border-amber-500/20"
                          : "bg-primary/10 text-primary border border-primary/20";
                  return (
                    <div
                      key={t.id}
                      className={`w-full flex items-start justify-between gap-3 px-3 py-2 rounded-lg ${base}`}
                    >
                      <span className="text-sm whitespace-normal break-words">
                        {t.text}
                      </span>
                      <button
                        className="ml-2 opacity-70 hover:opacity-100"
                        onClick={() => {
                          setToasts((prev) =>
                            prev.filter((x) => x.id !== t.id)
                          );
                          if (
                            t.type === "error" &&
                            currentErrorToastId.current === t.id
                          ) {
                            onDismissMessage && onDismissMessage();
                          }
                        }}
                        aria-label="Dismiss notification"
                      >
                        ✕
                      </button>
                    </div>
                  );
                })}
            </div>
          )}
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            {/* Left side: Progress + Info text (left-aligned) */}
            <div className="flex items-center gap-3">
              {generationBatch && (
                <>
                  {errorMessage ? (
                    <div
                      className="rounded-full h-4 w-4 bg-red-500 flex items-center justify-center"
                      title="Error"
                      aria-label="Error"
                    >
                      <svg
                        className="w-2.5 h-2.5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm-1.414-5.414a1 1 0 011.414 0L10 12.586l.707-.707a1 1 0 111.414 1.414L11.414 14l.707.707a1 1 0 01-1.414 1.414L10 15.414l-.707.707a1 1 0 01-1.414-1.414L8.586 14l-.707-.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  ) : (
                    <>
                      {isProcessing && (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                      )}
                      {isCompleted && (
                        <div className="rounded-full h-4 w-4 bg-green-500 flex items-center justify-center">
                          <svg
                            className="w-2.5 h-2.5 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                    </>
                  )}
                  {/* Progress text removed per request (icon only) */}
                </>
              )}
              {lastRequestId && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(lastRequestId)}
                  title={lastRequestId}
                  className="px-2 py-[2px] h-6 text-xs"
                >
                  id: {lastRequestId.slice(0, 8)}
                </Button>
              )}
              {/* (Prompt moved below, spanning selectors) */}
            </div>

            {/* (Right side empty; toggle is moved above Generate button below) */}
          </div>

          <div className="flex items-center">
            <div className="ml-auto flex items-stretch gap-3">
              {/* Prompt above selectors, aligned to selectors width */}
              <div
                className="flex flex-col justify-between h-full gap-3"
                style={{ width: promptWidth || undefined }}
              >
                <div className="relative flex-1 min-h-[48px]">
                  <textarea
                    value={topPrompt}
                    onChange={(e) => setTopPrompt(e.target.value)}
                    placeholder="Describe what to create…"
                    className="w-full h-full min-h-[48px] p-2 pl-12 text-sm bg-muted rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-background resize-none placeholder:text-muted-foreground transition-colors border border-border"
                  />
                  <button
                    type="button"
                    className="absolute left-2 top-1/2 -translate-y-1/2 inline-flex items-center justify-center h-7 w-7 rounded-md border border-gray-300/70 dark:border-gray-600/70 bg-gray-200/90 dark:bg-gray-700/80 text-gray-600 dark:text-gray-300 hover:bg-gray-300/90 dark:hover:bg-gray-600/80"
                    title="Add"
                    aria-label="Add"
                  >
                    <PlusIcon className="h-4 w-4" />
                  </button>
                </div>
                <div
                  ref={selectorsRef}
                  className="grid auto-cols-max grid-flow-col gap-3 items-end"
                >
                  {/* Selectors row */}
                  <Select
                    value={settings.aspectRatio}
                    onValueChange={(value: string) => {
                      handleSettingsChange("aspectRatio", value);
                      if (generationMode === "video") {
                        // If switching to 9:16 on Veo 3/2, enforce 720p
                        if ((isVeo3 || isVeo2) && value === "9:16") {
                          handleSettingsChange("resolution" as any, "720p");
                        }
                      }
                    }}
                  >
                    <SelectTrigger
                      size="sm"
                      className="w-32 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                    >
                      <SelectValue placeholder="Select aspect ratio" />
                    </SelectTrigger>
                    <SelectContent>
                      {aspectRatioOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {generationMode === "video" && (
                    <Select
                      value={currentResolution}
                      onValueChange={(value: string) =>
                        handleSettingsChange("resolution" as any, value)
                      }
                    >
                      <SelectTrigger
                        size="sm"
                        className="w-28 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                      >
                        <SelectValue placeholder="Select resolution" />
                      </SelectTrigger>
                      <SelectContent>
                        {resolutionOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  {generationMode === "image" && (
                    <Select
                      value={quality}
                      onValueChange={(value: string) =>
                        handleSettingsChange("quality", value)
                      }
                    >
                      <SelectTrigger
                        size="sm"
                        className="w-32 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                      >
                        <SelectValue placeholder="Select quality" />
                      </SelectTrigger>
                      <SelectContent>
                        {qualityOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  {generationMode === "image" ? (
                    <ModelSelect
                      label="AI Model"
                      value={selectedModelId}
                      onChange={(val: string) => onModelChange(val)}
                      options={IMAGE_MODELS as any}
                      dense
                      className="w-36"
                    />
                  ) : generationMode === "video" ? (
                    <Select
                      value={selectedModelId}
                      onValueChange={(val: string) => {
                        onModelChange(val);
                        // Video-specific resolution correction
                        const nextIsVeo2 = val.startsWith("veo-2.0");
                        const arNow = (settings as any).aspectRatio as string;
                        const resAllowed = nextIsVeo2
                          ? ["720p"]
                          : arNow === "9:16"
                            ? ["720p"]
                            : ["720p", "1080p"];
                        if (!resAllowed.includes(currentResolution)) {
                          handleSettingsChange(
                            "resolution" as any,
                            resAllowed[0]
                          );
                        }
                      }}
                    >
                      <SelectTrigger
                        size="sm"
                        className="w-36 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                      >
                        <SelectValue placeholder="Select AI model" />
                      </SelectTrigger>
                      <SelectContent>
                        {modelOptions.map((option: any) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : null}
                  {generationMode === "video" && (
                    <Select
                      value={currentDuration}
                      onValueChange={(value: string) =>
                        handleSettingsChange("duration" as any, Number(value))
                      }
                    >
                      <SelectTrigger
                        size="sm"
                        className="w-20 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                      >
                        <SelectValue placeholder="Select duration" />
                      </SelectTrigger>
                      <SelectContent>
                        {durationOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
              {/* (Notifications moved to top banner) */}

              {/* Right column: mode toggle above Generate button */}
              <div className="flex flex-col items-end gap-2 h-full justify-end">
                <div className="w-[240px] flex justify-end">
                  <ModeToggle
                    mode={generationMode}
                    onChange={onModeChange}
                    orientation="horizontal"
                    fullWidth
                  />
                </div>
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerateDisabled}
                  className="h-16 min-w-[240px] px-6 text-base font-semibold"
                  size="lg"
                >
                  {isInitiating
                    ? "Starting…"
                    : `Generate ${selectedCount > 0 ? `(${selectedCount})` : ""}`}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
