import React, { useCallback, useEffect } from "react";
import { Asset } from "@/services/assetService";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronLeft, ChevronRight, X } from "lucide-react";

interface ImageLightboxProps {
  isOpen: boolean;
  onClose: () => void;
  assets: Asset[];
  currentIndex: number;
  onIndexChange: (index: number) => void;
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
}

export const ImageLightbox: React.FC<ImageLightboxProps> = ({
  isOpen,
  onClose,
  assets,
  currentIndex,
  onIndexChange,
  selectedAssetIds,
  onAssetSelect,
}) => {
  const currentAsset = assets[currentIndex];

  // Keyboard navigation
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          onIndexChange(
            currentIndex > 0 ? currentIndex - 1 : assets.length - 1
          );
          break;
        case "ArrowRight":
          e.preventDefault();
          onIndexChange(
            currentIndex < assets.length - 1 ? currentIndex + 1 : 0
          );
          break;
        case "Home":
          e.preventDefault();
          onIndexChange(0);
          break;
        case "End":
          e.preventDefault();
          onIndexChange(assets.length - 1);
          break;
        case "Escape":
          e.preventDefault();
          onClose();
          break;
      }
    },
    [isOpen, currentIndex, assets.length, onIndexChange, onClose]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  const handlePrevious = () => {
    onIndexChange(currentIndex > 0 ? currentIndex - 1 : assets.length - 1);
  };

  const handleNext = () => {
    onIndexChange(currentIndex < assets.length - 1 ? currentIndex + 1 : 0);
  };

  const handleSelect = () => {
    if (currentAsset) {
      onAssetSelect(currentAsset, false);
    }
  };

  if (!currentAsset) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <DialogPrimitive.Content className="fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 w-[min(1200px,95vw)] h-[min(85vh,95vh)] bg-card border border-[#EDEDED] dark:border-border rounded-lg shadow-xl p-0 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0 data-[state=open]:zoom-in-95 data-[state=closed]:zoom-out-95 duration-300 ease-linear">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close button */}
            <button
              onClick={onClose}
              aria-label="Close"
              className="absolute top-3 right-3 z-50 h-10 w-10 inline-flex items-center justify-center rounded-md text-foreground/80 hover:text-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40"
            >
              <X className="h-5 w-5" />
            </button>

            {/* Navigation arrows */}
            {assets.length > 1 && (
              <>
                <button
                  onClick={handlePrevious}
                  aria-label="Previous"
                  className="absolute left-3 top-1/2 -translate-y-1/2 z-50 h-10 w-10 inline-flex items-center justify-center rounded-md text-foreground/80 hover:text-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                <button
                  onClick={handleNext}
                  aria-label="Next"
                  className="absolute right-3 top-1/2 -translate-y-1/2 z-50 h-10 w-10 inline-flex items-center justify-center rounded-md text-foreground/80 hover:text-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40"
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </>
            )}

            {/* Top controls */}
            <div className="absolute top-3 left-3 z-50 flex items-center gap-2">
              {/* Selection checkbox */}
              <div className="flex items-center gap-2 bg-card/95 border border-[#EDEDED] dark:border-border rounded-md px-2 py-1 shadow-sm">
                <Checkbox
                  checked={selectedAssetIds.has(currentAsset.id)}
                  onCheckedChange={handleSelect}
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                <span className="text-xs text-foreground">
                  {selectedAssetIds.has(currentAsset.id)
                    ? "Selected"
                    : "Select for generation"}
                </span>
              </div>

              {/* Asset info */}
              <div className="bg-card/95 border border-[#EDEDED] dark:border-border rounded-md px-2 py-1 text-xs text-foreground shadow-sm">
                {currentIndex + 1} of {assets.length}
              </div>
            </div>

            {/* Main image */}
            <div className="w-full h-full flex items-center justify-center p-4 sm:p-6">
              <img
                src={currentAsset.url}
                alt={currentAsset.displayName || currentAsset.filename}
                className="max-w-full max-h-[calc(85vh-96px)] object-contain"
              />
            </div>

            {/* Bottom thumbnail strip */}
            {assets.length > 1 && (
              <div className="absolute bottom-3 left-1/2 -translate-x-1/2 z-50">
                <div className="flex gap-2 bg-card/95 border border-[#EDEDED] dark:border-border rounded-md p-2 max-w-[80vw] overflow-x-auto shadow-sm">
                  {assets.map((asset, index) => (
                    <button
                      key={asset.id}
                      onClick={() => onIndexChange(index)}
                      className={cn(
                        "flex-shrink-0 w-16 h-16 rounded overflow-hidden border-2 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40",
                        index === currentIndex
                          ? "border-primary"
                          : "border-[#EDEDED] dark:border-border hover:border-foreground/40"
                      )}
                    >
                      <img
                        src={asset.url}
                        alt={asset.displayName || asset.filename}
                        className="w-full h-full object-contain bg-muted/10"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
  );
};
