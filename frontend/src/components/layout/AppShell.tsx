"use client";

import React from "react";
import { useLayout } from "@/contexts/LayoutContext";
import { Header } from "./Header";
import { Sidebar } from "./Sidebar";
import { Footer } from "./Footer";
import { cn } from "@/lib/utils";

interface AppShellProps {
  children: React.ReactNode;
  pageTitle?: string;
  noContentPadding?: boolean;
}

export const AppShell: React.FC<AppShellProps> = ({ children, pageTitle, noContentPadding }) => {
  const { sidebarCollapsed, isMobile } = useLayout();

  return (
    <div className="flex h-screen bg-background overflow-x-hidden">
      {/* Sidebar */}
      <Sidebar />

      {/* Main content area */}
      <div
        className={cn(
          "flex flex-1 flex-col min-w-0 transition-[margin-left] duration-300 ease-linear",
          !isMobile && !sidebarCollapsed ? "ml-64" : "ml-0"
        )}
      >
        {/* Header */}
        <Header pageTitle={pageTitle} />

        {/* Page content */}
        <main className="flex-1 overflow-auto">
          <div className="flex flex-col">
            <div
              className={cn(
                "flex-1",
                noContentPadding
                  ? "p-0"
                  : !isMobile && !sidebarCollapsed
                  ? "pl-0 pr-6 pt-6 pb-6"
                  : !isMobile && sidebarCollapsed
                  ? "p-0"
                  : "p-6"
              )}
            >
              {children}
            </div>
            {/* <Footer /> */}
          </div>
        </main>
      </div>
    </div>
  );
};
