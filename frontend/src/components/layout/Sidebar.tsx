"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLayout } from "@/contexts/LayoutContext";
import {
  Home,
  Package,
  ShoppingCart,
  Users,
  Store,
  Settings,
  BarChart3,
  Webhook,
  ChevronDown,
  BookOpen,
  HelpCircle,
  MessageCircle,
  Phone,
  Link as LinkIcon,
  Wand2,
  Globe,
} from "lucide-react";
import { cn } from "@/lib/utils";
import useNavigationTracking from "@/hooks/useNavigationTracking";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: Home },
  { name: "Media Studio", href: "/media-studio", icon: Wand2 },
  { name: "Products", href: "/products", icon: Package },
  { name: "Stores", href: "/stores", icon: Store },
];

const commerceNavigation = [
  { name: "Orders", href: "/orders", icon: ShoppingCart },
  { name: "Customers", href: "/customers", icon: Users },
  { name: "Web Scraper", href: "/scraper", icon: Globe },
  { name: "Webhooks", href: "/webhook-monitor", icon: Webhook },
];

const helpNavigation = [
  { name: "Documentation", href: "/docs", icon: BookOpen },
  { name: "Help Center", href: "/help", icon: HelpCircle },
  { name: "FAQ", href: "/faq", icon: HelpCircle },
  { name: "Support", href: "/support", icon: MessageCircle },
];

export const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const { sidebarCollapsed, setSidebarCollapsed, isMobile } = useLayout();
  const { trackNavClick } = useNavigationTracking({ section: "Sidebar" });
  const [commerceDropdownOpen, setCommerceDropdownOpen] = React.useState(false);

  if (isMobile) {
    return null; // Handle mobile sidebar separately if needed
  }

  // Keep sidebar mounted to allow smooth slide animations; keep it fully off-screen when collapsed.

  return (
    <nav
      role="navigation"
      aria-label="Sidebar"
      aria-hidden={sidebarCollapsed}
      className={cn(
        "fixed left-0 top-0 z-40 h-screen w-64 bg-card border-r border-[#EDEDED] dark:border-border",
        "transform transition-transform duration-300 ease-linear",
        sidebarCollapsed ? "-translate-x-full pointer-events-none" : "translate-x-0"
      )}
    >
      <div className="flex h-full flex-col">
        {/* Logo/Brand */}
        <div className="flex h-12 items-center justify-between border-b border-[#EDEDED] dark:border-border px-3">
          {!sidebarCollapsed && (
            <span className="text-base font-semibold">Fieson</span>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto px-3 py-2">
          <div className="space-y-1 pb-1 mb-1 border-b border-[#EDEDED] dark:border-border">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => trackNavClick(item.name, item.href)}
                  aria-current={isActive ? "page" : undefined}
                  className={cn(
                    "block focus-visible:outline focus-visible:outline-2 focus-visible:outline-primary/40"
                  )}
                >
                  <div
                    className={cn(
                      "flex items-center gap-2 rounded-none px-2 h-8 text-sm font-medium transition-colors",
                      isActive
                        ? "border-l-2 border-l-primary text-foreground bg-transparent"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted/40",
                      sidebarCollapsed && "justify-center px-2"
                    )}
                  >
                    <item.icon className="h-4 w-4 flex-shrink-0" />
                    {!sidebarCollapsed && <span>{item.name}</span>}
                  </div>
                </Link>
              );
            })}
          </div>

          {/* Commerce Dropdown */}
          <div className="space-y-1">
            <button
              onClick={() => setCommerceDropdownOpen(!commerceDropdownOpen)}
              className={cn(
                "flex w-full items-center gap-2 rounded-none px-2 h-8 text-sm font-medium transition-colors",
                "text-muted-foreground hover:text-foreground hover:bg-muted/40",
                sidebarCollapsed && "justify-center px-2 focus-visible:outline-none"
              )}
            >
              <Store className="h-4 w-4 flex-shrink-0" />
              {!sidebarCollapsed && (
                <>
                  <span>Commerce</span>
                  <ChevronDown
                    className={cn(
                      "ml-auto h-4 w-4 transition-transform",
                      commerceDropdownOpen && "rotate-180"
                    )}
                  />
                </>
              )}
            </button>

            {commerceDropdownOpen && !sidebarCollapsed && (
              <div className="ml-4 space-y-1">
                {commerceNavigation.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => trackNavClick(item.name, item.href)}
                      aria-current={isActive ? "page" : undefined}
                      className="block focus-visible:outline focus-visible:outline-2 focus-visible:outline-primary/40"
                    >
                      <div
                        className={cn(
                          "flex items-center gap-2 rounded-none px-2 h-8 text-sm font-medium transition-colors",
                          isActive
                            ? "border-l-2 border-l-primary text-foreground bg-transparent"
                            : "text-muted-foreground hover:text-foreground hover:bg-muted/40"
                        )}
                      >
                        <item.icon className="h-4 w-4 flex-shrink-0" />
                        <span>{item.name}</span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Help Section */}
        <div className="border-t border-[#EDEDED] dark:border-border p-3">
          {!sidebarCollapsed && (
            <h3 className="mb-2 px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Help & Support
            </h3>
          )}
          <div className="space-y-1">
            {helpNavigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => trackNavClick(item.name, item.href)}
                  aria-current={isActive ? "page" : undefined}
                  className="block focus-visible:outline focus-visible:outline-2 focus-visible:outline-primary/40"
                >
                  <div
                    className={cn(
                      "flex items-center gap-2 rounded-none px-2 h-8 text-sm font-medium transition-colors",
                      isActive
                        ? "border-l-2 border-l-primary text-foreground bg-transparent"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted/40",
                      sidebarCollapsed && "justify-center px-2"
                    )}
                  >
                    <item.icon className="h-4 w-4 flex-shrink-0" />
                    {!sidebarCollapsed && <span>{item.name}</span>}
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
};
