"use client";

import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useLayout } from "@/contexts/LayoutContext";
import { Button } from "@/components/ui/button";
import { ChevronsLeft, Menu, User, LogOut } from "lucide-react";
import { cn } from "@/lib/utils";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import useAnalytics from "@/hooks/useAnalytics";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderProps {
  pageTitle?: string;
}

export const Header: React.FC<HeaderProps> = ({ pageTitle }) => {
  const { user, logout } = useAuth();
  const { toggleSidebar, isMobile, sidebarCollapsed } = useLayout();
  const { trackEvent } = useAnalytics();

  const handleLogout = () => {
    trackEvent({
      action: "logout",
      category: "Authentication",
      label: "Header Logout",
    });
    logout();
  };

  return (
    <header
      className={cn(
        // Compact, slightly tighter left padding for better visual balance
        "flex h-12 items-center justify-between border-b bg-card border-[#EDEDED] dark:border-border pl-2 pr-3"
      )}
    >
      <div className="flex items-center gap-2">
        <button
          onClick={toggleSidebar}
          aria-label={sidebarCollapsed ? "Open menu" : "Close menu"}
          title={sidebarCollapsed ? "Open menu" : "Close menu"}
          aria-expanded={!sidebarCollapsed}
          className={cn(
            "group h-10 w-10 inline-flex items-center justify-center rounded-md transition-colors",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 focus-visible:ring-offset-0",
            "text-foreground/80 hover:text-primary"
          )}
        >
          {sidebarCollapsed ? (
            <Menu className="h-5 w-5 transition-colors" />
          ) : (
            <ChevronsLeft className="h-5 w-5 transition-colors" />
          )}
        </button>

        {/* Page Title on Left */}
        {pageTitle && (
          <h1 className="text-lg font-semibold text-foreground">
            {pageTitle}
          </h1>
        )}
      </div>

      <div className="flex items-center gap-3">
        {/* Credits Display (compact) */}
        <div className="flex items-center gap-1.5 px-2 py-0.5 bg-primary/10 rounded-md">
          <span className="text-xs font-medium text-primary">Credits:</span>
          <span className="text-xs font-semibold text-primary">1234</span>
        </div>

        <ThemeToggle />
        {user && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <User className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user.full_name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Account</DropdownMenuItem>
              <DropdownMenuItem>Privacy Policy</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
};
