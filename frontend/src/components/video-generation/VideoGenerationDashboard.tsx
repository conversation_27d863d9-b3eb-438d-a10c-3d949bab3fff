"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Play,
  Pause,
  Download,
  Share2,
  RotateCcw,
  Eye,
  TrendingUp,
  Video,
  Image as ImageIcon,
  Zap,
} from "lucide-react";
import { VideoService } from "@/services/videoService";
import { ShopifyService } from "@/services/shopifyService";

interface VideoVariant {
  variant_id: number;
  variant_name: string;
  status: string;
  video_url?: string;
  thumbnail_url?: string;
  duration?: number;
}

interface VideoJob {
  job_id: number;
  status: string;
  progress: number;
  variants: VideoVariant[];
}

interface ProductAnalytics {
  views: number;
  plays: number;
  completion_rate: number;
  conversions: number;
  conversion_lift?: number;
}

export default function VideoGenerationDashboard() {
  const [activeJobs, setActiveJobs] = useState<VideoJob[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [analytics, setAnalytics] = useState<ProductAnalytics | null>(null);

  // Mock data for demonstration
  const mockJob: VideoJob = {
    job_id: 1,
    status: "completed",
    progress: 100,
    variants: [
      {
        variant_id: 1,
        variant_name: "Square (1:1)",
        status: "completed",
        video_url: "https://example.com/video1.mp4",
        thumbnail_url: "https://example.com/thumb1.jpg",
        duration: 30,
      },
      {
        variant_id: 2,
        variant_name: "Vertical (9:16)",
        status: "completed",
        video_url: "https://example.com/video2.mp4",
        thumbnail_url: "https://example.com/thumb2.jpg",
        duration: 30,
      },
      {
        variant_id: 3,
        variant_name: "Horizontal (16:9)",
        status: "completed",
        video_url: "https://example.com/video3.mp4",
        thumbnail_url: "https://example.com/thumb3.jpg",
        duration: 30,
      },
      {
        variant_id: 4,
        variant_name: "Story (9:16)",
        status: "completed",
        video_url: "https://example.com/video4.mp4",
        thumbnail_url: "https://example.com/thumb4.jpg",
        duration: 15,
      },
    ],
  };

  const mockAnalytics: ProductAnalytics = {
    views: 1250,
    plays: 980,
    completion_rate: 0.78,
    conversions: 42,
    conversion_lift: 15.3,
  };

  useEffect(() => {
    setActiveJobs([mockJob]);
    setAnalytics(mockAnalytics);
  }, []);

  const handleGenerateVideos = async () => {
    setIsGenerating(true);
    try {
      // Mock generation process
      await new Promise((resolve) => setTimeout(resolve, 2000));
      // In real implementation:
      // const result = await VideoService.generateVideos({
      //   shop_id: 1,
      //   product_ids: selectedProducts,
      //   template_id: 'modern_product_showcase',
      //   aspect_ratio: '16:9',
      //   locale: 'en'
      // })
    } catch (error) {
      console.error("Generation failed:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleRegenerateVariant = async (jobId: number, variantId: number) => {
    try {
      // await VideoService.regenerateVariant({
      //   job_id: jobId,
      //   variant_id: variantId,
      //   override_params: {}
      // })
      console.log("Regenerating variant:", variantId);
    } catch (error) {
      console.error("Regeneration failed:", error);
    }
  };

  const handlePushToShopify = async (variantId: number, productId: string) => {
    try {
      // await VideoService.pushToShopify({
      //   shop_id: 1,
      //   product_id: productId,
      //   variant_id: variantId,
      //   publish_targets: ['product_media'],
      //   publish_options: {
      //     alt_text: 'Product video',
      //     position: 1
      //   }
      // })
      console.log("Pushing to Shopify:", variantId);
    } catch (error) {
      console.error("Push failed:", error);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Video Generation
          </h1>
          <p className="text-muted-foreground">
            Create engaging product videos with AI
          </p>
        </div>
        <Button
          onClick={handleGenerateVideos}
          disabled={isGenerating || selectedProducts.length === 0}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          {isGenerating ? (
            <>
              <Zap className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Video className="mr-2 h-4 w-4" />
              Generate Videos
            </>
          )}
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.views.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Play Rate</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics
                ? Math.round((analytics.plays / analytics.views) * 100)
                : 0}
              %
            </div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics ? Math.round(analytics.completion_rate * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">+8% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.conversions}</div>
            <p className="text-xs text-muted-foreground">
              +{analytics?.conversion_lift}% lift
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="generation" className="space-y-4">
        <TabsList>
          <TabsTrigger value="generation">Video Generation</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="generation" className="space-y-4">
          {/* Active Jobs */}
          {activeJobs.map((job) => (
            <Card key={job.job_id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Job #{job.job_id}</CardTitle>
                    <CardDescription>
                      Status:{" "}
                      <Badge
                        variant={
                          job.status === "completed" ? "default" : "secondary"
                        }
                      >
                        {job.status}
                      </Badge>
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">
                      Progress
                    </div>
                    <Progress value={job.progress} className="w-[100px]" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  {job.variants.map((variant) => (
                    <Card key={variant.variant_id} className="overflow-hidden">
                      <div className="aspect-video bg-muted relative">
                        {variant.thumbnail_url ? (
                          <img
                            src={variant.thumbnail_url}
                            alt={variant.variant_name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <Video className="h-8 w-8 text-muted-foreground" />
                          </div>
                        )}
                        <div className="absolute top-2 right-2">
                          <Badge variant="secondary" className="text-xs">
                            {variant.duration}s
                          </Badge>
                        </div>
                      </div>
                      <CardContent className="p-4">
                        <h4 className="font-semibold text-sm mb-2">
                          {variant.variant_name}
                        </h4>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1"
                          >
                            <Play className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleRegenerateVariant(
                                job.job_id,
                                variant.variant_id
                              )
                            }
                          >
                            <RotateCcw className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handlePushToShopify(
                                variant.variant_id,
                                "product_123"
                              )
                            }
                          >
                            <Share2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>
                Detailed insights into your video performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Analytics dashboard coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <CardTitle>Video Templates</CardTitle>
              <CardDescription>
                Choose from professional templates for your videos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Template gallery coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
