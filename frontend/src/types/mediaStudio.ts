// Additional types for Media Studio beyond what's in mediaService.ts

export type MainTab = "canvas" | "models" | "props" | "scenes" | "brandbook";

export type GenerationMode = "image" | "video";

// Rich prompt system interfaces
export interface AttachedImage {
  assetId: string;
  filename: string;
  displayName?: string; // User-friendly display name for @mentions
  url: string;
  productId: string;
}

export interface PromptSegment {
  type: "text" | "mention";
  content: string; // For text: the actual text, For mention: the display name
  image?: AttachedImage; // Only present for mention segments
}

export interface PromptWithImages {
  segments: PromptSegment[];
  // Helper method to get plain text representation
  getText(): string;
  // Helper method to get all attached images
  getAttachedImages(): AttachedImage[];
}

export type ImageSettings = {
  size: string;
  guidance: number;
  steps: number;
  strength: number;
  seed: number;
  upscale: boolean;
  safety: boolean;
  aspectRatio: string;
  quality: string;
};

export type VideoSettings = {
  duration: number;
  fps: number;
  resolution: string;
  aspectRatio: string;
  motionStrength: number;
  seed: number;
  audio: boolean;
  quality: string;
};

// Additional interfaces for UI components
export interface Collection {
  id: string;
  name: string;
  color: string;
}

export type BatchState = {
  batchId: string;
  total: number;
  completed: number;
  failed: number;
  status: string;
};

export interface RowProgress {
  completed: number;
  total: number;
  pending: number;
}

export interface Model {
  id: string;
  name: string;
  health: "good" | "degraded" | "down";
  eta: string;
  cost: number;
}
