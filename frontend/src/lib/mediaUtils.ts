import { Product } from "@/services/productService";

export interface MediaItem {
  type:
    | "image"
    | "video"
    | "featured_image"
    | "featured_video"
    | "detected_image"
    | "detected_video";
  id: string;
  src: string;
  alt: string;
  path?: string;
  key?: string;
  data?: any;
}

/**
 * Recursively parses all JSON strings in an object
 */
export const parseJsonStringsRecursively = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === "string") {
    // Try to parse as JSON
    try {
      const parsed = JSON.parse(obj);
      // If parsing succeeds and it's an object/array, return parsed
      if (typeof parsed === "object" && parsed !== null) {
        return parseJsonStringsRecursively(parsed);
      }
      // If it's a primitive (string, number, boolean), return as is
      return parsed;
    } catch (e) {
      // If parsing fails, return original string
      return obj;
    }
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => parseJsonStringsRecursively(item));
  }

  if (typeof obj === "object") {
    const result: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result[key] = parseJsonStringsRecursively(obj[key]);
      }
    }
    return result;
  }

  // Return primitive values as is
  return obj;
};

/**
 * Helper function to parse featured media
 */
export const parseFeaturedMedia = (product: Product) => {
  if (!product.featured_media) return null;

  try {
    const mediaData = JSON.parse(product.featured_media);
    return {
      id: mediaData.id,
      mediaContentType: mediaData.media_content_type,
      preview: mediaData.preview,
      alt: mediaData.alt,
      status: mediaData.status,
    };
  } catch (e) {
    console.warn("Failed to parse featured_media:", e);
    return null;
  }
};

/**
 * Function to detect media URLs in JSON data
 */
export const detectMediaInJson = (
  obj: any,
  path: string = "",
  parentKey: string = ""
): MediaItem[] => {
  const mediaItems: MediaItem[] = [];

  if (!obj) return mediaItems;

  // Check if current value is a string that looks like a media URL
  if (typeof obj === "string") {
    const url = obj.trim();

    // Check for image URLs
    if (
      /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff?)(\?.*)?$/i.test(url) ||
      (url.includes("cdn.shopify.com") && /\bimages?\b/i.test(path))
    ) {
      mediaItems.push({
        type: "detected_image",
        id: `detected_${path}_${url.slice(-10)}`,
        src: url,
        alt: `Detected image from ${path}`,
        path: path,
        key: parentKey,
        data: { url, path, key: parentKey },
      });
    }

    // Check for video URLs
    if (
      /\.(mp4|webm|ogg|avi|mov|wmv|flv|m4v|mkv)(\?.*)?$/i.test(url) ||
      (url.includes("cdn.shopify.com") && /\bvideo\b/i.test(path))
    ) {
      mediaItems.push({
        type: "detected_video",
        id: `detected_${path}_${url.slice(-10)}`,
        src: url,
        alt: `Detected video from ${path}`,
        path: path,
        key: parentKey,
        data: { url, path, key: parentKey },
      });
    }

    return mediaItems;
  }

  // Recursively search arrays
  if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      mediaItems.push(
        ...detectMediaInJson(item, `${path}[${index}]`, parentKey)
      );
    });
    return mediaItems;
  }

  // Recursively search objects
  if (typeof obj === "object") {
    Object.keys(obj).forEach((key) => {
      const newPath = path ? `${path}.${key}` : key;
      mediaItems.push(...detectMediaInJson(obj[key], newPath, key));
    });
    return mediaItems;
  }

  return mediaItems;
};

/**
 * Get all available media for a product (images + featured media + detected media)
 * @param product - The product to analyze
 */
export const getAllMediaForProduct = (product: Product): MediaItem[] => {
  const mediaItems: MediaItem[] = [];

  // Parse the product data to detect media in JSON
  const parsedProduct = parseJsonStringsRecursively(product);
  const detectedMedia = detectMediaInJson(parsedProduct);
  detectedMedia.forEach((media) => {
    mediaItems.push(media);
  });

  // Add featured media (videos and images)
  const featuredMedia = parseFeaturedMedia(product);
  if (featuredMedia) {
    if (
      featuredMedia.mediaContentType === "VIDEO" &&
      featuredMedia.preview?.image?.url
    ) {
      mediaItems.unshift({
        type: "featured_video",
        id: featuredMedia.id,
        src: featuredMedia.preview.image.url,
        alt: featuredMedia.alt || product.title,
        data: featuredMedia,
      });
    } else if (
      featuredMedia.mediaContentType === "IMAGE" &&
      featuredMedia.preview?.image?.url
    ) {
      mediaItems.unshift({
        type: "featured_image",
        id: featuredMedia.id,
        src: featuredMedia.preview.image.url,
        alt: featuredMedia.alt || product.title,
        data: featuredMedia,
      });
    }
  }

  // Add regular images
  if (product.images && product.images.length > 0) {
    product.images.forEach((img) => {
      mediaItems.push({
        type: "image",
        id: img.id.toString(),
        src: img.src,
        alt: img.alt || product.title,
        data: img,
      });
    });
  }

  // Remove duplicates based on src URL
  const uniqueMedia = mediaItems.filter(
    (media, index, self) => index === self.findIndex((m) => m.src === media.src)
  );

  return uniqueMedia;
};

