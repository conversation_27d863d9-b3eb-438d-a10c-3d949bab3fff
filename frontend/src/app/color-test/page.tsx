"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from "@/components/ui/navigation-menu";

export default function ColorTestPage() {
  return (
    <div className="min-h-screen bg-background p-8 space-y-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">
          🎨 Color System Test - New Green Accent Theme
        </h1>
        
        <div className="space-y-8">
          {/* Button Variants */}
          <Card>
            <CardHeader>
              <CardTitle>Button Variants (Test Hover States)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <Button variant="default">Primary Button</Button>
                <Button variant="outline">Outline Button (Uses Accent)</Button>
                <Button variant="ghost">Ghost Button (Uses Accent)</Button>
                <Button variant="secondary">Secondary Button</Button>
                <Button variant="destructive">Destructive Button</Button>
              </div>
            </CardContent>
          </Card>

          {/* Card Variants */}
          <Card>
            <CardHeader>
              <CardTitle>Card Variants</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card variant="default">
                <CardContent className="p-4">
                  <p>Default Card</p>
                </CardContent>
              </Card>
              <Card variant="interactive">
                <CardContent className="p-4">
                  <p>Interactive Card (Uses Accent on Hover)</p>
                </CardContent>
              </Card>
              <Card variant="glass">
                <CardContent className="p-4">
                  <p>Glass Card</p>
                </CardContent>
              </Card>
            </CardContent>
          </Card>

          {/* Badge Variants */}
          <Card>
            <CardHeader>
              <CardTitle>Badge Variants</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="default">Default Badge</Badge>
                <Badge variant="outline">Outline Badge (Uses Accent)</Badge>
                <Badge variant="ghost">Ghost Badge (Uses Accent)</Badge>
                <Badge variant="secondary">Secondary Badge</Badge>
                <Badge variant="destructive">Destructive Badge</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Input Components */}
          <Card>
            <CardHeader>
              <CardTitle>Input Components</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input placeholder="Test input with hover states" />
              <Input variant="glass" placeholder="Glass variant input" />
              <Input variant="minimal" placeholder="Minimal variant input" />
            </CardContent>
          </Card>

          {/* Navigation Menu */}
          <Card>
            <CardHeader>
              <CardTitle>Navigation Menu (Uses Accent)</CardTitle>
            </CardHeader>
            <CardContent>
              <NavigationMenu>
                <NavigationMenuList>
                  <NavigationMenuItem>
                    <NavigationMenuTrigger>Getting started</NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <div className="grid gap-3 p-6 md:w-[400px] lg:w-[500px]">
                        <NavigationMenuLink className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                          <div className="text-sm font-medium leading-none">Introduction</div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            Re-usable components built using Radix UI and Tailwind CSS.
                          </p>
                        </NavigationMenuLink>
                      </div>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                </NavigationMenuList>
              </NavigationMenu>
            </CardContent>
          </Card>

          {/* Color Swatches */}
          <Card>
            <CardHeader>
              <CardTitle>New Green Accent Color Palette</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <div className="space-y-2">
                  <div className="w-full h-16 bg-accent-50 rounded border"></div>
                  <p className="text-xs text-center">accent-50</p>
                </div>
                <div className="space-y-2">
                  <div className="w-full h-16 bg-accent-100 rounded border"></div>
                  <p className="text-xs text-center">accent-100</p>
                </div>
                <div className="space-y-2">
                  <div className="w-full h-16 bg-accent-200 rounded border"></div>
                  <p className="text-xs text-center">accent-200</p>
                </div>
                <div className="space-y-2">
                  <div className="w-full h-16 bg-accent-500 rounded border"></div>
                  <p className="text-xs text-center">accent-500</p>
                </div>
                <div className="space-y-2">
                  <div className="w-full h-16 bg-accent-700 rounded border"></div>
                  <p className="text-xs text-center">accent-700</p>
                </div>
                <div className="space-y-2">
                  <div className="w-full h-16 bg-accent-900 rounded border"></div>
                  <p className="text-xs text-center">accent-900</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Success Message */}
          <Card variant="interactive" className="border-green-200 bg-green-50">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                <p className="text-green-800 font-medium">
                  ✅ Success! The ugly reddish hover color has been eliminated and replaced with a beautiful, coherent green accent system that complements your media-studio design.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
