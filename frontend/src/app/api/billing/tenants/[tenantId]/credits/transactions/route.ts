import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { tenantId: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')

    // In a real implementation, this would proxy to your backend API
    // For now, return mock data
    const mockTransactions = [
      {
        id: "tx_1",
        type: "subscription_grant",
        amount: 10000,
        balance_after: 18750,
        description: "Monthly subscription renewal: 10,000 credits (professional plan)",
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        expires_at: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(), // 28 days from now
        metadata: { plan_type: "professional" }
      },
      {
        id: "tx_2",
        type: "usage",
        amount: -2450,
        balance_after: 16300,
        description: "Usage: 50 video generations",
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        resource_type: "media_generation"
      },
      {
        id: "tx_3",
        type: "purchase",
        amount: 5000,
        balance_after: 21300,
        description: "Credit purchase: 5,000 credits",
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
        expires_at: new Date(Date.now() + 355 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
        metadata: { credit_amount: 5000 }
      }
    ].slice(0, limit)

    return NextResponse.json({ transactions: mockTransactions })
  } catch (error) {
    console.error('Error fetching credit transactions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch credit transactions' },
      { status: 500 }
    )
  }
}