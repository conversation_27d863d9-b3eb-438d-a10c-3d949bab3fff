import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { tenantId: string } }
) {
  try {
    // In a real implementation, this would proxy to your backend API
    // For now, return mock data
    const mockBalance = {
      current_balance: 8750,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      plan_tier: "professional",
      monthly_grant: 10000
    }

    return NextResponse.json(mockBalance)
  } catch (error) {
    console.error('Error fetching credit balance:', error)
    return NextResponse.json(
      { error: 'Failed to fetch credit balance' },
      { status: 500 }
    )
  }
}