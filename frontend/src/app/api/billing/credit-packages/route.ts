import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // In a real implementation, this would proxy to your backend API
    // For now, return mock data
    const mockPackages = [
      {
        id: "credits_1000",
        credits: 1000,
        amount: 1000, // $10.00 in cents
        price_id: "price_1ABC123"
      },
      {
        id: "credits_5000",
        credits: 5000,
        amount: 4500, // $45.00 in cents
        price_id: "price_2DEF456"
      },
      {
        id: "credits_10000",
        credits: 10000,
        amount: 8000, // $80.00 in cents
        price_id: "price_3GHI789"
      },
      {
        id: "credits_25000",
        credits: 25000,
        amount: 17500, // $175.00 in cents
        price_id: "price_4JKL012"
      }
    ]

    return NextResponse.json({ packages: mockPackages })
  } catch (error) {
    console.error('Error fetching credit packages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch credit packages' },
      { status: 500 }
    )
  }
}