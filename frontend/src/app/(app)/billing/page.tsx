"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { CreditBalance, CreditTransactions, CreditPurchase } from "@/components/billing"
import { Receipt, CreditCard, TrendingUp, AlertTriangle, CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// Mock data - replace with actual API calls
interface CreditPackage {
  id: string
  credits: number
  amount: number
  price_id: string
}

const mockCreditPackages: CreditPackage[] = [
  { id: "credits_1000", credits: 1000, amount: 1000, price_id: "price_1000" },
  { id: "credits_5000", credits: 5000, amount: 4500, price_id: "price_5000" },
  { id: "credits_10000", credits: 10000, amount: 8000, price_id: "price_10000" },
  { id: "credits_25000", credits: 25000, amount: 17500, price_id: "price_25000" },
]

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [creditPackages, setCreditPackages] = useState<CreditPackage[]>(mockCreditPackages)
  const [tenantId, setTenantId] = useState<string>("1") // Mock tenant ID
  const { toast } = useToast()

  useEffect(() => {
    // Check for success/cancelled query parameters
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('success') === 'true') {
      toast({
        title: "Purchase Successful",
        description: "Your credits have been added to your account.",
      })
      // Clear the URL parameter
      window.history.replaceState({}, '', window.location.pathname)
    } else if (urlParams.get('cancelled') === 'true') {
      toast({
        title: "Purchase Cancelled",
        description: "Your purchase was cancelled. No charges were made.",
        variant: "destructive",
      })
      // Clear the URL parameter
      window.history.replaceState({}, '', window.location.pathname)
    }

    // Load credit packages
    loadCreditPackages()
  }, [])

  const loadCreditPackages = async () => {
    try {
      const response = await fetch('/api/billing/credit-packages')
      if (response.ok) {
        const data = await response.json()
        setCreditPackages(data.packages)
      }
    } catch (error) {
      console.error('Failed to load credit packages:', error)
      // Keep mock data as fallback
    }
  }

  const handlePurchaseClick = () => {
    setActiveTab("purchase")
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Billing & Credits</h1>
        <p className="text-muted-foreground mt-2">
          Manage your subscription, credits, and billing information
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="credits">Credits</TabsTrigger>
          <TabsTrigger value="purchase">Purchase</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <CreditBalance tenantId={tenantId} onPurchaseClick={handlePurchaseClick} />

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Usage This Month
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,450</div>
                <p className="text-sm text-muted-foreground">Credits used</p>
                <div className="mt-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Images</span>
                    <span>1,200</span>
                  </div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Videos</span>
                    <span>850</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Text</span>
                    <span>400</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5" />
                  Current Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-xl font-bold">Professional</div>
                    <p className="text-sm text-muted-foreground">$99/month</p>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    10,000 credits/month
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Unlimited storage
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Priority support
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Your latest credit transactions and usage</CardDescription>
            </CardHeader>
            <CardContent>
              <CreditTransactions tenantId={tenantId} limit={5} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="credits" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-1">
              <CreditBalance tenantId={tenantId} onPurchaseClick={handlePurchaseClick} />
            </div>
            <div className="lg:col-span-2">
              <CreditTransactions tenantId={tenantId} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="purchase" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Purchase Credits</CardTitle>
              <CardDescription>
                Buy additional credits to ensure uninterrupted service
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CreditPurchase
                tenantId={tenantId}
                packages={creditPackages}
                onPurchaseComplete={() => {
                  setActiveTab("credits")
                  toast({
                    title: "Credits Purchased",
                    description: "Your credits have been added successfully.",
                  })
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>View and download your invoices</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Receipt className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No invoices yet</p>
                <p className="text-sm">Your billing history will appear here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}