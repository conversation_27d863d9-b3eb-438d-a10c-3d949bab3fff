"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useLayout } from "@/contexts/LayoutContext";
import { cn } from "@/lib/utils";
import { productService, Product } from "@/services/productService";
import { mediaService, Asset } from "@/services/mediaService";
import { assetService } from "@/services/assetService";
import {
  MainTab,
  GenerationMode,
  ImageSettings,
  VideoSettings,
  BatchState,
  PromptWithImages,
} from "@/types/mediaStudio";
import { createPromptFromText } from "@/utils/promptUtils";
import { MainTabs } from "@/components/media-studio/MainTabs";
import { ProductGrid } from "@/components/media-studio/ProductGrid";
import { PreviewPane } from "@/components/media-studio/PreviewPane";
import { FloatingGenerateBar } from "@/components/media-studio/FloatingGenerateBar";
import { PromptEditor } from "@/components/media-studio/PromptEditor";
import { CollectionsFilter } from "@/components/media-studio/CollectionsFilter";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Search, AlertCircle } from "lucide-react";
import { toast } from "sonner";

const MODELS = [
  { id: "banana", name: "Banana", type: "image" as const },
  { id: "banana-video", name: "Banana Video", type: "video" as const },
];

const MediaStudioPage: React.FC = () => {
  const { sidebarCollapsed, isMobile } = useLayout();

  // Core state
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [productsError, setProductsError] = useState<string | null>(null);

  // Selection state
  const [selectedAssetIds, setSelectedAssetIds] = useState<Set<string>>(
    new Set()
  );
  const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(
    new Set()
  );

  // Image attachment state for generation composition
  const [attachedImages, setAttachedImages] = useState<
    Record<
      string,
      Array<{
        id: string;
        url: string;
        filename: string;
        displayName?: string;
      }>
    >
  >({});
  const [activeAsset, setActiveAsset] = useState<Asset | null>(null);

  // UI state
  const [activeMainTab, setActiveMainTab] = useState<MainTab>("gallery");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("image");
  const [selectedModelId, setSelectedModelId] = useState<string>("banana");

  // Generation state
  const [settings, setSettings] = useState<ImageSettings | VideoSettings>({
    size: "1024x1024",
    guidance: 7.5,
    steps: 25,
    strength: 0.8,
    seed: Math.floor(Math.random() * 100000),
    upscale: true,
    safety: true,
    aspectRatio: "1:1",
    quality: "Standard",
  });

  // Prompts and filters with persistence
  const [prompts, setPrompts] = useState<
    Record<string, string | PromptWithImages>
  >({});

  const [collectionFilters, setCollectionFilters] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-collection-filters");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });

  const [searchQuery, setSearchQuery] = useState<string>(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("media-studio-search-query") || "";
    }
    return "";
  });

  const [filterSelectedOnly, setFilterSelectedOnly] = useState<boolean>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-filter-selected-only");
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  const [filterHasGenerated, setFilterHasGenerated] = useState<boolean>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-filter-has-generated");
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  const [sortMode, setSortMode] = useState<
    "default" | "selected_first" | "generated_first"
  >(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-sort-mode");
      return (
        (saved as "default" | "selected_first" | "generated_first") || "default"
      );
    }
    return "default";
  });

  // Generation batch state
  const [generationBatch, setGenerationBatch] = useState<BatchState | null>(
    null
  );
  const [isInitiating, setIsInitiating] = useState(false);
  const [initiationMessage, setInitiationMessage] = useState<string | null>(
    null
  );
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Generated assets
  const [generatedImages, setGeneratedImages] = useState<
    Record<string, Asset[]>
  >({});
  const [isLoadingAssets, setIsLoadingAssets] = useState(true);
  const [assetsError, setAssetsError] = useState<string | null>(null);

  // Gallery pagination state
  const [galleryAssets, setGalleryAssets] = useState<Asset[]>([]);
  const [galleryPage, setGalleryPage] = useState(1);
  const [galleryHasMore, setGalleryHasMore] = useState(true);
  const [isLoadingMoreGallery, setIsLoadingMoreGallery] = useState(false);

  // Persist filter states
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "media-studio-collection-filters",
        JSON.stringify(collectionFilters)
      );
    }
  }, [collectionFilters]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("media-studio-search-query", searchQuery);
    }
  }, [searchQuery]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "media-studio-filter-selected-only",
        JSON.stringify(filterSelectedOnly)
      );
    }
  }, [filterSelectedOnly]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "media-studio-filter-has-generated",
        JSON.stringify(filterHasGenerated)
      );
    }
  }, [filterHasGenerated]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("media-studio-sort-mode", sortMode);
    }
  }, [sortMode]);

  // Load products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);
        setProductsError(null);

        const response = await productService.getProducts(1, 50, "");
        if (response && response.items) {
          const transformedProducts: Product[] = response.items.map(
            (item: any) => {
              const assets: Asset[] = [];
              const productForNaming = {
                title: item.title || item.name || "Product",
              } as any;

              if (item.assets && Array.isArray(item.assets)) {
                item.assets.forEach((assetItem: any) => {
                  const asset: Asset = {
                    id: assetItem.id,
                    productId: item.product_id || item.id,
                    url: assetItem.src,
                    type: assetItem.type as "image" | "video",
                    filename: assetItem.src?.split("/").pop() || "asset.jpg",
                    displayName:
                      assetItem.alt || `${productForNaming.title} - Asset`,
                  };
                  assets.push(asset);
                });
              } else if (item.images && Array.isArray(item.images)) {
                // Fallback to images if assets not available
                item.images.forEach((img: any) => {
                  const asset: Asset = {
                    id: `asset_${img.id}`,
                    productId: item.product_id || item.id,
                    url: img.src_url || img.src,
                    type: "image" as const,
                    filename: img.src_url?.split("/").pop() || "image.jpg",
                    displayName: "",
                  };
                  asset.displayName = `${productForNaming.title} - Image`;
                  assets.push(asset);
                });
              }

              const collections = Array.isArray(item.collections)
                ? item.collections.map((c: any) => ({
                    id: String(c.id ?? c.slug ?? c.name ?? "collection"),
                    name: String(c.name ?? c.slug ?? "Collection"),
                    color: String(c.color ?? "bg-gray-500"),
                  }))
                : [];

              return {
                id: item.product_id || item.id,
                external_id: item.external_id,
                title: item.title || item.name,
                variants: item.variants || [],
                collections,
                assets,
                store_id: item.store_id,
              } as Product;
            }
          );

          setProducts(transformedProducts);

          // Initialize prompts
          const initialPrompts: Record<string, string> = {};
          transformedProducts.forEach((product) => {
            initialPrompts[product.id] =
              `A professional product shot of a ${product.title.toLowerCase()}, high-resolution, on a clean white background.`;
          });
          setPrompts(initialPrompts);
        } else {
          setProducts([]);
        }
      } catch (error) {
        console.error("Failed to fetch products:", error);
        setProductsError("Failed to load products. Please try again.");
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  // Load generated assets
  const loadGeneratedAssets = useCallback(async () => {
    try {
      setIsLoadingAssets(true);
      setAssetsError(null);

      const response = await assetService.getAssets({ limit: 200 });
      const byProduct: Record<string, Asset[]> = {};

      // Transform MediaItem[] to Asset[] and group by product
      for (const item of response.items) {
        const productId = item.data?.product_id;
        if (!productId) continue;

        const asset: Asset = {
          id: item.id,
          productId: productId,
          url: item.src,
          type: item.type as "image" | "video",
          filename: item.src?.split("/").pop() || "asset.jpg",
          displayName: item.alt,
          fileUrl: item.src,
          previewUrl: item.data?.preview_uri,
          prompt: item.data?.prompt,
          sourceType: (item.source_type || item.data?.source_type) as
            | "product"
            | "ai_generated",
        };

        if (!byProduct[productId]) byProduct[productId] = [];
        byProduct[productId].push(asset);
      }
      setGeneratedImages(byProduct);

      // Also populate gallery assets
      const allAssets = Object.values(byProduct).flat();
      setGalleryAssets(allAssets);
      setGalleryPage(1);
      setGalleryHasMore(response.total_pages > 1);
    } catch (error) {
      console.error("Failed to load generated assets:", error);
      setAssetsError("Failed to load generated assets");
    } finally {
      setIsLoadingAssets(false);
    }
  }, []);

  // Load more gallery assets for infinite scroll
  const loadMoreGalleryAssets = useCallback(async () => {
    if (isLoadingMoreGallery || !galleryHasMore) return;

    try {
      setIsLoadingMoreGallery(true);
      const nextPage = galleryPage + 1;

      const response = await assetService.getAssets({
        page: nextPage,
        limit: 60,
      });

      if (response.items.length > 0) {
        // Transform new assets
        const newAssets: Asset[] = response.items.map((item: any) => ({
          id: item.id,
          productId: item.data?.product_id || "",
          url: item.src,
          type: item.type as "image" | "video",
          filename: item.src?.split("/").pop() || "asset.jpg",
          displayName: item.alt,
          fileUrl: item.src,
          previewUrl: item.data?.preview_uri,
          prompt: item.data?.prompt,
          sourceType: (item.source_type || item.data?.source_type) as
            | "product"
            | "ai_generated",
        }));

        setGalleryAssets((prev) => [...prev, ...newAssets]);
        setGalleryPage(nextPage);
        setGalleryHasMore(response.total_pages > nextPage);
      } else {
        setGalleryHasMore(false);
      }
    } catch (error) {
      console.error("Failed to load more gallery assets:", error);
      setGalleryHasMore(false);
    } finally {
      setIsLoadingMoreGallery(false);
    }
  }, [galleryPage, galleryHasMore, isLoadingMoreGallery]);

  useEffect(() => {
    loadGeneratedAssets();
  }, [loadGeneratedAssets]);

  // Reset gallery pagination when tab changes to gallery
  useEffect(() => {
    if (
      activeMainTab === "gallery" &&
      galleryAssets.length === 0 &&
      Object.keys(generatedImages).length > 0
    ) {
      const allAssets = Object.values(generatedImages).flat();
      setGalleryAssets(allAssets);
      setGalleryPage(1);
      setGalleryHasMore(true);
    }
  }, [activeMainTab, generatedImages, galleryAssets.length]);

  // Update settings when mode changes
  useEffect(() => {
    if (generationMode === "image") {
      setSettings({
        size: "1024x1024",
        guidance: 7.5,
        steps: 25,
        strength: 0.8,
        seed: Math.floor(Math.random() * 100000),
        upscale: true,
        safety: true,
        aspectRatio: "1:1",
        quality: "Standard",
      });
      setSelectedModelId(MODELS.filter((m) => !m.id.includes("veo"))[0].id);
    } else if (generationMode === "video") {
      setSettings({
        duration: 4,
        fps: 24,
        resolution: "1080p",
        aspectRatio: "16:9",
        motionStrength: 5,
        seed: Math.floor(Math.random() * 100000),
        audio: false,
        quality: "Standard",
      });
      setSelectedModelId("veo-3.0-generate-preview");
    }
  }, [generationMode]);

  const handleAssetSelect = useCallback(
    (asset: Asset, isMultiSelect: boolean) => {
      setSelectedAssetIds((prevSelectedAssets: Set<string>) => {
        let isToggleOff = false;
        const newAssetSet = new Set(prevSelectedAssets);

        if (isMultiSelect) {
          if (newAssetSet.has(asset.id)) {
            newAssetSet.delete(asset.id);
            isToggleOff = true;
          } else {
            newAssetSet.add(asset.id);
            // In video mode, limit to max 1 selected image per row
            if (generationMode === "video" && asset.type === "image") {
              const row = products.find(
                (p: Product) => p.id === asset.productId
              );
              const imgIds = (row?.assets || [])
                .filter((a: any) => a.type === "image")
                .map((a: any) => a.id);
              for (const id of imgIds) {
                if (id !== asset.id) newAssetSet.delete(id);
              }
            }
          }
        } else {
          const productAssets =
            products
              .find((p: Product) => p.id === asset.productId)
              ?.assets.map((a: Asset) => a.id) || [];

          for (const assetId of productAssets) {
            newAssetSet.delete(assetId);
          }

          if (!(productAssets.length === 1 && productAssets[0] === asset.id)) {
            newAssetSet.add(asset.id);
          } else {
            isToggleOff = true;
          }
        }

        // Update product selection based on asset selection
        const productHasSelection = products
          .find((p) => p.id === asset.productId)
          ?.assets.some((a) => newAssetSet.has(a.id));

        setSelectedProductIds((prevSelectedProducts) => {
          const newProductsSet = new Set(prevSelectedProducts);
          if (productHasSelection) {
            newProductsSet.add(asset.productId);
          } else {
            newProductsSet.delete(asset.productId);
          }
          return newProductsSet;
        });

        return newAssetSet;
      });
    },
    [products, generationMode]
  );

  const handleProductSelectionChange = useCallback(
    (productId: string, isChecked: boolean) => {
      const product = products.find((p) => p.id === productId);
      if (!product) return;

      setSelectedProductIds((prev) => {
        const newSet = new Set(prev);
        if (isChecked) newSet.add(productId);
        else newSet.delete(productId);
        return newSet;
      });

      if (isChecked) {
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (generationMode === "video") {
            const imageAssets = product.assets.filter(
              (a) => a.type === "image"
            );
            const toAdd = imageAssets[0];
            if (toAdd) newSet.add(toAdd.id);
          } else {
            product.assets.forEach((asset) => newSet.add(asset.id));
          }
          return newSet;
        });
      } else {
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          product.assets.forEach((asset) => newSet.delete(asset.id));
          return newSet;
        });
      }
    },
    [products, generationMode]
  );

  const handleSelectAllProducts = useCallback(
    (isChecked: boolean) => {
      if (isChecked) {
        const allProductIds = new Set(products.map((p) => p.id));
        setSelectedProductIds(allProductIds);

        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (generationMode === "video") {
            products.forEach((product) => {
              const firstImg = product.assets.find((a) => a.type === "image");
              if (firstImg) newSet.add(firstImg.id);
            });
          } else {
            products.forEach((product) => {
              product.assets.forEach((asset) => newSet.add(asset.id));
            });
          }
          return newSet;
        });
      } else {
        setSelectedProductIds(new Set());
        setSelectedAssetIds(new Set());
        setActiveAsset(null);
      }
    },
    [products, generationMode]
  );

  const handlePromptChange = useCallback(
    (productId: string, value: PromptWithImages) => {
      setPrompts((prev: Record<string, string | PromptWithImages>) => ({
        ...prev,
        [productId]: value,
      }));
    },
    []
  );

  const handleCopyPromptToAll = useCallback(
    (sourceProductId: string) => {
      const sourcePrompt = prompts[sourceProductId];
      if (sourcePrompt === undefined) return;

      setPrompts((prev: Record<string, string | PromptWithImages>) => {
        const newPrompts: Record<string, string | PromptWithImages> = {
          ...prev,
        };
        products.forEach((p: Product) => {
          newPrompts[p.id] = sourcePrompt;
        });
        return newPrompts;
      });
    },
    [prompts, products]
  );

  // Image attachment handlers for generation composition
  const handleAttachImage = useCallback(
    (
      productId: string,
      image: { id: string; url: string; filename: string; displayName?: string }
    ) => {
      setAttachedImages((prev) => {
        const currentImages = prev[productId] || [];
        // Check if image is already attached
        if (currentImages.some((img) => img.id === image.id)) {
          return prev;
        }
        // Add image if we have space (max 4)
        if (currentImages.length < 4) {
          return {
            ...prev,
            [productId]: [...currentImages, image],
          };
        }
        return prev;
      });
      toast.success(`Image attached for generation composition`);
    },
    []
  );

  const handleDetachImage = useCallback(
    (productId: string, imageId: string) => {
      setAttachedImages((prev) => {
        const currentImages = prev[productId] || [];
        return {
          ...prev,
          [productId]: currentImages.filter((img) => img.id !== imageId),
        };
      });
      toast.success("Image detached");
    },
    []
  );

  const handleGenerate = useCallback(async () => {
    const selectedCount = selectedProductIds.size;
    if (selectedCount === 0) return;

    const modeCap = generationMode === "video" ? 3 : 5;
    if (selectedCount > modeCap) {
      return;
    }

    try {
      setIsInitiating(true);
      setInitiationMessage(
        `Batch started (${selectedCount} items) • Mode: ${generationMode} • Model: ${MODELS.find((m) => m.id === selectedModelId)?.name || selectedModelId}`
      );

      const ids = Array.from(selectedProductIds).slice(0, modeCap);
      const items = ids.map((productId: string) => {
        const product = products.find((p) => p.id === productId);
        const imageAssetIds = (product?.assets || [])
          .filter((a) => a.type === "image")
          .map((a) => a.id);
        const selectedInRow = Array.from(selectedAssetIds).filter((aid) =>
          imageAssetIds.includes(aid)
        );

        const urls: string[] = (() => {
          const productGeneratedImages = generatedImages[productId] || [];
          const allImages = [
            ...(product?.assets.filter((a) => a.type === "image") || []),
            ...productGeneratedImages.filter((a) => a.type === "image"),
          ];

          // Get all selected images for this product
          const selectedImageIds = Array.from(selectedAssetIds).filter((aid) =>
            imageAssetIds.includes(aid)
          );

          // If specific images are selected, use only those
          if (selectedImageIds.length > 0) {
            return allImages
              .filter((img) => selectedImageIds.includes(img.id))
              .map((img) => img.url);
          }

          // Otherwise, use all available images
          return allImages.map((img) => img.url);
        })();

        return {
          productId,
          prompt: (() => {
            const prompt = prompts[productId];
            if (!prompt)
              return `Professional product shot of ${product?.title}`;
            // Handle both string (legacy) and PromptWithImages (new) formats
            return typeof prompt === "string" ? prompt : prompt.getText();
          })(),
          referenceImageUrls: urls,
          storeId: product?.store_id,
        };
      });

      const start = await mediaService.startGeneration({
        mode: generationMode as "image" | "video",
        model: selectedModelId,
        settings: settings,
        items,
      });

      const batchId: string = start.batch_id || start.id;
      setGenerationBatch({
        batchId,
        total: items.length,
        completed: 0,
        failed: 0,
        status: "processing",
      });

      toast.success(`Generation batch started: ${batchId}`);
    } catch (error: any) {
      console.error("Generation failed:", error);
      setErrorMessage(error?.message || "Failed to start generation");
      toast.error("Failed to start generation");
    } finally {
      setTimeout(() => {
        setIsInitiating(false);
        setInitiationMessage(null);
      }, 6000);
    }
  }, [
    selectedProductIds,
    generationMode,
    selectedModelId,
    settings,
    products,
    selectedAssetIds,
    prompts,
    generatedImages,
  ]);

  const clearSelection = useCallback(() => {
    setSelectedAssetIds(new Set());
    setSelectedProductIds(new Set());
    setActiveAsset(null);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        clearSelection();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [clearSelection]);

  const overLimit =
    selectedProductIds.size > (generationMode === "video" ? 3 : 5);
  const overLimitMessage = `Maximum ${generationMode === "video" ? 3 : 5} items allowed for ${generationMode} generation.`;

  return (
    <div className="flex flex-col bg-background font-sans text-foreground h-screen">
      {/* Main Content */}
      <main className="flex flex-1 min-h-0">
        {/* Left Panel (2/3 width) */}
        <div className="w-2/3 flex flex-col border-r border-[#EDEDED] dark:border-border bg-background min-h-0">
          {/* Left Panel Header */}
          <div className="flex-shrink-0 h-12 border-b border-[#EDEDED] dark:border-border bg-card">
            <div className="h-full flex items-center gap-3 px-4">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-8"
                />
              </div>

              <CollectionsFilter
                options={[]}
                selectedIds={collectionFilters}
                onChange={setCollectionFilters}
                compact={true}
              />

              {/* Quick Filters */}
              <div className="flex items-center gap-2">
                <Button
                  variant={filterSelectedOnly ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterSelectedOnly(!filterSelectedOnly)}
                  className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                  aria-pressed={filterSelectedOnly}
                  disabled={isLoadingProducts}
                >
                  Selected
                </Button>
                <Button
                  variant={filterHasGenerated ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterHasGenerated(!filterHasGenerated)}
                  className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
                  aria-pressed={filterHasGenerated}
                  disabled={isLoadingProducts || isLoadingAssets}
                >
                  Generated
                  {isLoadingAssets && (
                    <div className="ml-2 animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Product Grid */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden min-h-0">
            {isLoadingProducts ? (
              // Loading State
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <div className="text-lg font-medium text-foreground mb-2">
                    Loading Products...
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Fetching your product catalog
                  </div>
                </div>
              </div>
            ) : productsError ? (
              // Error State
              <div className="flex items-center justify-center h-full">
                <div className="text-center max-w-md">
                  <div className="text-red-500 text-6xl mb-4">
                    <AlertCircle className="h-16 w-16 mx-auto" />
                  </div>
                  <div className="text-xl font-semibold text-foreground mb-2">
                    Failed to Load Products
                  </div>
                  <div className="text-muted-foreground mb-4">
                    {productsError}
                  </div>
                  <Button
                    onClick={() => window.location.reload()}
                    variant="outline"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            ) : products.length === 0 ? (
              // Empty State
              <div className="flex items-center justify-center h-full">
                <div className="text-center max-w-md">
                  <div className="text-6xl text-muted-foreground mb-4">📦</div>
                  <div className="text-xl font-semibold text-foreground mb-2">
                    No Products Found
                  </div>
                  <div className="text-muted-foreground mb-4">
                    Your store doesn't have any products yet. Add some products
                    to get started with media generation.
                  </div>
                  <Button
                    onClick={() => window.location.reload()}
                    variant="outline"
                  >
                    Refresh
                  </Button>
                </div>
              </div>
            ) : (
              // Products Grid
              <ProductGrid
                products={products}
                selectedAssetIds={selectedAssetIds}
                onAssetSelect={handleAssetSelect}
                prompts={prompts}
                onPromptChange={handlePromptChange}
                onTabChange={setActiveMainTab}
                selectedProductIds={selectedProductIds}
                onProductSelectionChange={handleProductSelectionChange}
                onSelectAllProducts={handleSelectAllProducts}
                onCopyPromptToAll={handleCopyPromptToAll}
                generatedImages={generatedImages}
                availableCollections={[]}
                collectionFilters={collectionFilters}
                onCollectionFiltersChange={setCollectionFilters}
                searchQuery={searchQuery}
                onSearchQueryChange={setSearchQuery}
                filterSelectedOnly={filterSelectedOnly}
                onFilterSelectedOnlyChange={setFilterSelectedOnly}
                filterHasGenerated={filterHasGenerated}
                onFilterHasGeneratedChange={setFilterHasGenerated}
                sortMode={sortMode}
                onSortModeChange={setSortMode}
                productTotalCount={products.length}
                isLoadingAssets={isLoadingAssets}
                attachedImages={attachedImages}
                onAttachImage={handleAttachImage}
                onDetachImage={handleDetachImage}
              />
            )}
          </div>
        </div>

        {/* Right Panel (1/3 width) */}
        <div className="w-1/3 flex flex-col bg-background min-h-0">
          {/* Right Panel Header */}
          <div className="flex-shrink-0 h-12 border-b border-[#EDEDED] dark:border-border bg-card">
            <div className="h-full flex items-center px-4 gap-1">
              {["Gallery", "Models", "Props", "Scenes", "Brandbook"].map(
                (tab) => (
                  <Button
                    key={tab}
                    variant="ghost"
                    onClick={() =>
                      setActiveMainTab(tab.toLowerCase() as MainTab)
                    }
                    className={`h-full -mb-px px-3 py-0 text-sm font-medium transition-colors duration-150 rounded-none border-b-2 border-transparent hover:bg-transparent focus:ring-0 focus:ring-offset-0 ${
                      activeMainTab === tab.toLowerCase()
                        ? "border-primary text-foreground"
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    {tab}
                  </Button>
                )
              )}
            </div>
          </div>

          {/* Right Panel Content */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden min-h-0">
            {activeMainTab === "gallery" ? (
              <PreviewPane
                assets={galleryAssets}
                loading={isLoadingAssets}
                error={assetsError}
                onRefresh={loadGeneratedAssets}
                onLoadMore={loadMoreGalleryAssets}
                hasMore={galleryHasMore}
                isLoadingMore={isLoadingMoreGallery}
              />
            ) : activeMainTab === "models" ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-semibold mb-2">Models</div>
                  <div className="text-muted-foreground">
                    Model selection coming soon
                  </div>
                </div>
              </div>
            ) : activeMainTab === "props" ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-semibold mb-2">Props</div>
                  <div className="text-muted-foreground">
                    Props library coming soon
                  </div>
                </div>
              </div>
            ) : activeMainTab === "scenes" ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-semibold mb-2">Scenes</div>
                  <div className="text-muted-foreground">
                    Scene templates coming soon
                  </div>
                </div>
              </div>
            ) : activeMainTab === "brandbook" ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-semibold mb-2">Brandbook</div>
                  <div className="text-muted-foreground">
                    Brand assets coming soon
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="text-lg font-semibold mb-2">Canvas</div>
                  <div className="text-muted-foreground">Canvas view</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Floating Generate Bar */}
      <FloatingGenerateBar
        selectedCount={selectedProductIds.size}
        generationMode={generationMode}
        onModeChange={setGenerationMode}
        settings={settings}
        setSettings={setSettings}
        selectedModelId={selectedModelId}
        onModelChange={setSelectedModelId}
        onGenerate={handleGenerate}
        generationBatch={generationBatch}
        isInitiating={isInitiating}
        initiationMessage={initiationMessage}
        overLimit={overLimit}
        overLimitMessage={overLimitMessage}
        errorMessage={errorMessage}
        onDismissMessage={() => setErrorMessage(null)}
        onDismissInitiationMessage={() => setInitiationMessage(null)}
      />
    </div>
  );
};

export default MediaStudioPage;
