import { api } from "./api";

export interface Customer {
  id: string;
  external_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  orders_count: number;
  total_spent: number;
  state: string;
  created_at: string;
}

export const customerService = {
  async getCustomers(): Promise<Customer[]> {
    const response = await api.get("/api/customers/");
    return response.data;
  },

  async getCustomer(id: string): Promise<Customer> {
    const response = await api.get(`/api/customers/${id}`);
    return response.data;
  },
};
