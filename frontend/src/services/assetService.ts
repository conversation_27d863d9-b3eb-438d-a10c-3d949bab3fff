import { api } from "./api";

export interface Asset {
  id: string;
  productId: string;
  url: string;
  type: "image" | "video";
  filename: string;
  displayName: string;
  fileUrl?: string;
  previewUrl?: string;
  prompt?: string;
  sourceType?: "product" | "ai_generated";
}

export interface MediaItem {
  type: string;
  id: string;
  src: string;
  alt: string;
  path?: string;
  key?: string;
  data?: any;
  source_type?: string;
}

export interface PaginatedAssetResponse {
  items: MediaItem[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export class AssetService {
  /**
   * Get all assets for the current user
   */
  async getAssets(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<PaginatedAssetResponse> {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.set("page", params.page.toString());
    if (params?.limit) searchParams.set("limit", params.limit.toString());
    if (params?.search) searchParams.set("search", params.search);

    const response = await api.get(`/api/assets?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get assets for specific products
   */
  async getAssetsForProducts(
    productIds: string[],
    params?: {
      limit?: number;
    }
  ): Promise<Asset[]> {
    const searchParams = new URLSearchParams();

    if (params?.limit) searchParams.set("limit", params.limit.toString());
    searchParams.set("product_ids", productIds.join(","));

    const response = await api.get(`/api/assets?${searchParams.toString()}`);

    // Transform MediaItem[] to Asset[]
    return (
      response.data.items?.map((item: MediaItem) => ({
        id: item.id,
        productId: item.data?.product_id || "",
        url: item.src,
        type: item.type as "image" | "video",
        filename: item.src?.split("/").pop() || "asset.jpg",
        displayName: item.alt,
        fileUrl: item.src,
        previewUrl: item.data?.preview_uri,
        prompt: item.data?.prompt,
        sourceType: (item.source_type || item.data?.source_type) as
          | "product"
          | "ai_generated",
      })) || []
    );
  }

  /**
   * Reassign an asset to a different product
   */
  async reassignAsset(
    assetId: string,
    newProductId: string
  ): Promise<{ success: boolean; message: string }> {
    const response = await api.post("/api/assets/reassign", {
      asset_id: assetId,
      new_product_id: newProductId,
    });
    return response.data;
  }
}

export const assetService = new AssetService();
