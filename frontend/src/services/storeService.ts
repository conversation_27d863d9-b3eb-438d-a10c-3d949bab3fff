import { api } from "./api";

export interface Store {
  id: string;
  platform: string;
  shop_domain?: string;
  shop_name?: string;
  is_active: boolean;
  last_sync: string | null;
  airbyte_source_id?: string | null;
  airbyte_destination_id?: string | null;
  airbyte_connection_id?: string | null;
  admin_access_token?: string; // For store responses
  has_access_token?: boolean; // For domain lookup responses
}

export interface StoreCreate {
  admin_access_token: string;
  storefront_access_token?: string;
}

export interface ConnectionTest {
  success: boolean;
  message: string;
  store_info?: any;
}

export const storeService = {
  async getStores(): Promise<Store[]> {
    const response = await api.get("/api/stores/");
    return response.data;
  },

  async getStore(storeId: string): Promise<Store> {
    const response = await api.get(`/api/stores/${storeId}`);
    return response.data;
  },

  async createStore(store: StoreCreate): Promise<Store> {
    const response = await api.post("/api/stores/", store);
    return response.data;
  },

  async testConnection(store: StoreCreate): Promise<ConnectionTest> {
    const response = await api.post("/api/stores/test-connection", store);
    return response.data;
  },

  async testConnectionByCredentials(credentials: {
    platform?: string;
    shop_domain: string;
    admin_access_token: string;
    storefront_access_token?: string;
  }): Promise<ConnectionTest> {
    const response = await api.post("/api/stores/test-connection", credentials);
    return response.data;
  },

  async syncStore(storeId: string): Promise<any> {
    const response = await api.post(`/api/stores/${storeId}/sync`);
    return response.data;
  },

  async toggleStoreActivation(storeId: string): Promise<Store> {
    const response = await api.post(`/api/stores/${storeId}/toggle-activation`);
    return response.data;
  },

  async connectStore(storeId: string): Promise<any> {
    const response = await api.post(`/api/stores/${storeId}/connect`);
    return response.data;
  },

  async getStoreByDomain(domain: string): Promise<Store | null> {
    try {
      const response = await api.get(`/api/stores/by-domain/${domain}`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null; // Store not found
      }
      throw error;
    }
  },

  async getStoreAccessToken(domain: string): Promise<{
    admin_access_token: string;
    storefront_access_token?: string;
    shop_name?: string;
  } | null> {
    try {
      const response = await api.get(`/api/stores/by-domain/${domain}/token`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null; // Token not found
      }
      throw error;
    }
  },

  async testStoreConnection(storeId: string): Promise<any> {
    const response = await api.post(`/api/stores/${storeId}/test-connection`);
    return response.data;
  },

  async getConnectionStatus(): Promise<{
    isConnected: boolean;
    stores: Array<{
      id: string;
      domain: string;
      productCount: number;
      videosGenerated: number;
      lastSync?: string;
    }>;
  }> {
    try {
      // Try to get connected stores first
      const storesResponse = await api.get("/api/stores/");
      const stores = storesResponse.data || [];

      // Check if any stores are active
      const activeStores = stores.filter((store: any) => store.is_active);

      return {
        isConnected: activeStores.length > 0,
        stores: activeStores.map((store: any) => ({
          id: store.id.toString(),
          domain: store.shop_domain,
          productCount: 0, // We'll need to implement this
          videosGenerated: 0, // We'll need to implement this
          lastSync: store.last_sync,
        })),
      };
    } catch (error) {
      console.error("Failed to get connection status:", error);
      return {
        isConnected: false,
        stores: [],
      };
    }
  },

  async disconnectStore(storeId: string): Promise<any> {
    const response = await api.post(`/api/stores/${storeId}/disconnect`);
    return response.data;
  },
};
