/**
 * Video Generation Service - ProductVideo API client
 */

import { api } from "./api";

// Additional types for the new components
export interface VideoGenerationRequest {
  productIds: string[];
  templateId: string;
  aspectRatio: string;
  ctaText: string;
}

// Types for ProductVideo API
export interface GenerateVideoRequest {
  shop_id: string;
  product_ids: string[];
  template_id?: string;
  aspect_ratio?: string;
  locale?: string;
}

export interface VideoVariant {
  variant_id: string;
  variant_name: string;
  status: string;
  video_url?: string;
  thumbnail_url?: string;
  duration?: number;
}

export interface VideoJob {
  job_id: string;
  status: string;
  progress: number;
  variants: VideoVariant[];
}

export interface PushToShopifyRequest {
  shop_id: string;
  product_id: string;
  variant_id: string;
  publish_targets?: string[];
  publish_options?: {
    alt_text?: string;
    position?: number;
  };
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  preview_url?: string;
  category?: string;
}

export interface AnalyticsMetrics {
  views: number;
  plays: number;
  completion_rate: number;
  avg_watch_time: number;
  ctr: number;
  conversions: number;
  conversion_lift?: number;
}

export interface ProductAnalytics {
  product_id: string;
  variant_id?: string;
  metrics: AnalyticsMetrics;
  period: {
    from: string;
    to: string;
  };
}

/**
 * Video Generation API Service
 */
export class VideoService {
  /**
   * Get product analytics
   */
  static async getProductAnalytics(
    productId: string,
    fromDate?: string,
    toDate?: string,
    variantId?: string
  ): Promise<ProductAnalytics> {
    const params = new URLSearchParams();
    if (fromDate) params.append("from_date", fromDate);
    if (toDate) params.append("to_date", toDate);
    if (variantId) params.append("variant_id", variantId);

    const response = await api.get(
      `/api/analytics/product/${productId}?${params}`
    );
    return response.data;
  }

  /**
   * Get dashboard metrics
   */
  static async getDashboardMetrics(days: number = 30) {
    const response = await api.get(`/api/analytics/dashboard?days=${days}`);
    return response.data;
  }

  /**
   * Generate videos with new interface
   */
  static async generateVideos(request: VideoGenerationRequest) {
    const response = await api.post("/api/video-generation/generate", request);
    return response.data;
  }

  /**
   * Get video variants
   */
  static async getVariants() {
    const response = await api.get("/api/video-generation/variants");
    return response.data;
  }

  /**
   * Get variants by IDs
   */
  static async getVariantsByIds(variantIds: string[]) {
    const response = await api.post("/api/video-generation/variants/by-ids", {
      variantIds,
    });
    return response.data;
  }

  /**
   * Toggle favorite status
   */
  static async toggleFavorite(variantId: string) {
    const response = await api.post(
      `/api/video-generation/variants/${variantId}/favorite`
    );
    return response.data;
  }

  /**
   * Regenerate variant
   */
  static async regenerateVariant(variantId: string) {
    const response = await api.post(
      `/api/video-generation/variants/${variantId}/regenerate`
    );
    return response.data;
  }

  /**
   * Track video event
   */
  static async trackEvent(event: {
    variantId: string;
    eventType: string;
    timestamp: string;
    sessionId: string;
  }) {
    const response = await api.post("/api/analytics/events/ingest", {
      event_type: event.eventType,
      video_variant_id: event.variantId,
      session_id: event.sessionId,
      timestamp: event.timestamp,
      dedup_token: `${event.sessionId}-${event.variantId}-${event.eventType}-${Date.now()}`,
    });
    return response.data;
  }

  /**
   * Push to Shopify with new interface
   */
  static async pushToShopify(params: {
    variantIds: string[];
    altTexts: Record<string, string>;
    mediaPositions: Record<string, number>;
    replaceExisting: boolean;
  }) {
    const response = await api.post(
      "/api/video-generation/push-to-shopify",
      params
    );
    return response.data;
  }

  /**
   * Get push job status
   */
  static async getPushJobStatus(jobId: string) {
    const response = await api.get(
      `/api/video-generation/push-jobs/${jobId}/status`
    );
    return response.data;
  }

  /**
   * Get gallery items
   */
  static async getGallery(params: {
    search?: string;
    sortBy?: string;
    status?: string;
    tag?: string;
    page?: number;
    limit?: number;
  }) {
    const response = await api.get("/api/video-generation/gallery", { params });
    return response.data;
  }

  /**
   * Get gallery tags
   */
  static async getGalleryTags() {
    const response = await api.get("/api/video-generation/gallery/tags");
    return response.data;
  }

  /**
   * Create bulk download
   */
  static async createBulkDownload(variantIds: string[]) {
    const response = await api.post("/api/video-generation/bulk-download", {
      variantIds,
    });
    return response.data.downloadUrl;
  }

  /**
   * Bulk delete variants
   */
  static async bulkDelete(variantIds: string[]) {
    const response = await api.delete("/api/video-generation/variants/bulk", {
      data: { variantIds },
    });
    return response.data;
  }

  /**
   * Get analytics dashboard data
   */
  static async getAnalytics(params: { timeRange: string; productId?: string }) {
    const response = await api.get("/api/analytics/dashboard", { params });
    return response.data;
  }

  /**
   * Get analytics products
   */
  static async getAnalyticsProducts() {
    const response = await api.get("/api/analytics/products");
    return response.data;
  }
}

export default VideoService;
