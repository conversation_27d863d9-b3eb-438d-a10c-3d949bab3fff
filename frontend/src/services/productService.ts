import { api } from "./api";

// Import MediaItem type from media-viewer component
export interface MediaItem {
  type:
    | "image"
    | "video"
    | "featured_image"
    | "featured_video"
    | "detected_image"
    | "detected_video";
  id: string;
  src: string;
  alt: string;
  path?: string;
  key?: string;
  data?: any;
}

export interface Metafield {
  id?: string;
  namespace: string;
  key: string;
  value: string;
  value_type: string;
  description?: string;
  owner_id?: string;
  created_at?: string;
  updated_at?: string;
  owner_resource?: string;
}

export interface ProductVariant {
  id: string;
  external_id: string;
  product_id: string;
  title?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  compare_at_price?: number;
  cost?: number;
  weight?: number;
  weight_unit: string;
  quantity: number;
  inventory_policy?: string;
  inventory_item_id?: string;
  option1?: string;
  option2?: string;
  option3?: string;
  taxable: boolean;
  requires_shipping: boolean;
  fulfillment_service?: string;
  available_for_sale: boolean;
  full_json?: string;
  metafields?: Metafield[];
  created_at: string;
  updated_at: string;
  source_updated_at?: string;
}

export interface ProductImage {
  id: string;
  external_id: string;
  product_id: string;
  variant_id?: string;
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  position: number;
  full_json?: string;
  metafields?: Metafield[];
  created_at: string;
  updated_at: string;
  source_updated_at?: string;
}

export interface Product {
  id: string;
  external_id: string;
  title: string;
  handle?: string;
  vendor?: string;
  product_type?: string;
  status: string;
  published: boolean;
  description?: string;
  tags?: string; // JSON string
  options?: string; // JSON string
  seo?: string; // JSON string
  metafields?: Metafield[]; // Parsed metafields
  collections?: string; // JSON string
  full_json?: string; // Complete raw data from platform
  featured_media?: string; // JSON: Featured media from Shopify (can be image or video)
  store_id: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
  source_updated_at?: string;

  // Relationships
  variants?: ProductVariant[];
  images?: ProductImage[];

  // Computed fields for list view
  variant_count?: number;
  image_count?: number;

  // Assets (media items)
  assets?: MediaItem[];
}

export interface SyncStatus {
  store_id: string;
  sync_in_progress: boolean;
  active_job?: {
    id: string;
    status: string;
    started_at?: string;
    entity_type: string;
  };
  lock_available: boolean;
}

export interface SyncResult {
  message: string;
  task_id: string;
  store_id: string;
}

export interface PaginatedProductsResponse {
  items: Product[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Helper function to parse metafields JSON string
const parseMetafields = (metafieldsRaw?: string): Metafield[] | undefined => {
  if (!metafieldsRaw) return undefined;

  try {
    const parsed = JSON.parse(metafieldsRaw);

    // Handle nested namespace/key structure from database
    if (parsed && typeof parsed === "object" && !Array.isArray(parsed)) {
      const metafields: Metafield[] = [];

      // Iterate through namespaces (custom, global, etc.)
      Object.entries(parsed).forEach(
        ([namespace, namespaceData]: [string, any]) => {
          if (namespaceData && typeof namespaceData === "object") {
            // Iterate through keys within each namespace
            Object.entries(namespaceData).forEach(
              ([key, fieldData]: [string, any]) => {
                if (fieldData && typeof fieldData === "object") {
                  metafields.push({
                    namespace,
                    key,
                    value: fieldData.value || "",
                    value_type: fieldData.type || "string",
                    updated_at: fieldData.updated_at,
                    description: fieldData.description,
                  });
                }
              }
            );
          }
        }
      );

      return metafields.length > 0 ? metafields : undefined;
    }

    // Handle legacy array format
    if (Array.isArray(parsed)) {
      return parsed;
    }

    // Handle single object format
    if (parsed && typeof parsed === "object") {
      return [parsed];
    }

    return undefined;
  } catch (error) {
    console.warn(
      "Failed to parse metafields:",
      error,
      "Raw data:",
      metafieldsRaw
    );
    return undefined;
  }
};

// Helper function to process product data and parse metafields
const processProductData = (product: any): Product => {
  return {
    ...product,
    metafields: parseMetafields(product.metafields),
    variants: product.variants?.map((variant: any) => ({
      ...variant,
      metafields: parseMetafields(variant.metafields),
    })),
    images: product.images?.map((image: any) => ({
      ...image,
      metafields: parseMetafields(image.metafields),
    })),
  };
};

export const productService = {
  async getProducts(
    page: number = 1,
    limit: number = 50,
    search?: string
  ): Promise<PaginatedProductsResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (search && search.trim()) {
      params.append("search", search.trim());
    }

    const response = await api.get(`/api/products/?${params.toString()}`);

    // Process the response data to parse metafields
    const processedData = {
      ...response.data,
      items: response.data.items?.map(processProductData) || [],
    };

    return processedData;
  },

  async getProduct(id: string): Promise<Product> {
    const response = await api.get(`/api/products/${id}`);
    return processProductData(response.data);
  },

  async getSyncStatus(
    storeId: string
  ): Promise<{ store_id: string; sync_status: SyncStatus }> {
    const response = await api.get(
      `/api/stores/${storeId}/sync-progress/products`
    );
    return response.data;
  },
};
