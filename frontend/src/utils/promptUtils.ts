import type {
  PromptWithImages,
  PromptSegment,
  AttachedImage,
} from "@/types/mediaStudio";
import type { Asset } from "@/services/mediaService";

// Enhanced mention regex for Phase 3
const MENTION_REGEX = /@([a-zA-Z0-9][-a-zA-Z0-9_]*)/g;

/**
 * Create an empty prompt with no content
 */
export function createEmptyPrompt(): PromptWithImages {
  return {
    segments: [{ type: "text", content: "" }],
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text"
            ? segment.content
            : `@${segment.image?.displayName || segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

/**
 * Create a prompt from plain text, parsing any existing @mentions
 */
export function createPromptFromText(text: string): PromptWithImages {
  if (!text.trim()) {
    return createEmptyPrompt();
  }

  // Simple parsing - split on @mentions but keep them
  const segments: PromptSegment[] = [];
  const parts = text.split(/(@\w+)/g);

  for (const part of parts) {
    if (part.startsWith("@")) {
      // This is a mention - we'll need to resolve it later with actual asset data
      const mentionText = part.substring(1);
      segments.push({
        type: "mention",
        content: mentionText,
        // image will be populated when we have asset context
      });
    } else if (part) {
      // Regular text
      segments.push({
        type: "text",
        content: part,
      });
    }
  }

  return {
    segments:
      segments.length > 0 ? segments : [{ type: "text", content: text }],
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text"
            ? segment.content
            : `@${segment.image?.displayName || segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

/**
 * Phase 3: Enhanced parsing with better mention detection
 */
export function parseTextWithMentions(
  text: string,
  availableImages: AttachedImage[]
): PromptWithImages {
  if (!text.trim()) {
    return createEmptyPrompt();
  }

  const segments: PromptSegment[] = [];
  let lastIndex = 0;

  // Find all mentions using enhanced regex
  const mentions = Array.from(text.matchAll(MENTION_REGEX));

  for (const match of mentions) {
    const [fullMatch, mentionName] = match;
    const start = match.index!;

    // Add text before mention
    if (start > lastIndex) {
      segments.push({
        type: "text",
        content: text.substring(lastIndex, start),
      });
    }

    // Find matching image with flexible matching
    const matchedImage = availableImages.find((img) => {
      const imgName = img.displayName?.toLowerCase().replace(/\s+/g, "-") || "";
      const fileName = img.filename.toLowerCase().replace(/\.[^/.]+$/, ""); // Remove extension
      const mentionLower = mentionName.toLowerCase();

      return (
        imgName === mentionLower ||
        fileName === mentionLower ||
        imgName.includes(mentionLower) ||
        fileName.includes(mentionLower)
      );
    });

    segments.push({
      type: "mention",
      content: mentionName,
      image: matchedImage,
    });

    lastIndex = start + fullMatch.length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    segments.push({
      type: "text",
      content: text.substring(lastIndex),
    });
  }

  return createPromptFromSegments(segments);
}

/**
 * Helper to create prompt from segments
 */
function createPromptFromSegments(segments: PromptSegment[]): PromptWithImages {
  return {
    segments: segments.length > 0 ? segments : [{ type: "text", content: "" }],
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text"
            ? segment.content
            : `@${segment.image?.displayName || segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

/**
 * Update the text content of a prompt while preserving mentions
 */
export function updatePromptText(
  prompt: PromptWithImages,
  newText: string
): PromptWithImages {
  return createPromptFromText(newText);
}

/**
 * Insert a mention at a specific position in the prompt
 */
export function insertMentionAtPosition(
  prompt: PromptWithImages,
  position: number,
  image: AttachedImage
): PromptWithImages {
  const currentText = prompt.getText();
  const beforeText = currentText.substring(0, position);
  const afterText = currentText.substring(position);

  const mentionText = `@${image.displayName || image.filename}`;
  const newText = beforeText + mentionText + afterText;

  const newPrompt = createPromptFromText(newText);

  // Now populate the image data for the new mention
  return populateImageData(newPrompt, [image]);
}

/**
 * Remove a mention by asset ID
 */
export function removeMention(
  prompt: PromptWithImages,
  assetId: string
): PromptWithImages {
  const newSegments: PromptSegment[] = [];

  for (const segment of prompt.segments) {
    if (segment.type === "mention" && segment.image?.assetId === assetId) {
      // Skip this mention
      continue;
    }
    newSegments.push(segment);
  }

  return {
    segments: newSegments,
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text"
            ? segment.content
            : `@${segment.image?.displayName || segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

/**
 * Populate image data for mentions in a prompt
 */
export function populateImageData(
  prompt: PromptWithImages,
  availableAssets: AttachedImage[]
): PromptWithImages {
  const assetMap = new Map(
    availableAssets.map((asset) => [asset.assetId, asset])
  );

  const newSegments = prompt.segments.map((segment) => {
    if (segment.type === "mention") {
      // Try to find matching asset by display name or filename
      const matchingAsset = availableAssets.find(
        (asset) =>
          asset.displayName === segment.content ||
          asset.filename === segment.content ||
          asset.assetId === segment.content
      );

      return {
        ...segment,
        image: matchingAsset,
      };
    }
    return segment;
  });

  return {
    segments: newSegments,
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text"
            ? segment.content
            : `@${segment.image?.displayName || segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

/**
 * Convert assets to AttachedImage format with product context
 */
export function assetToAttachedImage(
  asset: Asset,
  productTitle?: string
): AttachedImage {
  return {
    assetId: asset.id,
    filename: asset.filename,
    displayName: asset.displayName || generateDisplayName(asset, productTitle),
    url: asset.url,
    productId: asset.productId,
  };
}

/**
 * Generate a user-friendly display name for an asset with product context
 */
export function generateDisplayName(
  asset: Asset,
  productTitle?: string
): string {
  // Use existing displayName if available
  if (asset.displayName) {
    return asset.displayName;
  }

  // Extract meaningful name from filename
  let name = asset.filename;

  // Remove file extension
  name = name.replace(/\.[^/.]+$/, "");

  // Replace common separators with spaces
  name = name.replace(/[-_]/g, " ");

  // Capitalize first letter of each word
  name = name.replace(/\b\w/g, (l) => l.toUpperCase());

  // If we have product context, create a more descriptive name
  if (productTitle) {
    // Extract product number/code from title (e.g., "063" from "063 T-shirt...")
    const productMatch = productTitle.match(/^(\d+)\s+(.+)/);
    if (productMatch) {
      const [, productCode, productName] = productMatch;
      // Truncate product name if too long
      const shortProductName =
        productName.length > 25
          ? productName.substring(0, 22) + "..."
          : productName;
      return `${productCode} ${shortProductName} / ${name}`;
    } else {
      // Fallback: use first few words of product title
      const shortTitle = productTitle.split(" ").slice(0, 3).join(" ");
      const truncatedTitle =
        shortTitle.length > 20
          ? shortTitle.substring(0, 17) + "..."
          : shortTitle;
      return `${truncatedTitle} / ${name}`;
    }
  }

  // Truncate if too long (fallback)
  if (name.length > 20) {
    name = name.substring(0, 17) + "...";
  }

  return name;
}
