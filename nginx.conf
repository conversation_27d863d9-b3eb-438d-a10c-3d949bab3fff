events {
    worker_connections 1024;
}

http {
    include       mime.types; # Nginx default mime types
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting (from backend/nginx/nginx.conf)
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=webhooks:10m rate=100r/s;

    # Upstream backend servers (from backend/nginx/nginx.conf)
    upstream backend {
        server api:8123; # Use 'api' as service name from root docker-compose.yml
        keepalive 32;
    }

    # Frontend server block (adapted from frontend/nginx.conf)
    server {
        listen 80;
        server_name localhost; # Or your domain name

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

        # Proxy to frontend service
        location / {
            proxy_pass http://app:3000; # Use 'app' as service name from root docker-compose.yml
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_redirect off;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static assets (from frontend/nginx.conf)
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            proxy_pass http://app:3000; # Serve static assets from frontend service
        }

        # API endpoints (from backend/nginx/nginx.conf)
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }

        # Webhook endpoints (higher rate limit) (from backend/nginx/nginx.conf)
        location /api/plugins/shopify/webhooks {
            limit_req zone=webhooks burst=50 nodelay;

            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Longer timeout for webhooks
            proxy_read_timeout 60s;
        }

        # Static files (from backend/nginx/nginx.conf)
        location /static/ {
            alias /var/www/static/; # This path needs to be mounted from backend
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Video files (with range support) (from backend/nginx/nginx.conf)
        # Note: MinIO service not currently configured in docker-compose.yml
        # location /videos/ {
        #     proxy_pass http://minio:9000/ecommerce-videos/;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        #
        #     # Enable range requests for video streaming
        #     proxy_set_header Range $http_range;
        #     proxy_set_header If-Range $http_if_range;
        #     proxy_no_cache $http_range $http_if_range;
        #
        #     # Cache video files
        #     expires 1d;
        #     add_header Cache-Control "public";
        # }

        # Admin panel (basic auth protection) (from backend/nginx/nginx.conf)
        location /admin/ {
            auth_basic "Admin Area";
            auth_basic_user_file /etc/nginx/.htpasswd; # This file needs to be mounted
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Monitoring endpoints (from backend/nginx/nginx.conf)
        location /metrics {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Restrict access to monitoring
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
    }
}
