import google.genai as genai
import os
from google.genai.types import Image

# --- Configuration ---
# Replace with your actual API key
API_KEY = os.getenv("GEMINI_API_KEY")
if not API_KEY:
    raise ValueError("GEMINI_API_KEY environment variable not set.")
client = genai.Client(api_key=API_KEY)
# Replace with the path to your headphone image file
HEADPHONE_IMAGE_PATH = "headphone.avif" # or .jpeg

# Replace with the desired output video file name
OUTPUT_VIDEO_PATH = "video_wearing_headphones.mp4"

# --- Main Logic ---
def generate_video_with_image(image_path: str, prompt_text: str):
    """
    Generates a video using an input image and a text prompt.
    """
    try:
        # Read the local image file directly
        print(f"Reading local image file: {image_path}...")
        with open(image_path, 'rb') as f:
            image_data = f.read()

        # Create an Image object with the binary data and mime type
        # Assuming the mime type for .avif is image/avif
        input_image = Image(image_bytes=image_data, mime_type='image/avif')

        # 2. Call the generate_videos method on the client
        # The new SDK uses client.models.generate_videos
        operation = client.models.generate_videos(
            model='veo-3.0-generate-001',
            prompt=prompt_text,
            image=input_image # Use the directly loaded image
        )

        print("Waiting for video generation to complete...")

        # 3. Poll the operation status until the video is ready
        while not operation.done:
            # You should poll with a delay to avoid rate limiting
            import time
            time.sleep(10)
            operation = client.operations.get(operation)

        # 4. Download and save the video
        if operation.response and operation.response.generated_videos:
            video_file = operation.response.generated_videos[0].video
            
            # Use the new download method to get the video bytes
            video_bytes = client.files.download(file=video_file)

            with open(OUTPUT_VIDEO_PATH, 'wb') as f:
                f.write(video_bytes)

            print(f"Video saved successfully to {OUTPUT_VIDEO_PATH}")
            print("You can now play this MP4 file.")
        else:
            print("Error: Video generation failed or no video was returned.")

    except Exception as e:
        print(f"An error occurred: {e}")

# --- Example Usage ---
if __name__ == "__main__":
    # Define your descriptive prompt
    video_prompt = "A close-up shot of the provided headphones placed on a sleek wooden desk in a modern studio, with soft studio lighting highlighting the details, captured in a static composition."
    
    # Run the function
    generate_video_with_image(HEADPHONE_IMAGE_PATH, video_prompt)