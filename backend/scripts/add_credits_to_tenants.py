#!/usr/bin/env python3
"""
Script to add credits to all existing tenants.
Usage: python scripts/add_credits_to_tenants.py
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.db.database import SessionLocal
from sqlalchemy import text
from datetime import datetime, timedelta


def add_credits_to_all_tenants(credit_amount: int = 10000):
    """
    Add credits to all existing tenants.

    Args:
        credit_amount: Number of credits to add to each tenant
    """
    print(f"Adding {credit_amount} credits to all tenants...")

    try:
        db = SessionLocal()

        # Calculate expiration date (1 year from now)
        expires_at = datetime.utcnow() + timedelta(days=365)

        # Update all tenants with credits
        update_query = text("""
            UPDATE tenants
            SET credits = COALESCE(credits, 0) + :credit_amount,
                credit_expires_at = GREATEST(COALESCE(credit_expires_at, :expires_at), :expires_at)
            WHERE is_active = true
        """)

        result = db.execute(update_query, {
            "credit_amount": credit_amount,
            "expires_at": expires_at
        })

        updated_count = result.rowcount
        print(f"Updated {updated_count} tenants with credits")

        # Insert transaction records for each tenant
        if updated_count > 0:
            # Get all tenant IDs
            tenant_query = text("SELECT id, name FROM tenants WHERE is_active = true")
            tenant_result = db.execute(tenant_query)
            tenants = tenant_result.fetchall()

            # Insert transactions
            for tenant in tenants:
                insert_query = text("""
                    INSERT INTO credit_transactions
                    (tenant_id, transaction_type, amount, balance_after, description, resource_type, expires_at, transaction_metadata, created_at)
                    VALUES (:tenant_id, :transaction_type, :amount, :balance_after, :description, :resource_type, :expires_at, :transaction_metadata, :created_at)
                """)

                db.execute(insert_query, {
                    "tenant_id": tenant.id,
                    "transaction_type": "ADJUSTMENT",
                    "amount": credit_amount,
                    "balance_after": credit_amount,  # Simplified - actual balance would need calculation
                    "description": f"System credit grant: {credit_amount} credits",
                    "resource_type": "system_grant",
                    "expires_at": expires_at,
                    "transaction_metadata": f'{{"grant_type": "system_bootstrap", "credit_amount": {credit_amount}}}',
                    "created_at": datetime.utcnow()
                })

        db.commit()
        db.close()

        print(f"\n✅ Successfully added {credit_amount} credits to {updated_count} tenants")

    except Exception as e:
        print(f"❌ Error: {e}")
        raise


def main():
    """Main function."""
    if len(sys.argv) > 1:
        try:
            credit_amount = int(sys.argv[1])
        except ValueError:
            print("Error: Credit amount must be an integer")
            sys.exit(1)
    else:
        credit_amount = 10000

    add_credits_to_all_tenants(credit_amount)


if __name__ == "__main__":
    main()