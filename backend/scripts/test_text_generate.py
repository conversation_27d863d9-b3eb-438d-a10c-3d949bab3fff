#!/usr/bin/env python3
"""
Standalone script to test gemini text provider and generate real text files.
This script tests the gemini text provider for manual verification.
"""

import os
import json
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any
import time

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env")

# Add src directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set up logging
from core.utils.logging import setup_logging
setup_logging("text_test.log")

from modules.media.providers.config import ProviderConfig
from modules.media.providers.text.gemini import GeminiProvider
from modules.media.schemas import ProviderMediaRequest, ProductContext, ProductCategory


def save_text_file(file_name: str, text: str):
    """Save text content to file."""
    with open(file_name, "w", encoding="utf-8") as f:
        f.write(text)
    print(f"✓ Text file saved to: {file_name}")


def create_provider_config(provider_name: str) -> ProviderConfig:
    """Create provider configuration from the config file."""
    config_file = Path(__file__).parent.parent / "src" / "core" / "configs" / "providers_config.json"

    with open(config_file, "r") as f:
        config_data = json.load(f)

    if provider_name not in config_data["providers"]:
        raise ValueError(f"Provider {provider_name} not found in config")

    provider_config = config_data["providers"][provider_name]
    return ProviderConfig.from_dict(provider_name, provider_config)


def create_sample_request(media_type: str, product_title: str = None, variants_count: int = 2) -> ProviderMediaRequest:
    """Create a sample media request for testing."""
    product_context = ProductContext(
        title=product_title or "Premium Wireless Headphones",
        description="High-quality wireless headphones with active noise cancellation, premium sound quality, and 30-hour battery life. Perfect for music lovers and professionals.",
        category=ProductCategory.ELECTRONICS,
        brand="TechSound",
        colors=["black", "white", "silver"],
        materials=["plastic", "metal", "leather"],
        price=299.99,
        currency="USD",
        price_tier="premium",
        key_features=[
            "Active noise cancellation",
            "30-hour battery life",
            "Premium sound quality",
            "Comfortable fit for extended use",
            "Wireless connectivity",
            "Built-in microphone"
        ],
        benefits=[
            "Immersive audio experience",
            "Long-lasting battery for all-day use",
            "Comfortable for extended listening sessions",
            "Crystal clear calls with built-in mic"
        ]
    )

    request_params = {
        "product_title": product_title or "Premium Wireless Headphones",
        "media_type": media_type,
        "product_context": product_context,
        "aspect_ratio": "1:1",
        "language": "en",
        "variants_count": variants_count
    }

    return ProviderMediaRequest(**request_params)


async def test_text_provider(output_dir: Path, product_title: str = None, variants_count: int = 2):
    """Test gemini text provider."""
    print("\n" + "="*60)
    print("📝 TESTING GEMINI TEXT PROVIDER")
    print("="*60)

    try:
        # Create provider and config
        provider = GeminiProvider()
        config = create_provider_config("gemini")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Create request
        request = create_sample_request("text", product_title, variants_count)
        print(f"📝 Generating text content for: {request.product_title}")

        # Generate text
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Text generation failed: {result.error_message}")
            return False

        print(f"✅ Generated {len(result.variants)} text variants successfully")

        # Save generated text
        for i, variant in enumerate(result.variants):
            content_type = variant.get("content_type", "unknown")
            text_content = variant.get("text", "")

            file_name = output_dir / f"gemini_text_{content_type}_{i}.txt"
            save_text_file(str(file_name), text_content)
            print(f"   📄 Text variant {i} ({content_type}): {file_name}")

            # Also save as JSON with metadata
            json_file = output_dir / f"gemini_text_{content_type}_{i}.json"
            with open(json_file, "w") as f:
                json.dump(variant, f, indent=2)

        # Save metadata
        metadata_file = output_dir / "gemini_metadata.json"
        with open(metadata_file, "w") as f:
            json.dump({
                "provider": "gemini",
                "product": request.product_title,
                "variants_generated": len(result.variants),
                "result": result.dict() if hasattr(result, 'dict') else str(result)
            }, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error testing text provider: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test gemini text provider and generate real files")
    parser.add_argument("--product-title", type=str,
                        help="Product title for text generation")
    parser.add_argument("--variants-count", type=int, default=2,
                        help="Number of text variants to generate")
    parser.add_argument("--output-dir", type=str,
                        help="Output directory for generated files")

    args = parser.parse_args()

    # Create output directory
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = Path(f"./scripts/outputs/text_run_{int(time.time())}")
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🚀 Gemini Text Provider Test")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"🎯 Product: {args.product_title or 'Premium Wireless Headphones'}")
    print(f"📝 Variants to generate: {args.variants_count}")

    # Check environment variables
    if not os.environ.get("GEMINI_API_KEY"):
        print("⚠️  Warning: GEMINI_API_KEY not set")
        print("   Set the environment variable to run the test")
        print("   Example: export GEMINI_API_KEY='your-key-here'")
        return

    # Test text provider
    success = await test_text_provider(output_dir, args.product_title, args.variants_count)

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"TEXT: {status}")

    if success:
        print("🎉 Test passed! Check the output directory for generated files.")
    else:
        print("⚠️  Test failed. Check the output above for details.")

    print(f"📁 Generated files are in: {output_dir.absolute()}")


if __name__ == "__main__":
    asyncio.run(main())