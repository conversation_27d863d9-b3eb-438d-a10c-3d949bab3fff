#!/usr/bin/env python3
"""
Standalone script to test veo3 video provider and generate real video files.
This script tests the veo3 video provider for manual verification.
"""

import os
import json
import base64
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any
import time

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env")

# Add src directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set up logging
from core.utils.logging import setup_logging
setup_logging("video_test.log")

from modules.media.providers.config import ProviderConfig
from modules.media.providers.video.veo3 import Veo3Provider
from modules.media.schemas import ProviderMediaRequest, ProductContext, ProductCategory, ImageInput


def save_binary_file(file_name: str, data: bytes):
    """Save binary data to file."""
    with open(file_name, "wb") as f:
        f.write(data)
    print(f"✓ File saved to: {file_name}")


def create_provider_config(provider_name: str) -> ProviderConfig:
    """Create provider configuration from the config file."""
    config_file = Path(__file__).parent.parent / "src" / "core" / "configs" / "providers_config.json"

    with open(config_file, "r") as f:
        config_data = json.load(f)

    if provider_name not in config_data["providers"]:
        raise ValueError(f"Provider {provider_name} not found in config")

    provider_config = config_data["providers"][provider_name]
    return ProviderConfig.from_dict(provider_name, provider_config)


def create_sample_request(media_type: str, product_title: str = None, variants_count: int = 1) -> ProviderMediaRequest:
    """Create a sample media request for testing."""
    product_context = ProductContext(
        title=product_title or "Premium Wireless Headphones",
        description="High-quality wireless headphones with active noise cancellation, premium sound quality, and 30-hour battery life. Perfect for music lovers and professionals.",
        category=ProductCategory.ELECTRONICS,
        brand="TechSound",
        colors=["black", "white", "silver"],
        materials=["plastic", "metal", "leather"],
        price=299.99,
        currency="USD",
        price_tier="premium",
        key_features=[
            "Active noise cancellation",
            "30-hour battery life",
            "Premium sound quality",
            "Comfortable fit for extended use",
            "Wireless connectivity",
            "Built-in microphone"
        ],
        benefits=[
            "Immersive audio experience",
            "Long-lasting battery for all-day use",
            "Comfortable for extended listening sessions",
            "Crystal clear calls with built-in mic"
        ]
    )

    request_params = {
        "product_title": product_title or "Premium Wireless Headphones",
        "media_type": media_type,
        "product_context": product_context,
        "aspect_ratio": "16:9",
        "language": "en",
        "variants_count": variants_count
    }

    return ProviderMediaRequest(**request_params)


async def test_video_provider(output_dir: Path, use_image_input: bool = False, reference_image_path: str = None, product_title: str = None, variants_count: int = 1):
    """Test veo3 video provider."""
    print("\n" + "="*60)
    if use_image_input:
        print("🎥 TESTING VEO3 VIDEO PROVIDER (WITH IMAGE INPUT)")
    else:
        print("🎥 TESTING VEO3 VIDEO PROVIDER")
    print("="*60)

    try:
        # Create provider and config
        provider = Veo3Provider()
        config = create_provider_config("veo3")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Create request
        request = create_sample_request("video", product_title, variants_count)

        # Add image input if requested
        if use_image_input:
            print("🖼️  Loading reference image for video generation...")
            try:
                if reference_image_path and Path(reference_image_path).exists():
                    # Load existing image file
                    with open(reference_image_path, "rb") as f:
                        image_bytes = f.read()

                    # Convert to base64
                    base64_data = base64.b64encode(image_bytes).decode('utf-8')

                    # Determine mime type
                    import mimetypes
                    mime_type, _ = mimetypes.guess_type(reference_image_path)
                    if not mime_type:
                        # Fallback based on file extension
                        if reference_image_path.lower().endswith('.avif'):
                            mime_type = "image/avif"
                        elif reference_image_path.lower().endswith('.png'):
                            mime_type = "image/png"
                        elif reference_image_path.lower().endswith('.jpg') or reference_image_path.lower().endswith('.jpeg'):
                            mime_type = "image/jpeg"
                        else:
                            mime_type = "image/png"  # Default

                    # Always use ImageInput model
                    image_input = ImageInput(
                        bytesBase64Encoded=base64_data,
                        mimeType=mime_type
                    )
                    request.reference_image = image_input
                    print(f"✅ Reference image loaded and converted to base64 dict format from: {reference_image_path}")
                    print(f"   MIME type: {mime_type}")
                    print(f"   File size: {len(image_bytes)} bytes")
                else:
                    print("⚠️  No reference image path provided or file doesn't exist")
                    print("   Please specify --reference-image path/to/image.png")
                    use_image_input = False

            except Exception as e:
                print(f"⚠️  Failed to load reference image: {e}, proceeding without image input")
                use_image_input = False

        input_type = "with image input" if use_image_input else "text-only"
        print(f"📝 Generating {request.variants_count} video(s) for: {request.product_title} ({input_type})")

        # Generate videos
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Video generation failed: {result.error_message}")
            return False

        print(f"✅ Generated {len(result.variants)} video variants successfully")

        # Save generated videos
        for i, variant in enumerate(result.variants):
            video_url = variant.get("video_url", "")

            # Check if video was downloaded locally by the provider
            if video_url and not video_url.startswith("http"):
                # Video was downloaded locally, move it to output directory
                local_video_path = Path(video_url)
                if local_video_path.exists():
                    suffix = "_with_image" if use_image_input else ""
                    target_file = output_dir / f"veo3_video_{i}{suffix}.mp4"
                    # Copy the file to output directory
                    import shutil
                    shutil.copy2(str(local_video_path), str(target_file))
                    print(f"   🎬 Video {i}: {target_file}")
                    # Clean up the original file
                    local_video_path.unlink()
                else:
                    print(f"   ❌ Local video file not found: {video_url}")
            elif video_url and video_url.startswith("http"):
                # Try to download from URL
                try:
                    video_bytes = await provider.download_media(video_url)
                    suffix = "_with_image" if use_image_input else ""
                    file_name = output_dir / f"veo3_video_{i}{suffix}.mp4"
                    save_binary_file(str(file_name), video_bytes)
                    print(f"   🎬 Video {i}: {file_name}")
                except Exception as e:
                    print(f"   ❌ Failed to download video {i}: {e}")
                    print(f"      Video URL: {video_url}")
            else:
                print(f"   ⚠️  Video {i} URL not accessible: {video_url}")

        # Save metadata
        metadata_file = output_dir / f"veo3_metadata{'_with_image' if use_image_input else ''}.json"
        with open(metadata_file, "w") as f:
            # Gather input details
            input_details = {
                "reference_image_path": reference_image_path if use_image_input else None,
                "product_context": request.product_context.dict() if request.product_context else None,
                "media_type": request.media_type,
                "aspect_ratio": request.aspect_ratio,
                "variants_count": request.variants_count,
                "settings": request.settings,
                "custom_config": request.custom_config
            }

            # Add image processing details if image was used
            if use_image_input and reference_image_path:
                try:
                    image_path = Path(reference_image_path)
                    if image_path.exists():
                        input_details["image_processing"] = {
                            "file_path": str(image_path.absolute()),
                            "file_size_bytes": image_path.stat().st_size,
                            "file_extension": image_path.suffix,
                            "mime_type_used": "image/avif",  # As hardcoded in veo3.py
                            "processing_method": "direct_binary_read"
                        }
                except Exception as e:
                    input_details["image_processing_error"] = str(e)

            metadata = {
                "provider": "veo3",
                "product": request.product_title,
                "variants_generated": len(result.variants),
                "aspect_ratio": request.aspect_ratio,
                "image_input_used": use_image_input,
                "inputs": input_details,
                "result": result.dict() if hasattr(result, 'dict') else str(result)
            }
            json.dump(metadata, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error testing video provider: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test veo3 video provider and generate real files")
    parser.add_argument("--product-title", type=str,
                        help="Product title for video generation")
    parser.add_argument("--variants-count", type=int, default=1,
                        help="Number of video variants to generate")
    parser.add_argument("--video-with-image", action="store_true",
                        help="Test video generation with image input")
    parser.add_argument("--reference-image", type=str,
                        help="Path to reference image file for video generation")
    parser.add_argument("--output-dir", type=str,
                        help="Output directory for generated files")

    args = parser.parse_args()

    # Create output directory
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = Path(f"./scripts/outputs/video_run_{int(time.time())}")
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🚀 Veo3 Video Provider Test")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"🎯 Product: {args.product_title or 'Premium Wireless Headphones'}")
    print(f"🎥 Videos to generate: {args.variants_count}")
    print(f"🖼️  With image input: {args.video_with_image}")

    # Check environment variables
    if not os.environ.get("VEO3_API_KEY"):
        print("⚠️  Warning: VEO3_API_KEY not set")
        print("   Set the environment variable to run the test")
        print("   Example: export VEO3_API_KEY='your-key-here'")
        return

    # Test video provider
    success = await test_video_provider(
        output_dir,
        use_image_input=args.video_with_image,
        reference_image_path=args.reference_image,
        product_title=args.product_title,
        variants_count=args.variants_count
    )

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"VIDEO: {status}")

    if success:
        print("🎉 Test passed! Check the output directory for generated files.")
    else:
        print("⚠️  Test failed. Check the output above for details.")

    print(f"📁 Generated files are in: {output_dir.absolute()}")


if __name__ == "__main__":
    asyncio.run(main())