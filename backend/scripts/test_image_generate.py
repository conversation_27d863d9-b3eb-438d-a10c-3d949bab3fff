#!/usr/bin/env python3
"""
Standalone script to test banana image provider and generate real image files.
This script tests the banana (Gemini) image provider for manual verification.
"""

import os
import json
import base64
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any
import time

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env")

# Add src directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set up logging
from core.utils.logging import setup_logging
setup_logging("image_test.log")

from modules.media.providers.config import ProviderConfig
from modules.media.providers.image.banana import BananaProvider
from modules.media.schemas import ProviderMediaRequest, ProductContext, ProductCategory


def save_binary_file(file_name: str, data: bytes):
    """Save binary data to file."""
    with open(file_name, "wb") as f:
        f.write(data)
    print(f"✓ File saved to: {file_name}")


def create_provider_config(provider_name: str) -> ProviderConfig:
    """Create provider configuration from the config file."""
    config_file = Path(__file__).parent.parent / "src" / "core" / "configs" / "providers_config.json"

    with open(config_file, "r") as f:
        config_data = json.load(f)

    if provider_name not in config_data["providers"]:
        raise ValueError(f"Provider {provider_name} not found in config")

    provider_config = config_data["providers"][provider_name]
    return ProviderConfig.from_dict(provider_name, provider_config)


def create_sample_request(media_type: str, product_title: str = None, num_images: int = 2) -> ProviderMediaRequest:
    """Create a sample media request for testing."""
    product_context = ProductContext(
        title=product_title or "Premium Wireless Headphones",
        description="High-quality wireless headphones with active noise cancellation, premium sound quality, and 30-hour battery life. Perfect for music lovers and professionals.",
        category=ProductCategory.ELECTRONICS,
        brand="TechSound",
        colors=["black", "white", "silver"],
        materials=["plastic", "metal", "leather"],
        price=299.99,
        currency="USD",
        price_tier="premium",
        key_features=[
            "Active noise cancellation",
            "30-hour battery life",
            "Premium sound quality",
            "Comfortable fit for extended use",
            "Wireless connectivity",
            "Built-in microphone"
        ],
        benefits=[
            "Immersive audio experience",
            "Long-lasting battery for all-day use",
            "Comfortable for extended listening sessions",
            "Crystal clear calls with built-in mic"
        ]
    )

    request_params = {
        "product_title": product_title or "Premium Wireless Headphones",
        "media_type": media_type,
        "product_context": product_context,
        "aspect_ratio": "1:1",
        "language": "en",
        "num_images": num_images,
        "style": "product_photography"
    }

    return ProviderMediaRequest(**request_params)


async def test_image_provider(output_dir: Path, product_title: str = None, num_images: int = 2):
    """Test banana image provider."""
    print("\n" + "="*60)
    print("🖼️  TESTING BANANA IMAGE PROVIDER")
    print("="*60)

    try:
        # Create provider and config
        provider = BananaProvider()
        config = create_provider_config("banana")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Create request
        request = create_sample_request("image", product_title, num_images)
        print(f"📝 Generating {request.num_images} images for: {request.product_title}")

        # Generate images
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Image generation failed: {result.error_message}")
            return False

        print(f"✅ Generated {len(result.images)} images successfully")

        # Save generated images
        for i, image_data in enumerate(result.images):
            image_url = image_data["image_url"]

            try:
                # Download and save the image
                image_bytes = await provider.download_media(image_url)
                file_name = output_dir / f"banana_image_{i}.png"
                save_binary_file(str(file_name), image_bytes)
                print(f"   📸 Image {i}: {file_name}")

            except Exception as e:
                print(f"   ❌ Failed to save image {i}: {e}")

        # Save metadata
        metadata_file = output_dir / "banana_metadata.json"
        with open(metadata_file, "w") as f:
            json.dump({
                "provider": "banana",
                "product": request.product_title,
                "images_generated": len(result.images),
                "aspect_ratio": request.aspect_ratio,
                "result": result.dict() if hasattr(result, 'dict') else str(result)
            }, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error testing image provider: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test banana image provider and generate real files")
    parser.add_argument("--product-title", type=str,
                        help="Product title for image generation")
    parser.add_argument("--num-images", type=int, default=2,
                        help="Number of images to generate")
    parser.add_argument("--output-dir", type=str,
                        help="Output directory for generated files")

    args = parser.parse_args()

    # Create output directory
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = Path(f"./scripts/outputs/image_run_{int(time.time())}")
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🚀 Banana Image Provider Test")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"🎯 Product: {args.product_title or 'Premium Wireless Headphones'}")
    print(f"🖼️  Images to generate: {args.num_images}")

    # Check environment variables
    if not os.environ.get("BANANA_API_KEY"):
        print("⚠️  Warning: BANANA_API_KEY not set")
        print("   Set the environment variable to run the test")
        print("   Example: export BANANA_API_KEY='your-key-here'")
        return

    # Test image provider
    success = await test_image_provider(output_dir, args.product_title, args.num_images)

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"IMAGE: {status}")

    if success:
        print("🎉 Test passed! Check the output directory for generated files.")
    else:
        print("⚠️  Test failed. Check the output above for details.")

    print(f"📁 Generated files are in: {output_dir.absolute()}")


if __name__ == "__main__":
    asyncio.run(main())