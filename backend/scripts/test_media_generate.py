#!/usr/bin/env python3
"""
Standalone script to test media providers and generate real files for manual verification.
This script tests the banana (image), gemini (text), and veo3 (video) providers.
"""

import os
import json
import base64
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any
import time

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env")

# Add src directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set up logging
from core.utils.logging import setup_logging
setup_logging("media_test.log")

from modules.media.providers.config import ProviderConfig
from modules.media.providers.image.banana import BananaProvider
from modules.media.providers.text.gemini import GeminiProvider
from modules.media.providers.video.veo3 import Veo3Provider
from modules.media.schemas import ProviderMediaRequest, ProductContext, ProductCategory, ImageInput


def save_binary_file(file_name: str, data: bytes):
    """Save binary data to file."""
    with open(file_name, "wb") as f:
        f.write(data)
    print(f"✓ File saved to: {file_name}")


def save_text_file(file_name: str, text: str):
    """Save text content to file."""
    with open(file_name, "w", encoding="utf-8") as f:
        f.write(text)
    print(f"✓ Text file saved to: {file_name}")


def create_provider_config(provider_name: str) -> ProviderConfig:
    """Create provider configuration from the config file."""
    config_file = Path(__file__).parent.parent / "src" / "core" / "configs" / "providers_config.json"

    with open(config_file, "r") as f:
        config_data = json.load(f)

    if provider_name not in config_data["providers"]:
        raise ValueError(f"Provider {provider_name} not found in config")

    provider_config = config_data["providers"][provider_name]
    return ProviderConfig.from_dict(provider_name, provider_config)


def create_sample_request(media_type: str) -> ProviderMediaRequest:
    """Create a sample media request for testing."""
    product_context = ProductContext(
        title="Premium Wireless Headphones",
        description="High-quality wireless headphones with active noise cancellation, premium sound quality, and 30-hour battery life. Perfect for music lovers and professionals.",
        category=ProductCategory.ELECTRONICS,
        brand="TechSound",
        colors=["black", "white", "silver"],
        materials=["plastic", "metal", "leather"],
        price=299.99,
        currency="USD",
        price_tier="premium",
        key_features=[
            "Active noise cancellation",
            "30-hour battery life",
            "Premium sound quality",
            "Comfortable fit for extended use",
            "Wireless connectivity",
            "Built-in microphone"
        ],
        benefits=[
            "Immersive audio experience",
            "Long-lasting battery for all-day use",
            "Comfortable for extended listening sessions",
            "Crystal clear calls with built-in mic"
        ]
    )

    request_params = {
        "product_title": "Premium Wireless Headphones",
        "media_type": media_type,
        "product_context": product_context,
        "aspect_ratio": "1:1",
        "language": "en"
    }

    if media_type == "image":
        request_params.update({
            "num_images": 2,
            "style": "product_photography"
        })
    elif media_type == "text":
        request_params.update({
            "variants_count": 2
        })
    elif media_type == "video":
        request_params.update({
            "variants_count": 1,
            "aspect_ratio": "16:9"
        })

    return ProviderMediaRequest(**request_params)


async def test_image_provider(output_dir: Path):
    """Test banana image provider."""
    print("\n" + "="*60)
    print("🖼️  TESTING BANANA IMAGE PROVIDER")
    print("="*60)

    try:
        # Create provider and config
        provider = BananaProvider()
        config = create_provider_config("banana")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Create request
        request = create_sample_request("image")
        print(f"📝 Generating {request.num_images} images for: {request.product_title}")

        # Generate images
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Image generation failed: {result.error_message}")
            return False

        print(f"✅ Generated {len(result.images)} images successfully")

        # Save generated images
        for i, image_data in enumerate(result.images):
            image_url = image_data["image_url"]

            try:
                # Download and save the image
                image_bytes = await provider.download_media(image_url)
                file_name = output_dir / f"banana_image_{i}.png"
                save_binary_file(str(file_name), image_bytes)
                print(f"   📸 Image {i}: {file_name}")

            except Exception as e:
                print(f"   ❌ Failed to save image {i}: {e}")

        # Save metadata
        metadata_file = output_dir / "banana_metadata.json"
        with open(metadata_file, "w") as f:
            json.dump({
                "provider": "banana",
                "product": request.product_title,
                "images_generated": len(result.images),
                "aspect_ratio": request.aspect_ratio,
                "result": result.dict() if hasattr(result, 'dict') else str(result)
            }, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error testing image provider: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_text_provider(output_dir: Path):
    """Test gemini text provider."""
    print("\n" + "="*60)
    print("📝 TESTING GEMINI TEXT PROVIDER")
    print("="*60)

    try:
        # Create provider and config
        provider = GeminiProvider()
        config = create_provider_config("gemini")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Create request
        request = create_sample_request("text")
        print(f"📝 Generating text content for: {request.product_title}")

        # Generate text
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Text generation failed: {result.error_message}")
            return False

        print(f"✅ Generated {len(result.variants)} text variants successfully")

        # Save generated text
        for i, variant in enumerate(result.variants):
            content_type = variant.get("content_type", "unknown")
            text_content = variant.get("text", "")

            file_name = output_dir / f"gemini_text_{content_type}_{i}.txt"
            save_text_file(str(file_name), text_content)
            print(f"   📄 Text variant {i} ({content_type}): {file_name}")

            # Also save as JSON with metadata
            json_file = output_dir / f"gemini_text_{content_type}_{i}.json"
            with open(json_file, "w") as f:
                json.dump(variant, f, indent=2)

        # Save metadata
        metadata_file = output_dir / "gemini_metadata.json"
        with open(metadata_file, "w") as f:
            json.dump({
                "provider": "gemini",
                "product": request.product_title,
                "variants_generated": len(result.variants),
                "result": result.dict() if hasattr(result, 'dict') else str(result)
            }, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error testing text provider: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_video_provider(output_dir: Path, use_image_input: bool = False, reference_image_path: str = None):
    """Test veo3 video provider."""
    print("\n" + "="*60)
    if use_image_input:
        print("🎥 TESTING VEO3 VIDEO PROVIDER (WITH IMAGE INPUT)")
    else:
        print("🎥 TESTING VEO3 VIDEO PROVIDER")
    print("="*60)

    try:
        # Create provider and config
        provider = Veo3Provider()
        config = create_provider_config("veo3")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Create request
        request = create_sample_request("video")

        # Add image input if requested
        if use_image_input:
            print("🖼️  Loading reference image for video generation...")
            try:
                if reference_image_path and Path(reference_image_path).exists():
                    # Load existing image file
                    with open(reference_image_path, "rb") as f:
                        image_bytes = f.read()

                    # Convert to base64
                    base64_data = base64.b64encode(image_bytes).decode('utf-8')

                    # Determine mime type
                    import mimetypes
                    mime_type, _ = mimetypes.guess_type(reference_image_path)
                    if not mime_type:
                        # Fallback based on file extension
                        if reference_image_path.lower().endswith('.avif'):
                            mime_type = "image/avif"
                        elif reference_image_path.lower().endswith('.png'):
                            mime_type = "image/png"
                        elif reference_image_path.lower().endswith('.jpg') or reference_image_path.lower().endswith('.jpeg'):
                            mime_type = "image/jpeg"
                        else:
                            mime_type = "image/png"  # Default

                    # Always use ImageInput model
                    image_input = ImageInput(
                        bytesBase64Encoded=base64_data,
                        mimeType=mime_type
                    )
                    request.reference_image = image_input
                    print(f"✅ Reference image loaded and converted to base64 dict format from: {reference_image_path}")
                    print(f"   MIME type: {mime_type}")
                    print(f"   File size: {len(image_bytes)} bytes")
                else:
                    print("⚠️  No reference image path provided or file doesn't exist")
                    print("   Please specify --reference-image path/to/image.png")
                    use_image_input = False

            except Exception as e:
                print(f"⚠️  Failed to load reference image: {e}, proceeding without image input")
                use_image_input = False

        input_type = "with image input" if use_image_input else "text-only"
        print(f"📝 Generating {request.variants_count} video(s) for: {request.product_title} ({input_type})")

        # Generate videos
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Video generation failed: {result.error_message}")
            return False

        print(f"✅ Generated {len(result.variants)} video variants successfully")

        # Save generated videos
        for i, variant in enumerate(result.variants):
            video_url = variant.get("video_url", "")

            # Check if video was downloaded locally by the provider
            if video_url and not video_url.startswith("http"):
                # Video was downloaded locally, move it to output directory
                local_video_path = Path(video_url)
                if local_video_path.exists():
                    suffix = "_with_image" if use_image_input else ""
                    target_file = output_dir / f"veo3_video_{i}{suffix}.mp4"
                    # Copy the file to output directory
                    import shutil
                    shutil.copy2(str(local_video_path), str(target_file))
                    print(f"   🎬 Video {i}: {target_file}")
                    # Clean up the original file
                    local_video_path.unlink()
                else:
                    print(f"   ❌ Local video file not found: {video_url}")
            elif video_url and video_url.startswith("http"):
                # Try to download from URL
                try:
                    video_bytes = await provider.download_media(video_url)
                    suffix = "_with_image" if use_image_input else ""
                    file_name = output_dir / f"veo3_video_{i}{suffix}.mp4"
                    save_binary_file(str(file_name), video_bytes)
                    print(f"   🎬 Video {i}: {file_name}")
                except Exception as e:
                    print(f"   ❌ Failed to download video {i}: {e}")
                    print(f"      Video URL: {video_url}")
            else:
                print(f"   ⚠️  Video {i} URL not accessible: {video_url}")

        # Save metadata
        metadata_file = output_dir / f"veo3_metadata{'_with_image' if use_image_input else ''}.json"
        with open(metadata_file, "w") as f:
            # Gather input details
            input_details = {
                "reference_image_path": reference_image_path if use_image_input else None,
                "product_context": request.product_context.dict() if request.product_context else None,
                "media_type": request.media_type,
                "aspect_ratio": request.aspect_ratio,
                "variants_count": request.variants_count,
                "settings": request.settings,
                "custom_config": request.custom_config
            }

            # Add image processing details if image was used
            if use_image_input and reference_image_path:
                try:
                    image_path = Path(reference_image_path)
                    if image_path.exists():
                        input_details["image_processing"] = {
                            "file_path": str(image_path.absolute()),
                            "file_size_bytes": image_path.stat().st_size,
                            "file_extension": image_path.suffix,
                            "mime_type_used": "image/avif",  # As hardcoded in veo3.py
                            "processing_method": "direct_binary_read"
                        }
                except Exception as e:
                    input_details["image_processing_error"] = str(e)

            metadata = {
                "provider": "veo3",
                "product": request.product_title,
                "variants_generated": len(result.variants),
                "aspect_ratio": request.aspect_ratio,
                "image_input_used": use_image_input,
                "inputs": input_details,
                "result": result.dict() if hasattr(result, 'dict') else str(result)
            }
            json.dump(metadata, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error testing video provider: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test media providers and generate real files")
    parser.add_argument("--providers", nargs="+", choices=["image", "text", "video", "all"],
                       default=["all"], help="Providers to test")
    parser.add_argument("--video-with-image", action="store_true",
                       help="Test video generation with image input (requires image provider)")
    parser.add_argument("--reference-image", type=str,
                       help="Path to reference image file for video generation")

    args = parser.parse_args()

    # Create output directory
    output_dir = Path(f"./scripts/outputs/run_{int(time.time())}")
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🚀 Media Provider Integration Tests")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"🔑 Testing providers: {args.providers}")

    # Check environment variables
    required_keys = []
    if "image" in args.providers or "all" in args.providers:
        required_keys.append("BANANA_API_KEY")
    if "text" in args.providers or "all" in args.providers:
        required_keys.append("GEMINI_API_KEY")
    if "video" in args.providers or "all" in args.providers:
        required_keys.append("VEO3_API_KEY")

    missing_keys = [key for key in required_keys if not os.environ.get(key)]
    if missing_keys:
        print(f"⚠️  Warning: Missing API keys: {', '.join(missing_keys)}")
        print("   Set these environment variables to run the full tests")
        print("   Example: export BANANA_API_KEY='your-key-here'")
        return

    # Determine which providers to test
    providers_to_test = []
    if "all" in args.providers:
        providers_to_test = ["image", "text", "video"]
    else:
        providers_to_test = args.providers

    results = {}

    # Test each provider
    if "image" in providers_to_test:
        results["image"] = await test_image_provider(output_dir)

    if "text" in providers_to_test:
        results["text"] = await test_text_provider(output_dir)

    if "video" in providers_to_test:
        # Test video generation with and without image input
        if args.video_with_image:
            results["video_with_image"] = await test_video_provider(
                output_dir,
                use_image_input=True,
                reference_image_path=args.reference_image
            )
        else:
            results["video"] = await test_video_provider(output_dir, use_image_input=False)

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    for provider, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{provider.upper():>8}: {status}")

    successful = sum(results.values())
    total = len(results)

    print(f"\n🎯 Results: {successful}/{total} providers tested successfully")

    if successful == total:
        print("🎉 All tests passed! Check the output directory for generated files.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

    print(f"📁 Generated files are in: {output_dir.absolute()}")


if __name__ == "__main__":
    asyncio.run(main())