#!/usr/bin/env python3
"""
Simple script to generate images using all images from scripts/images directory
with a predefined prompt for e-commerce fashion photos.
"""

import os
import json
import base64
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any, List
import time

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env")

# Add src directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set up logging
from core.utils.logging import setup_logging
setup_logging("simple_image_gen.log")

from modules.media.providers.config import ProviderConfig
from modules.media.providers.image.banana import BananaProvider
from modules.media.schemas import ProviderMediaRequest, ProductContext, ProductCategory, ImageInput


def save_binary_file(file_name: str, data: bytes):
    """Save binary data to file."""
    with open(file_name, "wb") as f:
        f.write(data)
    print(f"✓ File saved to: {file_name}")


def create_provider_config(provider_name: str) -> ProviderConfig:
    """Create provider configuration from the config file."""
    config_file = Path(__file__).parent.parent / "src" / "core" / "configs" / "providers_config.json"

    with open(config_file, "r") as f:
        config_data = json.load(f)

    if provider_name not in config_data["providers"]:
        raise ValueError(f"Provider {provider_name} not found in config")

    provider_config = config_data["providers"][provider_name]
    return ProviderConfig.from_dict(provider_name, provider_config)


def get_all_images(images_dir: Path) -> List[Dict[str, Any]]:
    """Get all images from the directory."""
    images = []

    if not images_dir.exists():
        print(f"⚠️  Images directory not found: {images_dir}")
        return images

    # Supported image extensions
    image_extensions = {'.jpg', '.jpeg', '.png', '.avif', '.webp'}

    for image_file in images_dir.iterdir():
        if image_file.is_file() and image_file.suffix.lower() in image_extensions:
            image_info = {
                "path": image_file,
                "filename": image_file.name,
                "extension": image_file.suffix,
                "size_bytes": image_file.stat().st_size,
                "modified_time": image_file.stat().st_mtime
            }
            images.append(image_info)

    # Sort images by filename
    images.sort(key=lambda x: x["filename"])

    print(f"📸 Found {len(images)} images in {images_dir} (sorted by filename):")
    for img in images:
        print(f"   - {img['filename']} ({img['size_bytes']} bytes)")

    return images


async def generate_with_all_images(output_dir: Path, images: List[Dict[str, Any]], custom_prompt: str = None):
    """Generate images using all images as reference."""
    print("\n" + "="*60)
    print("🖼️  GENERATING IMAGES WITH ALL REFERENCE IMAGES")
    print("="*60)

    try:
        # Create provider and config
        provider = BananaProvider()
        config = create_provider_config("banana")

        print(f"📋 Initializing provider with model: {config.model}")

        # Initialize provider
        success = await provider.initialize(config)
        if not success:
            print("❌ Provider initialization failed")
            return False

        print("✅ Provider initialized successfully")

        # Load first 4 images (limit to avoid API issues)
        image_inputs = []
        for img_info in images:  # Limit to first 4 images
            try:
                with open(img_info["path"], "rb") as f:
                    image_bytes = f.read()

                # Convert to base64
                base64_data = base64.b64encode(image_bytes).decode('utf-8')

                # Determine mime type
                import mimetypes
                mime_type, _ = mimetypes.guess_type(str(img_info["path"]))
                if not mime_type:
                    if img_info["extension"].lower() in ['.jpg', '.jpeg']:
                        mime_type = "image/jpeg"
                    elif img_info["extension"].lower() == '.png':
                        mime_type = "image/png"
                    elif img_info["extension"].lower() == '.avif':
                        mime_type = "image/avif"
                    else:
                        mime_type = "image/jpeg"

                image_input = ImageInput(
                    bytesBase64Encoded=base64_data,
                    mimeType=mime_type
                )
                image_inputs.append(image_input)
                print(f"✅ Loaded: {img_info['filename']} ({len(image_bytes)} bytes, {mime_type})")

            except Exception as e:
                print(f"⚠️  Failed to load {img_info['filename']}: {e}")
                continue

        if not image_inputs:
            print("❌ No images could be loaded")
            return False

        # Create request
        product_context = ProductContext(
            title="Fashion Product",
            description="Professional e-commerce fashion product",
            category=ProductCategory.FASHION_APPAREL,
            brand="FashionBrand",
            colors=["various"],
            materials=["premium materials"],
            price=99.99,
            currency="USD",
            price_tier="premium"
        )

        # Use custom prompt or default (matching simple_fashion_gen.py)
        prompt_text = custom_prompt or """
        Make the woman wear the outfit items from first image, and scene from second image. 
        Replace her current outfit as far as needed. 
        DO NOT CHANGE THE WOMAN's features.. 
        The face from third image needs to match EXACTLY. 
        Put her in a fitting daring pose in the background with the props. 
        Ensure the lighting is perfect and top studio quality.
        """

        request = ProviderMediaRequest(
            product_title="Fashion Product",
            media_type="image",
            product_context=product_context,
            aspect_ratio="1:1",
            language="en",
            num_images=1,
            custom_prompt=prompt_text
        )

        # Add reference images
        request.reference_images = image_inputs
        print(f"📸 Passing {len(image_inputs)} images to Gemini API")
        print(f"📝 Using prompt: {prompt_text[:100]}...")

        # Generate image
        result = await provider.generate_media(request)

        if not result.success:
            print(f"❌ Image generation failed: {result.error_message}")
            print(f"💡 This usually means the API call succeeded but no images were generated.")
            print(f"   Possible reasons: content filtering, model limitations, or unsupported features.")
            return False

        print(f"✅ Generated {len(result.images)} image successfully")

        # Save generated image
        for i, image_data in enumerate(result.images):
            image_url = image_data["image_url"]

            try:
                # Download and save the image
                image_bytes = await provider.download_media(image_url)
                file_name = output_dir / "fashion_ecommerce_shot.png"
                save_binary_file(str(file_name), image_bytes)
                print(f"   📸 Generated image: {file_name}")

            except Exception as e:
                print(f"   ❌ Failed to save image {i}: {e}")

        # Save metadata
        metadata_file = output_dir / "generation_metadata.json"
        with open(metadata_file, "w") as f:
            metadata = {
                "timestamp": time.time(),
                "provider": "banana",
                "model": config.model,
                "images_used": len(images),
                "images_passed_to_api": len(image_inputs),  # Now uses all images
                "prompt_used": prompt_text,
                "generated_images": len(result.images),
                "reference_images": [
                    {
                        "filename": img["filename"],
                        "size_bytes": img["size_bytes"],
                        "path": str(img["path"])
                    } for img in images
                ]
            }
            json.dump(metadata, f, indent=2)

        print(f"📄 Metadata saved to: {metadata_file}")

        await provider.cleanup()
        return True

    except Exception as e:
        print(f"❌ Error generating images: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Generate fashion images using all images from scripts/images")
    parser.add_argument("--images-dir", type=str, default="./scripts/images",
                        help="Directory containing reference images")
    parser.add_argument("--prompt", type=str,
                        help="Custom prompt for image generation")
    parser.add_argument("--output-dir", type=str,
                        help="Output directory for generated files")

    args = parser.parse_args()

    # Get all images
    images_dir = Path(args.images_dir)
    images = get_all_images(images_dir)

    if not images:
        print("❌ No images found to use as reference")
        return

    # Create output directory
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = Path(f"./scripts/outputs/simple_gen_{int(time.time())}")
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🚀 Simple Fashion Image Generation")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"📸 Using {len(images)} reference images")
    print(f"📝 Prompt: {args.prompt[:100] if args.prompt else 'Default fashion prompt'}...")

    # Check environment variables
    if not os.environ.get("BANANA_API_KEY"):
        print("⚠️  Warning: BANANA_API_KEY not set")
        print("   Set the environment variable to run the generation")
        print("   Example: export BANANA_API_KEY='your-key-here'")
        return

    # Generate image
    success = await generate_with_all_images(output_dir, images, args.prompt)

    # Summary
    print("\n" + "="*60)
    print("📊 GENERATION SUMMARY")
    print("="*60)

    status = "✅ SUCCESS" if success else "❌ FAILED"
    print(f"IMAGE GENERATION: {status}")

    if success:
        print("🎉 Generation completed! Check the output directory for the generated image.")
        print(f"📸 Used {len(images)} reference images for enhanced generation")
    else:
        print("⚠️  Generation failed. Check the output above for details.")

    print(f"📁 Generated files are in: {output_dir.absolute()}")


if __name__ == "__main__":
    asyncio.run(main())