"""
Comprehensive unit tests for media provider configuration classes.
Tests all dataclasses and ProviderConfig functionality.
"""

import pytest
from typing import Dict, List, Any

from modules.media.providers.config import (
    ProviderCapabilities,
    ProviderLimits,
    ProviderCosts,
    ProviderQuality,
    ProviderMetadata,
    AspectRatio,
    TextGenerationParams,
    ImageGenerationParams,
    VideoGenerationParams,
    ImageConfig,
    VideoConfig,
    TextConfig,
    ProviderConfig
)


class TestProviderCapabilities:
    """Test ProviderCapabilities dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating ProviderCapabilities with all fields."""
        capabilities = ProviderCapabilities(
            supported_formats=["image", "video"],
            supported_styles=["professional", "casual"],
            supported_categories=["electronics", "fashion"],
            content_types=["product_description", "marketing_copy"],
            supported_languages=["en", "es", "fr"],
            supported_aspect_ratios=["1:1", "16:9", "9:16"],
            max_duration_seconds=300,
            supported_person_generation=["realistic", "cartoon"]
        )

        assert capabilities.supported_formats == ["image", "video"]
        assert capabilities.supported_styles == ["professional", "casual"]
        assert capabilities.supported_categories == ["electronics", "fashion"]
        assert capabilities.content_types == ["product_description", "marketing_copy"]
        assert capabilities.supported_languages == ["en", "es", "fr"]
        assert capabilities.supported_aspect_ratios == ["1:1", "16:9", "9:16"]
        assert capabilities.max_duration_seconds == 300
        assert capabilities.supported_person_generation == ["realistic", "cartoon"]

    def test_creation_with_optional_fields_none(self):
        """Test creating ProviderCapabilities with optional fields as None."""
        capabilities = ProviderCapabilities(
            supported_formats=["text"]
        )

        assert capabilities.supported_formats == ["text"]
        assert capabilities.supported_styles is None
        assert capabilities.supported_categories is None
        assert capabilities.content_types is None
        assert capabilities.supported_languages is None
        assert capabilities.supported_aspect_ratios is None
        assert capabilities.max_duration_seconds is None
        assert capabilities.supported_person_generation is None

    def test_equality(self):
        """Test equality comparison."""
        cap1 = ProviderCapabilities(supported_formats=["image"])
        cap2 = ProviderCapabilities(supported_formats=["image"])
        cap3 = ProviderCapabilities(supported_formats=["video"])

        assert cap1 == cap2
        assert cap1 != cap3


class TestProviderLimits:
    """Test ProviderLimits dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating ProviderLimits with all fields."""
        limits = ProviderLimits(
            max_variants_per_request=10,
            requests_per_minute=60,
            requests_per_hour=1000,
            token_limits={"input": 1000, "output": 2000}
        )

        assert limits.max_variants_per_request == 10
        assert limits.requests_per_minute == 60
        assert limits.requests_per_hour == 1000
        assert limits.token_limits == {"input": 1000, "output": 2000}

    def test_creation_with_optional_fields_none(self):
        """Test creating ProviderLimits with optional fields as None."""
        limits = ProviderLimits(
            max_variants_per_request=5,
            requests_per_minute=30,
            requests_per_hour=500
        )

        assert limits.max_variants_per_request == 5
        assert limits.requests_per_minute == 30
        assert limits.requests_per_hour == 500
        assert limits.token_limits is None


class TestProviderCosts:
    """Test ProviderCosts dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating ProviderCosts with all fields."""
        costs = ProviderCosts(
            cost_per_unit=0.02,
            currency="USD"
        )

        assert costs.cost_per_unit == 0.02
        assert costs.currency == "USD"

    def test_creation_with_default_currency(self):
        """Test creating ProviderCosts with default currency."""
        costs = ProviderCosts(cost_per_unit=0.01)

        assert costs.cost_per_unit == 0.01
        assert costs.currency == "USD"


class TestProviderQuality:
    """Test ProviderQuality dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating ProviderQuality with all fields."""
        quality = ProviderQuality(
            quality_score=0.85,
            average_generation_time=45
        )

        assert quality.quality_score == 0.85
        assert quality.average_generation_time == 45


class TestProviderMetadata:
    """Test ProviderMetadata dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating ProviderMetadata with all fields."""
        metadata = ProviderMetadata(
            provider_name="test_provider",
            description="A test provider for media generation"
        )

        assert metadata.provider_name == "test_provider"
        assert metadata.description == "A test provider for media generation"


class TestAspectRatio:
    """Test AspectRatio dataclass."""

    def test_creation(self):
        """Test creating AspectRatio."""
        ratio = AspectRatio(width=1920, height=1080)

        assert ratio.width == 1920
        assert ratio.height == 1080


class TestTextGenerationParams:
    """Test TextGenerationParams dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating TextGenerationParams with all fields."""
        params = TextGenerationParams(
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            max_tokens=1000,
            presence_penalty=0.1,
            frequency_penalty=0.1,
            stop_sequences=["\n", "END"]
        )

        assert params.temperature == 0.7
        assert params.top_p == 0.9
        assert params.top_k == 50
        assert params.max_tokens == 1000
        assert params.presence_penalty == 0.1
        assert params.frequency_penalty == 0.1
        assert params.stop_sequences == ["\n", "END"]

    def test_creation_with_defaults(self):
        """Test creating TextGenerationParams with defaults."""
        params = TextGenerationParams()

        assert params.temperature == 0.7
        assert params.top_p is None
        assert params.top_k is None
        assert params.max_tokens is None
        assert params.presence_penalty is None
        assert params.frequency_penalty is None
        assert params.stop_sequences is None


class TestImageGenerationParams:
    """Test ImageGenerationParams dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating ImageGenerationParams with all fields."""
        params = ImageGenerationParams(
            quality="high",
            style="photorealistic",
            negative_prompt="blurry, low quality",
            guidance_scale=7.5,
            num_inference_steps=25
        )

        assert params.quality == "high"
        assert params.style == "photorealistic"
        assert params.negative_prompt == "blurry, low quality"
        assert params.guidance_scale == 7.5
        assert params.num_inference_steps == 25

    def test_creation_with_all_none(self):
        """Test creating ImageGenerationParams with all None."""
        params = ImageGenerationParams()

        assert params.quality is None
        assert params.style is None
        assert params.negative_prompt is None
        assert params.guidance_scale is None
        assert params.num_inference_steps is None


class TestVideoGenerationParams:
    """Test VideoGenerationParams dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating VideoGenerationParams with all fields."""
        params = VideoGenerationParams(
            resolution="1920x1080",
            fps=30,
            duration_seconds=60,
            codec="h264",
            bitrate="5000k"
        )

        assert params.resolution == "1920x1080"
        assert params.fps == 30
        assert params.duration_seconds == 60
        assert params.codec == "h264"
        assert params.bitrate == "5000k"

    def test_creation_with_all_none(self):
        """Test creating VideoGenerationParams with all None."""
        params = VideoGenerationParams()

        assert params.resolution is None
        assert params.fps is None
        assert params.duration_seconds is None
        assert params.codec is None
        assert params.bitrate is None


class TestImageConfig:
    """Test ImageConfig dataclass."""

    def test_creation_with_generation_params(self):
        """Test creating ImageConfig with generation params."""
        gen_params = ImageGenerationParams(quality="high")
        config = ImageConfig(
            generation_params=gen_params
        )

        assert config.generation_params == gen_params

    def test_creation_with_none_values(self):
        """Test creating ImageConfig with None values."""
        config = ImageConfig()

        assert config.generation_params is None


class TestVideoConfig:
    """Test VideoConfig dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating VideoConfig with all fields."""
        aspect_resolutions = {"1:1": "1024x1024", "16:9": "1920x1080"}

        gen_params = VideoGenerationParams(resolution="1920x1080", fps=30)
        config = VideoConfig(
            aspect_resolutions=aspect_resolutions,
            generation_params=gen_params
        )

        assert config.aspect_resolutions == aspect_resolutions
        assert config.generation_params == gen_params

    def test_creation_with_none_values(self):
        """Test creating VideoConfig with None values."""
        config = VideoConfig()

        assert config.aspect_resolutions is None
        assert config.generation_params is None


class TestTextConfig:
    """Test TextConfig dataclass."""

    def test_creation_with_all_fields(self):
        """Test creating TextConfig with all fields."""
        temp_settings = {"creative": 0.9, "factual": 0.3}
        gen_params = TextGenerationParams(temperature=0.7, max_tokens=1000)

        config = TextConfig(
            temperature_settings=temp_settings,
            generation_params=gen_params,
            estimated_completion_time_per_variant=15
        )

        assert config.temperature_settings == temp_settings
        assert config.generation_params == gen_params
        assert config.estimated_completion_time_per_variant == 15

    def test_creation_with_none_values(self):
        """Test creating TextConfig with None values."""
        config = TextConfig()

        assert config.temperature_settings is None
        assert config.generation_params is None
        assert config.estimated_completion_time_per_variant is None


class TestProviderConfig:
    """Test ProviderConfig class."""

    def test_constructor_creation(self):
        """Test creating ProviderConfig via constructor."""
        capabilities = ProviderCapabilities(supported_formats=["image"])
        limits = ProviderLimits(max_variants_per_request=5, requests_per_minute=30, requests_per_hour=500)
        costs = ProviderCosts(cost_per_unit=0.02)
        quality = ProviderQuality(quality_score=0.8, average_generation_time=30)
        metadata = ProviderMetadata(provider_name="test_provider", description="Test provider")

        config = ProviderConfig(
            name="test_provider",
            type="image",
            api_key="test_key",
            timeout=300,
            model="test_model",
            capabilities=capabilities,
            limits=limits,
            costs=costs,
            quality=quality,
            features=["batch_generation"],
            metadata=metadata
        )

        assert config.name == "test_provider"
        assert config.type == "image"
        assert config.api_key == "test_key"
        assert config.timeout == 300
        assert config.model == "test_model"
        assert config.capabilities == capabilities
        assert config.limits == limits
        assert config.costs == costs
        assert config.quality == quality
        assert config.features == ["batch_generation"]
        assert config.metadata == metadata

    def test_from_dict_simple_config(self):
        """Test from_dict with real banana provider configuration."""
        import json
        from pathlib import Path

        # Load the real banana provider config
        config_path = Path(__file__).parent.parent.parent.parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "providers_config.json"
        with open(config_path, 'r') as f:
            real_config = json.load(f)

        config_dict = real_config["providers"]["banana"]
        config = ProviderConfig.from_dict("banana", config_dict)

        assert config.name == "banana"
        assert config.type == "image"
        assert config.api_key == "${BANANA_API_KEY}"
        assert config.timeout == 300
        assert config.model == "gemini-2.5-flash-image-preview"
        assert config.capabilities.supported_formats == ["image"]
        assert config.limits.max_variants_per_request == 4
        assert config.costs.cost_per_unit == 0.04
        assert config.quality.quality_score == 0.95
        assert "Professional product photography" in config.features
        assert config.metadata.provider_name == "Google Gemini - Image Generation"

    def test_from_dict_with_image_config(self):
        """Test from_dict with real banana provider image configuration."""
        import json
        from pathlib import Path

        # Load the real banana provider config
        config_path = Path(__file__).parent.parent.parent.parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "providers_config.json"
        with open(config_path, 'r') as f:
            real_config = json.load(f)

        config_dict = real_config["providers"]["banana"]
        config = ProviderConfig.from_dict("banana", config_dict)

        assert config.image_config is not None
        assert config.image_config.generation_params.quality == "high"
        assert config.image_config.generation_params.style == "photorealistic"

    def test_from_dict_with_video_config(self):
        """Test from_dict with real veo3 provider video configuration."""
        import json
        from pathlib import Path

        # Load the real veo3 provider config
        config_path = Path(__file__).parent.parent.parent.parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "providers_config.json"
        with open(config_path, 'r') as f:
            real_config = json.load(f)

        config_dict = real_config["providers"]["veo3"]
        config = ProviderConfig.from_dict("veo3", config_dict)

        assert config.video_config is not None
        assert config.video_config.aspect_resolutions["1:1"] == "1080x1080"
        assert config.video_config.generation_params.resolution == "1080p"
        assert config.video_config.generation_params.fps == 30

    def test_from_dict_with_text_config(self):
        """Test from_dict with real gemini provider text configuration."""
        import json
        from pathlib import Path

        # Load the real gemini provider config
        config_path = Path(__file__).parent.parent.parent.parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "providers_config.json"
        with open(config_path, 'r') as f:
            real_config = json.load(f)

        config_dict = real_config["providers"]["gemini"]
        config = ProviderConfig.from_dict("gemini", config_dict)

        assert config.text_config is not None
        assert config.text_config.temperature_settings["social_caption"] == 0.7
        assert config.text_config.generation_params.temperature == 0.7
        assert config.text_config.generation_params.max_tokens == 1000
        assert config.text_config.estimated_completion_time_per_variant == 10

    def test_from_dict_with_defaults(self):
        """Test from_dict with missing optional fields (should use defaults)."""
        config_dict = {
            "type": "image",
            "api_key": "test_key",
            "model": "test_model",
            "capabilities": {"supported_formats": ["image"]},
            "limits": {"max_variants_per_request": 5, "requests_per_minute": 30, "requests_per_hour": 500},
            "costs": {"cost_per_unit": 0.02},
            "quality": {"quality_score": 0.8, "average_generation_time": 30},
            "features": [],
            "metadata": {"provider_name": "test_provider"}
        }

        config = ProviderConfig.from_dict("test_provider", config_dict)

        # Check defaults are applied
        assert config.timeout == 300  # Default timeout
        assert config.costs.currency == "USD"  # Default currency
        assert config.metadata.description == ""  # Default empty description

    def test_from_dict_missing_required_fields(self):
        """Test from_dict with missing required fields."""
        # Missing capabilities
        config_dict = {
            "type": "image",
            "api_key": "test_key",
            "model": "test_model",
            "limits": {"max_variants_per_request": 5, "requests_per_minute": 30, "requests_per_hour": 500},
            "costs": {"cost_per_unit": 0.02},
            "quality": {"quality_score": 0.8, "average_generation_time": 30},
            "features": [],
            "metadata": {"provider_name": "test_provider"}
        }

        # Should handle missing capabilities gracefully or raise appropriate error
        try:
            config = ProviderConfig.from_dict("test_provider", config_dict)
            # If it succeeds, verify it has default capabilities
            assert config.capabilities is not None
        except (KeyError, ValueError, TypeError):
            # This is also acceptable
            pass

    def test_from_dict_with_image_config_generation_params(self):
        """Test from_dict with image config generation params."""
        config_dict = {
            "type": "image",
            "api_key": "test_key",
            "timeout": 300,
            "model": "test_model",
            "capabilities": {"supported_formats": ["image"]},
            "limits": {"max_variants_per_request": 5, "requests_per_minute": 30, "requests_per_hour": 500},
            "costs": {"cost_per_unit": 0.02},
            "quality": {"quality_score": 0.8, "average_generation_time": 30},
            "features": [],
            "metadata": {"provider_name": "test_provider", "description": "Test provider"},
            "image_config": {
                "generation_params": {
                    "quality": "high",
                    "style": "photorealistic"
                }
            }
        }

        # Should handle gracefully
        config = ProviderConfig.from_dict("test_provider", config_dict)
        assert config.image_config is not None
        assert config.image_config.generation_params.quality == "high"
        assert config.image_config.generation_params.style == "photorealistic"


class TestConfigIntegration:
    """Test integration between different config classes."""

    def test_full_provider_config_creation(self):
        """Test creating a complete provider configuration."""
        # Create all sub-components
        capabilities = ProviderCapabilities(
            supported_formats=["image", "video", "text"],
            supported_styles=["professional", "casual", "artistic"],
            supported_languages=["en", "es", "fr"],
            supported_aspect_ratios=["1:1", "16:9", "9:16"]
        )

        limits = ProviderLimits(
            max_variants_per_request=10,
            requests_per_minute=100,
            requests_per_hour=2000
        )

        costs = ProviderCosts(cost_per_unit=0.015, currency="USD")
        quality = ProviderQuality(quality_score=0.9, average_generation_time=25)

        # Create media-specific configs
        image_config = ImageConfig(
            generation_params=ImageGenerationParams(
                quality="high",
                guidance_scale=7.5
            )
        )

        video_config = VideoConfig(
            generation_params=VideoGenerationParams(
                resolution="1920x1080",
                fps=30
            )
        )

        text_config = TextConfig(
            temperature_settings={"creative": 0.8, "factual": 0.4},
            generation_params=TextGenerationParams(
                temperature=0.7,
                max_tokens=2000
            )
        )

        metadata = ProviderMetadata(
            provider_name="comprehensive_provider",
            description="A comprehensive media generation provider"
        )

        # Create main config
        config = ProviderConfig(
            name="comprehensive_provider",
            type="multi_modal",
            api_key="comprehensive_key",
            timeout=600,
            model="comprehensive_model",
            capabilities=capabilities,
            limits=limits,
            costs=costs,
            quality=quality,
            features=["batch_generation", "style_transfer", "multilingual"],
            metadata=metadata,
            image_config=image_config,
            video_config=video_config,
            text_config=text_config
        )

        # Verify all components are properly integrated
        assert config.name == "comprehensive_provider"
        assert config.capabilities.supported_formats == ["image", "video", "text"]
        assert config.limits.max_variants_per_request == 10
        assert config.costs.cost_per_unit == 0.015
        assert config.quality.quality_score == 0.9
        assert config.text_config.temperature_settings["creative"] == 0.8
        assert config.features == ["batch_generation", "style_transfer", "multilingual"]