"""
Comprehensive unit tests for media provider manager classes.
Tests MediaProviderRegistry and MediaProviderManager functionality.
"""

import os
import pytest
import json
import subprocess
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from typing import Dict, List, Any, Type

from modules.media.providers.manager import (
    get_provider,
    get_available_providers,
    get_text_provider,
    get_image_provider,
    get_video_provider
)
from modules.media.providers.base import BaseMediaProvider
from modules.media.providers.config import ProviderConfig, ProviderCapabilities, ProviderLimits, ProviderCosts, ProviderQuality, ProviderMetadata
from modules.media.schemas import ProviderMediaRequest, ProviderMediaResult


class TestSimplifiedManager:
    """Test the simplified manager functions."""

    def test_get_available_providers(self):
        """Test getting list of available providers."""
        available = get_available_providers()

        assert isinstance(available, dict)
        assert "text" in available
        assert "image" in available
        assert "video" in available

        # Check that we have the expected providers
        assert "gemini" in available["text"]
        assert "example_text" in available["text"]
        assert "banana" in available["image"]
        assert "example_image" in available["image"]
        assert "veo3" in available["video"]
        assert "example_video" in available["video"]

    def test_get_available_providers_filtered(self):
        """Test getting available providers with media type filter."""
        text_providers = get_available_providers("text")
        image_providers = get_available_providers("image")
        video_providers = get_available_providers("video")

        assert "text" in text_providers
        assert "gemini" in text_providers["text"]
        assert "example_text" in text_providers["text"]

        assert "image" in image_providers
        assert "banana" in image_providers["image"]
        assert "example_image" in image_providers["image"]

        assert "video" in video_providers
        assert "veo3" in video_providers["video"]
        assert "example_video" in video_providers["video"]

    @pytest.mark.asyncio
    async def test_get_provider_by_type_with_overrides(self):
        """Test getting providers by type (with current override settings)."""
        # Set overrides for testing
        import os
        original_text_override = os.environ.get("TEXT_PROVIDER_OVERRIDE", "")
        original_image_override = os.environ.get("IMAGE_PROVIDER_OVERRIDE", "")
        original_video_override = os.environ.get("VIDEO_PROVIDER_OVERRIDE", "")

        os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"
        os.environ["IMAGE_PROVIDER_OVERRIDE"] = "example_image"
        os.environ["VIDEO_PROVIDER_OVERRIDE"] = "example_video"

        try:
            # Test text provider - will return example_text due to override
            text_provider = await get_text_provider("gemini")
            assert text_provider is not None
            assert text_provider.provider_name == "example_text"  # Overridden

            # Test image provider - will return example_image due to override
            image_provider = await get_image_provider("banana")
            assert image_provider is not None
            assert image_provider.provider_name == "example_image"  # Overridden

            # Test video provider - will return example_video due to override
            video_provider = await get_video_provider("veo3")
            assert video_provider is not None
            assert video_provider.provider_name == "example_video"  # Overridden
        finally:
            # Restore original overrides
            if original_text_override:
                os.environ["TEXT_PROVIDER_OVERRIDE"] = original_text_override
            else:
                os.environ.pop("TEXT_PROVIDER_OVERRIDE", None)

            if original_image_override:
                os.environ["IMAGE_PROVIDER_OVERRIDE"] = original_image_override
            else:
                os.environ.pop("IMAGE_PROVIDER_OVERRIDE", None)

            if original_video_override:
                os.environ["VIDEO_PROVIDER_OVERRIDE"] = original_video_override
            else:
                os.environ.pop("VIDEO_PROVIDER_OVERRIDE", None)

    @pytest.mark.asyncio
    async def test_get_provider_by_type_no_overrides(self):
        """Test getting providers by type without overrides."""
        # Clear overrides for testing
        original_text_override = os.environ.get("TEXT_PROVIDER_OVERRIDE", "")
        original_image_override = os.environ.get("IMAGE_PROVIDER_OVERRIDE", "")
        original_video_override = os.environ.get("VIDEO_PROVIDER_OVERRIDE", "")

        os.environ["IMAGE_PROVIDER_OVERRIDE"] = ""
        os.environ["VIDEO_PROVIDER_OVERRIDE"] = ""
        os.environ["TEXT_PROVIDER_OVERRIDE"] = ""

        try:
            # Test text provider - should return example_text (default for gemini)
            text_provider = await get_text_provider("example_text")
            assert text_provider is not None
            assert text_provider.provider_name == "example_text"

            # Test image provider - should return example_image (default for banana)
            image_provider = await get_image_provider("example_image")
            assert image_provider is not None
            assert image_provider.provider_name == "example_image"

            # Test video provider - should return example_video (default for veo3)
            video_provider = await get_video_provider("example_video")
            assert video_provider is not None
            assert video_provider.provider_name == "example_video"
        finally:
            # Restore original overrides
            if original_text_override:
                os.environ["TEXT_PROVIDER_OVERRIDE"] = original_text_override
            else:
                os.environ.pop("TEXT_PROVIDER_OVERRIDE", None)

            if original_image_override:
                os.environ["IMAGE_PROVIDER_OVERRIDE"] = original_image_override
            else:
                os.environ.pop("IMAGE_PROVIDER_OVERRIDE", None)

            if original_video_override:
                os.environ["VIDEO_PROVIDER_OVERRIDE"] = original_video_override
            else:
                os.environ.pop("VIDEO_PROVIDER_OVERRIDE", None)

    @pytest.mark.asyncio
    async def test_get_provider_unified_interface_with_overrides(self):
        """Test the unified get_provider interface with overrides."""
        # Set overrides for testing
        original_text_override = os.environ.get("TEXT_PROVIDER_OVERRIDE", "")
        original_image_override = os.environ.get("IMAGE_PROVIDER_OVERRIDE", "")
        original_video_override = os.environ.get("VIDEO_PROVIDER_OVERRIDE", "")

        os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"
        os.environ["IMAGE_PROVIDER_OVERRIDE"] = "example_image"
        os.environ["VIDEO_PROVIDER_OVERRIDE"] = "example_video"

        try:
            # Test getting providers through unified interface
            text_provider = await get_provider("gemini", "text")
            assert text_provider is not None
            assert text_provider.provider_name == "example_text"

            image_provider = await get_provider("banana", "image")
            assert image_provider is not None
            assert image_provider.provider_name == "example_image"

            video_provider = await get_provider("veo3", "video")
            assert video_provider is not None
            assert video_provider.provider_name == "example_video"
        finally:
            # Restore original overrides
            if original_text_override:
                os.environ["TEXT_PROVIDER_OVERRIDE"] = original_text_override
            else:
                os.environ.pop("TEXT_PROVIDER_OVERRIDE", None)

            if original_image_override:
                os.environ["IMAGE_PROVIDER_OVERRIDE"] = original_image_override
            else:
                os.environ.pop("IMAGE_PROVIDER_OVERRIDE", None)

            if original_video_override:
                os.environ["VIDEO_PROVIDER_OVERRIDE"] = original_video_override
            else:
                os.environ.pop("VIDEO_PROVIDER_OVERRIDE", None)


    @pytest.mark.asyncio
    async def test_provider_caching(self):
        """Test that providers are cached after first access."""
        # Set override for testing
        original_text_override = os.environ.get("TEXT_PROVIDER_OVERRIDE", "")
        os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"

        try:
            # Get a provider
            provider1 = await get_text_provider("gemini")
            assert provider1 is not None

            # Get the same provider again - should return cached instance
            provider2 = await get_text_provider("gemini")
            assert provider2 is provider1  # Same instance
        finally:
            # Restore original override
            if original_text_override:
                os.environ["TEXT_PROVIDER_OVERRIDE"] = original_text_override
            else:
                os.environ.pop("TEXT_PROVIDER_OVERRIDE", None)


    def test_invalid_media_type(self):
        """Test error handling for invalid media types."""
        with pytest.raises(ValueError) as exc_info:
            import asyncio
            asyncio.run(get_provider("gemini", "invalid_type"))

        assert "unsupported media type" in str(exc_info.value).lower()



class TestProviderManagerIntegration:
    """Test integration between manager components."""




    @pytest.mark.asyncio
    async def test_provider_override_logic_with_correct_names(self):
        """Test that provider overrides work with correct provider names."""
        from modules.media.providers.manager import get_text_provider, get_image_provider, get_video_provider

        # Set overrides for testing
        original_text_override = os.environ.get("TEXT_PROVIDER_OVERRIDE", "")
        original_image_override = os.environ.get("IMAGE_PROVIDER_OVERRIDE", "")
        original_video_override = os.environ.get("VIDEO_PROVIDER_OVERRIDE", "")

        os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"
        os.environ["IMAGE_PROVIDER_OVERRIDE"] = "example_image"
        os.environ["VIDEO_PROVIDER_OVERRIDE"] = "example_video"

        try:
            # Test image provider override - should return example_image instead of banana
            provider = await get_image_provider("banana")
            assert provider.provider_name == "example_image"

            # Test video provider override - should return example_video instead of veo3
            provider = await get_video_provider("veo3")
            assert provider.provider_name == "example_video"

            # Test text provider override - should return example_text instead of gemini
            provider = await get_text_provider("gemini")
            assert provider.provider_name == "example_text"
        finally:
            # Restore original overrides
            if original_text_override:
                os.environ["TEXT_PROVIDER_OVERRIDE"] = original_text_override
            else:
                os.environ.pop("TEXT_PROVIDER_OVERRIDE", None)

            if original_image_override:
                os.environ["IMAGE_PROVIDER_OVERRIDE"] = original_image_override
            else:
                os.environ.pop("IMAGE_PROVIDER_OVERRIDE", None)

            if original_video_override:
                os.environ["VIDEO_PROVIDER_OVERRIDE"] = original_video_override
            else:
                os.environ.pop("VIDEO_PROVIDER_OVERRIDE", None)

    @pytest.mark.asyncio
    async def test_provider_lifecycle_integration(self):
        """Test provider lifecycle with real components."""
        # Use functional testing approach to set env vars before module import
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "",
            "IMAGE_PROVIDER_OVERRIDE": "",
            "VIDEO_PROVIDER_OVERRIDE": ""
        }

        test_code = """    # Test getting providers through the simplified interface (no overrides)
    # Use example providers that don't require API keys
    text_provider = await get_text_provider("example_text")
    assert text_provider is not None
    assert text_provider.provider_name == "example_text"

    image_provider = await get_image_provider("example_image")
    assert image_provider is not None
    assert image_provider.provider_name == "example_image"

    video_provider = await get_video_provider("example_video")
    assert video_provider is not None
    assert video_provider.provider_name == "example_video"

    print("SUCCESS: Provider lifecycle integration working correctly")"""

        # Use the functional testing approach
        result = TestProviderOverrideFunctional._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result



class TestProviderOverrideFunctional:
    """Functional tests for provider overrides that work around module-level caching."""

    @staticmethod
    def _run_test_with_env_overrides(env_vars: Dict[str, str], test_code: str) -> str:
        """Run a test in a subprocess with specific environment variables set."""
        # Create test script
        test_script = f"""import os
import sys
import asyncio

# Set environment variables before any imports
for key, value in {env_vars}.items():
    os.environ[key] = value

# Now import the manager (this will use the env vars we just set)
from modules.media.providers.manager import get_text_provider, get_image_provider, get_video_provider

async def run_test():
{test_code}

if __name__ == "__main__":
    asyncio.run(run_test())
"""

        # Write test script to temp file
        script_path = Path(__file__).parent / "temp_override_test.py"
        with open(script_path, 'w') as f:
            f.write(test_script)

        try:
            # Run the test in subprocess
            result = subprocess.run([
                sys.executable, str(script_path)
            ], capture_output=True, text=True, cwd=Path(__file__).parent.parent.parent.parent.parent)

            if result.returncode != 0:
                raise AssertionError(f"Test failed: {result.stderr}")

            return result.stdout

        finally:
            # Clean up temp file
            if script_path.exists():
                script_path.unlink()

    def test_override_functionality_with_correct_provider_names(self):
        """Test that provider overrides work when set before module import."""
        # Test with correct provider names
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "example_text",
            "IMAGE_PROVIDER_OVERRIDE": "example_image",
            "VIDEO_PROVIDER_OVERRIDE": "example_video"
        }

        test_code = """    # Test that requesting different providers returns the overridden ones
    text_provider = await get_text_provider("gemini")
    assert text_provider.provider_name == "example_text", f"Expected example_text, got {{text_provider.provider_name}}"

    image_provider = await get_image_provider("banana")
    assert image_provider.provider_name == "example_image", f"Expected example_image, got {{image_provider.provider_name}}"

    video_provider = await get_video_provider("veo3")
    assert video_provider.provider_name == "example_video", f"Expected example_video, got {{video_provider.provider_name}}"

    print("SUCCESS: All overrides working correctly")"""

        result = self._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result

    def test_override_functionality_without_overrides(self):
        """Test that providers work normally when no overrides are set."""
        # Test without any overrides
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "",
            "IMAGE_PROVIDER_OVERRIDE": "",
            "VIDEO_PROVIDER_OVERRIDE": ""
        }

        test_code = """    # Test that requesting providers returns the requested ones (no overrides)
    text_provider = await get_text_provider("example_text")
    assert text_provider.provider_name == "example_text", f"Expected example_text, got {{text_provider.provider_name}}"

    image_provider = await get_image_provider("example_image")
    assert image_provider.provider_name == "example_image", f"Expected example_image, got {{image_provider.provider_name}}"

    video_provider = await get_video_provider("example_video")
    assert video_provider.provider_name == "example_video", f"Expected example_video, got {{video_provider.provider_name}}"

    print("SUCCESS: No overrides working correctly")"""

        result = self._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result

    def test_override_functionality_with_wrong_provider_names(self):
        """Test that wrong override names cause proper error handling."""
        # Test with wrong provider names
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "nonexistent_text",
            "IMAGE_PROVIDER_OVERRIDE": "nonexistent_image",
            "VIDEO_PROVIDER_OVERRIDE": "nonexistent_video"
        }

        test_code = """    # Test that wrong override names cause errors
    try:
        text_provider = await get_text_provider("gemini")
        assert False, "Should have raised ValueError for nonexistent_text"
    except ValueError as e:
        assert "nonexistent_text" in str(e), f"Error should mention nonexistent_text: {{e}}"

    try:
        image_provider = await get_image_provider("banana")
        assert False, "Should have raised ValueError for nonexistent_image"
    except ValueError as e:
        assert "nonexistent_image" in str(e), f"Error should mention nonexistent_image: {{e}}"

    try:
        video_provider = await get_video_provider("veo3")
        assert False, "Should have raised ValueError for nonexistent_video"
    except ValueError as e:
        assert "nonexistent_video" in str(e), f"Error should mention nonexistent_video: {{e}}"

    print("SUCCESS: Wrong override names handled correctly")"""

        result = self._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result

    def test_partial_overrides(self):
        """Test that only specified overrides are applied."""
        # Test with only text override
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "example_text",
            "IMAGE_PROVIDER_OVERRIDE": "",
            "VIDEO_PROVIDER_OVERRIDE": ""
        }

        test_code = """    # Test that only text is overridden
    text_provider = await get_text_provider("example_text")
    assert text_provider.provider_name == "example_text", f"Expected example_text, got {{text_provider.provider_name}}"

    image_provider = await get_image_provider("example_image")
    assert image_provider.provider_name == "example_image", f"Expected example_image, got {{image_provider.provider_name}}"

    video_provider = await get_video_provider("example_video")
    assert video_provider.provider_name == "example_video", f"Expected example_video, got {{video_provider.provider_name}}"

    print("SUCCESS: Partial overrides working correctly")"""

    def test_provider_not_found_with_override(self):
        """Test that provider not found errors work correctly with overrides."""
        # Test with override set to non-existent provider
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "nonexistent_provider",
            "IMAGE_PROVIDER_OVERRIDE": "",
            "VIDEO_PROVIDER_OVERRIDE": ""
        }

        test_code = """    # Test that non-existent override provider raises proper error
    try:
        text_provider = await get_text_provider("example_text")
        assert False, "Should have raised ValueError for nonexistent_provider"
    except ValueError as e:
        assert "nonexistent_provider" in str(e), f"Error should mention nonexistent_provider: {{e}}"

    print("SUCCESS: Provider not found with override handled correctly")"""

        result = self._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result

    def test_provider_not_found_without_override(self):
        """Test that provider not found errors work correctly without overrides."""
        # Test without any overrides
        env_overrides = {
            "TEXT_PROVIDER_OVERRIDE": "",
            "IMAGE_PROVIDER_OVERRIDE": "",
            "VIDEO_PROVIDER_OVERRIDE": ""
        }

        test_code = """    # Test that non-existent requested provider raises proper error
    try:
        text_provider = await get_text_provider("nonexistent_provider")
        assert False, "Should have raised ValueError for nonexistent_provider"
    except ValueError as e:
        assert "nonexistent_provider" in str(e), f"Error should mention nonexistent_provider: {{e}}"

    print("SUCCESS: Provider not found without override handled correctly")"""

        result = self._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result

        result = self._run_test_with_env_overrides(env_overrides, test_code)
        assert "SUCCESS" in result