"""
Comprehensive tests for media module models.
Tests all model classes, relationships, enums, and database operations.
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from modules.media.models import (
    MediaJob, MediaVariant, Template, GenerationBatch, GenerationRequest,
    MediaReview, RejectedAsset, GeneratedAsset,
    MediaJobStatus, MediaVariantStatus, PushStatus, PlanTier,
    ImageProvider, VideoProvider
)
from modules.auth.models import User


class TestMediaJobStatus:
    """Test MediaJobStatus enum."""
    
    def test_enum_values(self):
        """Test all enum values are present."""
        assert MediaJobStatus.PENDING.value == "pending"
        assert MediaJobStatus.PROCESSING.value == "processing"
        assert MediaJobStatus.COMPLETED.value == "completed"
        assert MediaJobStatus.FAILED.value == "failed"
        assert MediaJobStatus.CANCELLED.value == "cancelled"
    
    def test_enum_iteration(self):
        """Test enum can be iterated."""
        statuses = list(MediaJobStatus)
        assert len(statuses) == 5
        assert MediaJobStatus.PENDING in statuses


class TestMediaVariantStatus:
    """Test MediaVariantStatus enum."""
    
    def test_enum_values(self):
        """Test all enum values are present."""
        assert MediaVariantStatus.GENERATING.value == "GENERATING"
        assert MediaVariantStatus.COMPLETED.value == "COMPLETED"
        assert MediaVariantStatus.FAILED.value == "FAILED"
        assert MediaVariantStatus.PROCESSING.value == "PROCESSING"


class TestProviderEnums:
    """Test provider enums."""
    
    def test_image_provider_enum(self):
        """Test ImageProvider enum."""
        assert ImageProvider.BANANA.value == "banana"
        assert ImageProvider.MOCK.value == "mock"
    
    def test_video_provider_enum(self):
        """Test VideoProvider enum."""
        assert VideoProvider.VEO3.value == "veo3"
        assert VideoProvider.MOCK.value == "mock"


class TestPlanTier:
    """Test PlanTier enum."""
    
    def test_plan_tier_values(self):
        """Test plan tier enum values."""
        assert PlanTier.FREE.value == "free"
        assert PlanTier.STARTER.value == "starter"
        assert PlanTier.GROWTH.value == "growth"
        assert PlanTier.PRO.value == "pro"
        assert PlanTier.ENTERPRISE.value == "enterprise"


class TestPushStatus:
    """Test PushStatus enum."""
    
    def test_push_status_values(self):
        """Test push status enum values."""
        assert PushStatus.PENDING.value == "pending"
        assert PushStatus.PUSHING.value == "pushing"
        assert PushStatus.COMPLETED.value == "completed"
        assert PushStatus.FAILED.value == "failed"


class TestMediaJob:
    """Test MediaJob model."""
    
    @pytest.mark.asyncio
    async def test_create_media_job(self, db_session: AsyncSession, test_user: User):
        """Test creating a media job."""
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PENDING,
            media_type="image",
            provider="banana",
            progress_percentage=0.0
        )
        
        db_session.add(job)
        await db_session.commit()
        await db_session.refresh(job)
        
        assert job.id is not None
        assert job.user_id == test_user.id
        assert job.product_id == 123
        assert job.status == MediaJobStatus.PENDING
        assert job.media_type == "image"
        assert job.provider == "banana"
        assert job.progress_percentage == 0.0
        assert job.created_at is not None
        # updated_at is None for newly created records (only set on updates)
    
    @pytest.mark.asyncio
    async def test_media_job_relationships(self, db_session: AsyncSession, test_user: User):
        """Test MediaJob relationships."""
        # Create job
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PROCESSING,
            media_type="image",
            provider="banana"
        )
        db_session.add(job)
        await db_session.commit()
        await db_session.refresh(job)
        
        # Create variants
        variant1 = MediaVariant(
            job_id=job.id,
            user_id=test_user.id,
            variant_name="square",
            status=MediaVariantStatus.COMPLETED
        )
        variant2 = MediaVariant(
            job_id=job.id,
            user_id=test_user.id,
            variant_name="vertical",
            status=MediaVariantStatus.GENERATING
        )
        
        db_session.add_all([variant1, variant2])
        await db_session.commit()
        
        # Test relationship - load variants explicitly
        from sqlalchemy.orm import selectinload
        stmt = select(MediaJob).options(selectinload(MediaJob.variants)).where(MediaJob.id == job.id)
        result = await db_session.execute(stmt)
        job_with_variants = result.scalar_one()

        assert len(job_with_variants.variants) == 2
        variant_ids = [v.id for v in job_with_variants.variants]
        assert variant1.id in variant_ids
        assert variant2.id in variant_ids
    
    @pytest.mark.asyncio
    async def test_media_job_user_relationship(self, db_session: AsyncSession, test_user: User):
        """Test MediaJob user relationship."""
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PENDING,
            media_type="image",
            provider="banana"
        )
        
        db_session.add(job)
        await db_session.commit()
        await db_session.refresh(job)
        
        # Test user relationship
        assert job.user_id == test_user.id
        # Note: The actual user object relationship would need to be loaded separately
    
    @pytest.mark.asyncio
    async def test_media_job_update_progress(self, db_session: AsyncSession, test_user: User):
        """Test updating job progress."""
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PROCESSING,
            media_type="image",
            provider="banana",
            progress_percentage=0.0
        )
        
        db_session.add(job)
        await db_session.commit()
        
        # Update progress
        job.progress_percentage = 50.0
        job.status = MediaJobStatus.PROCESSING
        await db_session.commit()
        await db_session.refresh(job)
        
        assert job.progress_percentage == 50.0
        assert job.status == MediaJobStatus.PROCESSING
    
    @pytest.mark.asyncio
    async def test_media_job_completion(self, db_session: AsyncSession, test_user: User):
        """Test job completion."""
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PROCESSING,
            media_type="image",
            provider="banana"
        )
        
        db_session.add(job)
        await db_session.commit()
        
        # Complete job
        job.status = MediaJobStatus.COMPLETED
        job.progress_percentage = 100.0
        job.completed_at = datetime.utcnow()
        await db_session.commit()
        await db_session.refresh(job)
        
        assert job.status == MediaJobStatus.COMPLETED
        assert job.progress_percentage == 100.0
        assert job.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_media_job_failure(self, db_session: AsyncSession, test_user: User):
        """Test job failure handling."""
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PROCESSING,
            media_type="image",
            provider="banana"
        )
        
        db_session.add(job)
        await db_session.commit()
        
        # Fail job
        job.status = MediaJobStatus.FAILED
        job.error_message = "Provider API error"
        await db_session.commit()
        await db_session.refresh(job)
        
        assert job.status == MediaJobStatus.FAILED
        assert job.error_message == "Provider API error"


class TestMediaVariant:
    """Test MediaVariant model."""
    
    @pytest.mark.asyncio
    async def test_create_media_variant(self, db_session: AsyncSession, test_user: User):
        """Test creating a media variant."""
        # First create a job
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PROCESSING,
            media_type="image",
            provider="banana"
        )
        db_session.add(job)
        await db_session.commit()
        await db_session.refresh(job)
        
        # Create variant
        variant = MediaVariant(
            job_id=job.id,
            user_id=test_user.id,
            variant_name="square",
            status=MediaVariantStatus.COMPLETED,
            image_url="https://example.com/image.jpg",
            quality_score=85.5,
            user_rating=4
        )
        
        db_session.add(variant)
        await db_session.commit()
        await db_session.refresh(variant)
        
        assert variant.id is not None
        assert variant.job_id == job.id
        assert variant.user_id == test_user.id
        assert variant.variant_name == "square"
        assert variant.status == MediaVariantStatus.COMPLETED
        assert variant.image_url == "https://example.com/image.jpg"
        assert variant.quality_score == 85.5
        assert variant.user_rating == 4
        assert variant.created_at is not None
    
    @pytest.mark.asyncio
    async def test_media_variant_job_relationship(self, db_session: AsyncSession, test_user: User):
        """Test MediaVariant job relationship."""
        # Create job
        job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.PROCESSING,
            media_type="image",
            provider="banana"
        )
        db_session.add(job)
        await db_session.commit()
        await db_session.refresh(job)
        
        # Create variant
        variant = MediaVariant(
            job_id=job.id,
            user_id=test_user.id,
            variant_name="square",
            status=MediaVariantStatus.COMPLETED
        )
        db_session.add(variant)
        await db_session.commit()
        await db_session.refresh(variant)
        
        # Test relationship
        assert variant.job_id == job.id
    
    @pytest.mark.asyncio
    async def test_media_variant_different_types(self, db_session: AsyncSession, test_user: User):
        """Test variants for different media types."""
        # Create jobs for different media types
        image_job = MediaJob(
            user_id=test_user.id,
            product_id=123,
            status=MediaJobStatus.COMPLETED,
            media_type="image",
            provider="banana"
        )
        
        video_job = MediaJob(
            user_id=test_user.id,
            product_id=124,
            status=MediaJobStatus.COMPLETED,
            media_type="video",
            provider="veo3"
        )
        
        text_job = MediaJob(
            user_id=test_user.id,
            product_id=125,
            status=MediaJobStatus.COMPLETED,
            media_type="text",
            provider="gemini"
        )
        
        db_session.add_all([image_job, video_job, text_job])
        await db_session.commit()
        await db_session.refresh(image_job)
        await db_session.refresh(video_job)
        await db_session.refresh(text_job)
        
        # Create variants for each type
        image_variant = MediaVariant(
            job_id=image_job.id,
            user_id=test_user.id,
            variant_name="square",
            status=MediaVariantStatus.COMPLETED,
            image_url="https://example.com/image.jpg"
        )
        
        video_variant = MediaVariant(
            job_id=video_job.id,
            user_id=test_user.id,
            variant_name="standard",
            status=MediaVariantStatus.COMPLETED,
            video_url="https://example.com/video.mp4"
        )
        
        text_variant = MediaVariant(
            job_id=text_job.id,
            user_id=test_user.id,
            variant_name="marketing_copy",
            status=MediaVariantStatus.COMPLETED,
            text_content="Premium product description..."
        )
        
        db_session.add_all([image_variant, video_variant, text_variant])
        await db_session.commit()
        
        # Verify variants
        assert image_variant.image_url is not None
        assert video_variant.video_url is not None
        assert text_variant.text_content is not None
