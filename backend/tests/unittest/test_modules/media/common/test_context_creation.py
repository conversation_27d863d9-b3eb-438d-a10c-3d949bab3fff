"""
Tests for context_creation.py
"""

import pytest
from unittest.mock import MagicMock
from modules.media.common.context_creation import (
    create_product_context,
    create_brand_context,
    validate_request_context,
    enrich_request_with_defaults,
    create_provider_context,
    _is_valid_aspect_ratio
)
from modules.media.schemas import ProviderMediaRequest, ProductCategory
from modules.media.engines.context_engine import ProductContext, BrandContext, ContentStyle
from modules.media.providers.config import ProviderConfig

class TestContextCreation:
    """Test Context Creation utilities functionality."""

    @pytest.fixture
    def mock_provider_media_request(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.product_title = "Test Product"
        mock_request.product_description = "A wonderful test product."
        mock_request.media_type = "image"
        mock_request.aspect_ratio = "16:9"
        mock_request.target_audience = ["young adults"]
        mock_request.product_context = MagicMock()
        mock_request.product_context.price = 99.99
        mock_request.product_context.category = ProductCategory.ELECTRONICS
        mock_request.product_context.key_features = ["feature1"]
        mock_request.product_context.materials = ["material1"]
        mock_request.product_context.colors = ["red"]
        mock_request.product_context.price_tier = "premium"
        mock_request.product_context.style_keywords = ["modern"]
        mock_request.shop_branding = MagicMock()
        mock_request.shop_branding.shop_name = "Test Brand"
        mock_request.shop_branding.brand_voice = "friendly"
        mock_request.shop_branding.brand_values = ["value1"]
        mock_request.shop_branding.visual_style = "minimalist"
        mock_request.shop_branding.color_palette = ["#FFFFFF"]
        mock_request.usage_context = ["social media"]
        return mock_request

    def test_create_product_context_full_data(self, mock_provider_media_request):
        product_context = create_product_context(mock_provider_media_request)
        assert isinstance(product_context, ProductContext)
        assert product_context.title == "Test Product"
        assert product_context.description == "A wonderful test product."
        assert product_context.price == 99.99
        assert product_context.category == ProductCategory.ELECTRONICS
        assert product_context.target_audience == ["young adults"]
        assert product_context.key_features == ["feature1"]
        assert product_context.materials == ["material1"]
        assert product_context.colors == ["red"]
        assert product_context.price_tier == "premium"
        assert product_context.style_keywords == ["modern"]
        assert product_context.use_cases == ["social media"]

    def test_create_product_context_minimal_data(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.product_title = "Minimal Product"
        mock_request.product_description = None
        mock_request.media_type = "image"
        mock_request.aspect_ratio = "1:1"
        mock_request.target_audience = None
        mock_request.product_context = None
        mock_request.usage_context = None

        product_context = create_product_context(mock_request)
        assert isinstance(product_context, ProductContext)
        assert product_context.title == "Minimal Product"
        assert product_context.description == ""
        assert product_context.price is None
        assert product_context.category == ProductCategory.FASHION_APPAREL  # Default fallback
        assert product_context.target_audience == []
        assert product_context.key_features == []
        assert product_context.materials == []
        assert product_context.colors == []
        assert product_context.price_tier is None
        assert product_context.style_keywords == []
        assert product_context.use_cases == []

    def test_create_brand_context_full_data(self, mock_provider_media_request):
        brand_context = create_brand_context(mock_provider_media_request)
        assert isinstance(brand_context, BrandContext)
        assert brand_context.name == "Test Brand"
        assert brand_context.brand_voice == "friendly"
        assert brand_context.brand_values == ["value1"]
        assert brand_context.visual_style == ContentStyle.MINIMALIST
        assert brand_context.color_palette == ["#FFFFFF"]
        assert brand_context.industry == "e-commerce"
        assert brand_context.typography_style == "modern"
        assert brand_context.target_demographics == []
        assert brand_context.content_guidelines == {}

    def test_create_brand_context_no_branding(self, mock_provider_media_request):
        mock_provider_media_request.shop_branding = None
        brand_context = create_brand_context(mock_provider_media_request)
        assert brand_context is None

    def test_validate_request_context_valid(self, mock_provider_media_request):
        is_valid, error_message = validate_request_context(mock_provider_media_request)
        assert is_valid is True
        assert error_message is None

    @pytest.mark.parametrize(
        "product_title, media_type, aspect_ratio, expected_valid, expected_error",
        [
            (None, "image", "16:9", False, "Product title is required"),
            ("Title", None, "16:9", False, "Media type is required"),
            ("Title", "unsupported", "16:9", False, "Unsupported media type: unsupported. Supported: ['image', 'video', 'text', 'voice']"),
            ("Title", "image", "invalid-ratio", False, "Invalid aspect ratio format: invalid-ratio. Expected format: 'width:height'"),
            ("Title", "image", "16:9", True, None),
        ]
    )
    def test_validate_request_context_invalid(self, product_title, media_type, aspect_ratio, expected_valid, expected_error):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.product_title = product_title
        mock_request.media_type = media_type
        mock_request.aspect_ratio = aspect_ratio
        is_valid, error_message = validate_request_context(mock_request)
        assert is_valid == expected_valid
        assert error_message == expected_error

    @pytest.mark.parametrize(
        "aspect_ratio, expected_result",
        [
            ("16:9", True),
            ("1:1", True),
            ("4:3", True),
            ("0:1", False),
            ("1:0", False),
            ("invalid", False),
            ("16", False),
            ("16:9:1", False),
            (None, False),
            ("", False),
        ]
    )
    def test_is_valid_aspect_ratio(self, aspect_ratio, expected_result):
        assert _is_valid_aspect_ratio(aspect_ratio) == expected_result

    def test_enrich_request_with_defaults_no_defaults(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.aspect_ratio = "4:3"
        mock_request.media_type = "video"
        mock_request.variants_count = 5
        mock_request.style = "vintage"

        enriched_request = enrich_request_with_defaults(mock_request)
        assert enriched_request.aspect_ratio == "4:3"
        assert enriched_request.variants_count == 5
        assert enriched_request.style == "vintage"

    def test_enrich_request_with_defaults_missing_aspect_ratio(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.aspect_ratio = None
        mock_request.media_type = "image"
        mock_request.variants_count = None
        mock_request.style = None

        enriched_request = enrich_request_with_defaults(mock_request)
        assert enriched_request.aspect_ratio == "1:1"
        assert enriched_request.variants_count == 4
        assert enriched_request.style == "professional"

    def test_enrich_request_with_defaults_missing_variants_count_image(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.aspect_ratio = "16:9"
        mock_request.media_type = "image"
        mock_request.variants_count = None
        mock_request.style = None

        enriched_request = enrich_request_with_defaults(mock_request)
        assert enriched_request.variants_count == 4

    def test_enrich_request_with_defaults_missing_variants_count_video(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.aspect_ratio = "16:9"
        mock_request.media_type = "video"
        mock_request.variants_count = None
        mock_request.style = None

        enriched_request = enrich_request_with_defaults(mock_request)
        assert enriched_request.variants_count == 3

    def test_enrich_request_with_defaults_missing_variants_count_other(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.aspect_ratio = "16:9"
        mock_request.media_type = "text"
        mock_request.variants_count = None
        mock_request.style = None

        enriched_request = enrich_request_with_defaults(mock_request)
        assert enriched_request.variants_count == 1

    def test_create_provider_context_no_metadata(self):
        mock_config = MagicMock(spec=ProviderConfig)
        mock_config.timeout = 30
        mock_config.max_retries = 3
        mock_config.metadata = None

        context = create_provider_context("test_provider", mock_config)
        assert context == {
            'provider_name': "test_provider",
            'timeout': 30,
            'max_retries': 3,
        }

    def test_create_provider_context_with_metadata(self):
        mock_config = MagicMock(spec=ProviderConfig)
        mock_config.timeout = 60
        mock_config.max_retries = 5
        mock_config.metadata = {"provider_context": {"api_key": "123", "region": "us-east-1"}}

        context = create_provider_context("another_provider", mock_config)
        assert context == {
            'provider_name': "another_provider",
            'timeout': 60,
            'max_retries': 5,
            'api_key': "123",
            'region': "us-east-1",
        }