"""
Tests for error_handlers.py
"""

import pytest
from unittest.mock import MagicMock, patch
from modules.media.common.error_handlers import (
    handle_provider_error,
    create_error_result,
    classify_error,
    should_retry_error,
    get_retry_delay,
    create_timeout_error,
    create_rate_limit_error,
    validate_provider_response
)
from modules.media.schemas import ProviderMediaResult

class TestErrorHandlers:
    """Test Error Handlers functionality."""

    @pytest.fixture
    def mock_provider_media_result(self):
        return MagicMock(spec=ProviderMediaResult)

    @patch('modules.media.common.error_handlers.logger')
    def test_handle_provider_error(self, mock_logger, mock_provider_media_result):
        with patch('modules.media.common.error_handlers.create_error_result', return_value=mock_provider_media_result):
            error = ValueError("Test error")
            result = handle_provider_error(error, "test_provider", "test_operation", {"key": "value"})

            mock_logger.error.assert_called_once()
            assert result == mock_provider_media_result

    def test_create_error_result(self):
        result = create_error_result("Error message", "test_provider", "test_operation", "ERROR_CODE")
        assert isinstance(result, ProviderMediaResult)
        assert result.success is False
        assert result.error_message == "Error message"
        assert result.provider_job_id == "error_test_provider_test_operation"
        assert result.metadata['error_code'] == "ERROR_CODE"

    @pytest.mark.parametrize(
        "error, expected_classification",
        [
            (ConnectionError(), "NETWORK_ERROR"),
            (TimeoutError(), "NETWORK_ERROR"),
            (Exception("HTTPError"), "GENERIC_ERROR"), # Exception("HTTPError") creates Exception, not HTTPError
            (Exception("AuthenticationError"), "GENERIC_ERROR"), # Exception("AuthenticationError") creates Exception
            (Exception("UnauthorizedError"), "GENERIC_ERROR"), # Exception("UnauthorizedError") creates Exception
            (Exception("RateLimitError"), "GENERIC_ERROR"), # Exception("RateLimitError") creates Exception
            (Exception("TooManyRequests"), "GENERIC_ERROR"), # Exception("TooManyRequests") creates Exception
            (ValueError(), "CONFIGURATION_ERROR"),
            (Exception("API error"), "PROVIDER_API_ERROR"),
            (Exception("provider error"), "PROVIDER_API_ERROR"),
            (Exception("Generic error"), "GENERIC_ERROR"),
        ]
    )
    def test_classify_error(self, error, expected_classification):
        assert classify_error(error) == expected_classification

    @pytest.mark.parametrize(
        "error, attempt, max_attempts, expected_retry",
        [
            (ConnectionError(), 0, 3, True),
            (Exception("RateLimitError"), 1, 3, False),  # Exception doesn't match the specific error types
            (ValueError(), 0, 3, False),
            (ConnectionError(), 2, 3, False), # Max attempts reached
            (Exception("API error"), 0, 1, False), # Max attempts reached
        ]
    )
    def test_should_retry_error(self, error, attempt, max_attempts, expected_retry):
        assert should_retry_error(error, attempt, max_attempts) == expected_retry

    @pytest.mark.parametrize(
        "error, attempt, expected_delay",
        [
            (ConnectionError(), 0, 2.0),
            (ConnectionError(), 1, 4.0),
            (Exception("RateLimitError"), 0, 1.0),  # Exception falls back to default
            (Exception("RateLimitError"), 1, 2.0),  # Exception falls back to default with backoff
            (ValueError(), 0, 1.0),
            (Exception("API error"), 0, 3.0),  # Exception falls back to default
        ]
    )
    def test_get_retry_delay(self, error, attempt, expected_delay):
        delay = get_retry_delay(error, attempt)
        # Allow small floating point differences
        assert abs(delay - expected_delay) < 0.01

    def test_create_timeout_error(self):
        result = create_timeout_error("test_provider", "test_operation", 10)
        assert isinstance(result, ProviderMediaResult)
        assert result.success is False
        assert "timed out" in result.error_message
        assert result.metadata['error_code'] == "TIMEOUT_ERROR"

    def test_create_rate_limit_error(self):
        result = create_rate_limit_error("test_provider", "test_operation", 60)
        assert isinstance(result, ProviderMediaResult)
        assert result.success is False
        assert "rate limited" in result.error_message
        assert "Retry after 60 seconds" in result.error_message
        assert result.metadata['error_code'] == "RATE_LIMIT_ERROR"

    @patch('modules.media.common.error_handlers.logger')
    def test_log_provider_metrics_success(self, mock_logger):
        from modules.media.common.error_handlers import log_provider_metrics
        log_provider_metrics("test_provider", "op", True, 1.5)
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert "Provider operation completed successfully" in args[0]
        assert kwargs['extra']['success'] is True

    @patch('modules.media.common.error_handlers.logger')
    def test_log_provider_metrics_failure(self, mock_logger):
        from modules.media.common.error_handlers import log_provider_metrics
        log_provider_metrics("test_provider", "op", False, 2.0, "NETWORK_ERROR")
        mock_logger.warning.assert_called_once()
        args, kwargs = mock_logger.warning.call_args
        assert "Provider operation failed" in args[0]
        assert kwargs['extra']['success'] is False
        assert kwargs['extra']['error_type'] == "NETWORK_ERROR"

    @pytest.mark.parametrize(
        "response, expected_type, expected_valid, expected_error_message",
        [
            (None, "image", False, "Provider returned null response"),
            (MagicMock(variants=[]), "image", False, "Provider response missing images"),
            (MagicMock(variants=["img1"]), "image", True, None),
            (MagicMock(variants=[]), "video", False, "Provider response missing video variants"),
            (MagicMock(variants=["vid1"]), "video", True, None),
            (MagicMock(variants=[]), "text", False, "Provider response missing text variants"),
            (MagicMock(variants=["txt1"]), "text", True, None),
            (MagicMock(), "unsupported", True, None), # Unsupported type, no specific validation
        ]
    )
    def test_validate_provider_response(self, response, expected_type, expected_valid, expected_error_message):
        is_valid, error_message = validate_provider_response(response, expected_type)
        assert is_valid == expected_valid
        assert error_message == expected_error_message