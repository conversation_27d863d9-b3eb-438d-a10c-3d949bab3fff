"""
Tests for rate_limiter.py
"""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime, timedelta, timezone
import asyncio
import time

from modules.media.common.rate_limiter import (
    RateLimit,
    QuotaInfo,
    TokenBucket,
    SlidingWindowRateLimiter,
    MediaRateLimiter,
    DEFAULT_RATE_LIMITS
)
import redis.asyncio as redis

class TestRateLimit:
    """Test RateLimit dataclass functionality."""

    def test_rate_limit_init(self):
        rl = RateLimit(requests_per_minute=10, requests_per_hour=100, requests_per_day=1000)
        assert rl.requests_per_minute == 10
        assert rl.burst_limit == 10 # Default


class TestQuotaInfo:
    """Test QuotaInfo dataclass functionality."""

    def test_quota_info_init(self):
        qi = QuotaInfo(used=5, limit=10, reset_time=datetime.now())
        assert qi.used == 5
        assert qi.remaining == 5


class TestTokenBucket:
    """Test TokenBucket functionality."""

    @pytest.mark.asyncio
    async def test_consume_tokens(self):
        bucket = TokenBucket(capacity=5, refill_rate=1.0) # 1 token/sec
        assert await bucket.consume(1) is True
        assert abs(bucket.tokens - 4.0) < 0.01  # Allow small floating point differences

        # Consume more than available
        assert await bucket.consume(5) is False
        assert abs(bucket.tokens - 4.0) < 0.01  # Should not change

    @pytest.mark.asyncio
    @patch('time.time', side_effect=[0, 1, 2, 3, 4, 5])
    async def test_refill_tokens(self, mock_time):
        bucket = TokenBucket(capacity=5, refill_rate=1.0)
        await bucket.consume(5) # Empty the bucket
        assert bucket.tokens == 0

        await asyncio.sleep(1) # Simulate 1 second passing
        await bucket.consume(1) # This will trigger refill
        assert bucket.tokens == 0 # 1 token refilled, 1 consumed

        await asyncio.sleep(3) # Simulate 3 seconds passing
        await bucket.consume(1) # This will trigger refill
        assert bucket.tokens == 0 # 3 tokens refilled, 1 consumed (total 2, but capacity is 5)

    @pytest.mark.asyncio
    @patch('time.time', side_effect=[0, 1, 2, 3, 4, 5])
    async def test_wait_for_tokens(self, mock_time):
        bucket = TokenBucket(capacity=5, refill_rate=1.0)
        await bucket.consume(5)
        assert await bucket.wait_for_tokens(1) == 1.0 # Need 1 token, 1 sec refill rate
        assert await bucket.wait_for_tokens(3) == 3.0
        assert await bucket.wait_for_tokens(0) == 0.0


class TestSlidingWindowRateLimiter:
    """Test SlidingWindowRateLimiter functionality."""

    @pytest.fixture
    def mock_redis_client(self):
        mock_redis = MagicMock(spec=redis.Redis)
        mock_redis.pipeline.return_value = MagicMock()
        return mock_redis

    @pytest.mark.asyncio
    @patch('time.time', side_effect=[100, 101, 102, 103, 104, 105])
    async def test_is_allowed(self, mock_time, mock_redis_client):
        limiter = SlidingWindowRateLimiter(mock_redis_client)
        key = "test_key"
        limit = 3
        window = 10

        # Mock the pipeline to return a mock with async execute
        mock_pipeline = MagicMock()
        mock_redis_client.pipeline.return_value = mock_pipeline

        # First request - allowed
        mock_pipeline.execute = AsyncMock(return_value=[None, 0, None, None])
        allowed, quota = await limiter.is_allowed(key, limit, window)
        assert allowed is True
        assert quota.used == 0 # Before adding current request
        assert quota.remaining == 3

        # Second request - allowed
        mock_pipeline.execute = AsyncMock(return_value=[None, 1, None, None])
        allowed, quota = await limiter.is_allowed(key, limit, window)
        assert allowed is True
        assert quota.used == 1
        assert quota.remaining == 2

        # Third request - allowed
        mock_pipeline.execute = AsyncMock(return_value=[None, 2, None, None])
        allowed, quota = await limiter.is_allowed(key, limit, window)
        assert allowed is True
        assert quota.used == 2
        assert quota.remaining == 1

        # Fourth request - not allowed
        mock_redis_client.zrem = AsyncMock()
        mock_pipeline.execute = AsyncMock(return_value=[None, 3, None, None])
        allowed, quota = await limiter.is_allowed(key, limit, window)
        assert allowed is False
        assert quota.used == 3
        assert quota.remaining == 0
        mock_redis_client.zrem.assert_called_once() # Should remove the added request


class TestMediaRateLimiter:
    """Test MediaRateLimiter functionality."""

    @pytest.fixture
    def rate_limiter(self):
        with patch('redis.asyncio.from_url') as mock_from_url:
            mock_redis = MagicMock(spec=redis.Redis)
            mock_from_url.return_value = mock_redis
            limiter = MediaRateLimiter(redis_url="redis://localhost:6379")
            limiter.sliding_limiter = AsyncMock()
            return limiter

    def test_configure_provider(self, rate_limiter):
        rl = RateLimit(requests_per_minute=10, requests_per_hour=100, requests_per_day=1000)
        rate_limiter.configure_provider("test_provider", rl)
        assert "test_provider" in rate_limiter.provider_limits
        assert "test_provider" in rate_limiter.token_buckets
        assert rate_limiter.token_buckets["test_provider"].capacity == rl.burst_limit

    @pytest.mark.asyncio
    async def test_check_rate_limit_no_config(self, rate_limiter):
        allowed, quota = await rate_limiter.check_rate_limit("unconfigured_provider", 1)
        assert allowed is True
        assert quota == {}

    @pytest.mark.asyncio
    async def test_check_rate_limit_token_bucket_exceeded(self, rate_limiter):
        rl = RateLimit(requests_per_minute=10, requests_per_hour=100, requests_per_day=1000, burst_limit=1)
        rate_limiter.configure_provider("test_provider", rl)
        rate_limiter.token_buckets["test_provider"].consume = AsyncMock(side_effect=[True, False])
        # Mock sliding window for the first call
        rate_limiter.sliding_limiter.is_allowed = AsyncMock(return_value=(True, QuotaInfo(used=0, limit=10, remaining=10)))

        allowed, quota = await rate_limiter.check_rate_limit("test_provider", 1)
        assert allowed is True

        allowed, quota = await rate_limiter.check_rate_limit("test_provider", 1)
        assert allowed is False
        assert "burst" in quota
        assert quota["burst"].remaining == 0

    @pytest.mark.asyncio
    async def test_check_rate_limit_sliding_window_exceeded(self, rate_limiter):
        rl = RateLimit(requests_per_minute=1, requests_per_hour=100, requests_per_day=1000)
        rate_limiter.configure_provider("test_provider", rl)

        # Mock token bucket to always allow
        rate_limiter.token_buckets["test_provider"].consume = AsyncMock(return_value=True)

        # Mock sliding window to allow first check (all windows), but not second check (minute)
        rate_limiter.sliding_limiter.is_allowed = AsyncMock(side_effect=[
            (True, QuotaInfo(used=0, limit=1, remaining=1)), # First check, minute
            (True, QuotaInfo(used=0, limit=100, remaining=100)), # First check, hour
            (True, QuotaInfo(used=0, limit=1000, remaining=1000)), # First check, day
            (False, QuotaInfo(used=1, limit=1, remaining=0)), # Second check, minute
        ])

        # Mock Redis client for zrem
        rate_limiter.redis_client = AsyncMock()
        rate_limiter.redis_client.zrem = AsyncMock()
        rate_limiter.redis_client.zremrangebyscore = AsyncMock()
        rate_limiter.redis_client.zcard = AsyncMock(return_value=0)
        rate_limiter.redis_client.zadd = AsyncMock()
        rate_limiter.redis_client.expire = AsyncMock()

        allowed, quota = await rate_limiter.check_rate_limit("test_provider", 1)
        assert allowed is True

        allowed, quota = await rate_limiter.check_rate_limit("test_provider", 1)
        assert allowed is False
        assert "per_minute" in quota
        assert quota["per_minute"].remaining == 0

    @pytest.mark.asyncio
    async def test_get_wait_time(self, rate_limiter):
        rl = RateLimit(requests_per_minute=10, requests_per_hour=100, requests_per_day=1000, burst_limit=1)
        rate_limiter.configure_provider("test_provider", rl)
        rate_limiter.token_buckets["test_provider"].wait_for_tokens = AsyncMock(return_value=5.0)

        wait_time = await rate_limiter.get_wait_time("test_provider")
        assert wait_time == 5.0

    @pytest.mark.asyncio
    async def test_get_wait_time_no_config(self, rate_limiter):
        wait_time = await rate_limiter.get_wait_time("unconfigured_provider")
        assert wait_time == 0.0

    @pytest.mark.asyncio
    @patch('time.time', return_value=1000)
    async def test_get_quota_status(self, mock_time, rate_limiter):
        rl = RateLimit(requests_per_minute=10, requests_per_hour=100, requests_per_day=1000)
        rate_limiter.configure_provider("test_provider", rl)

        rate_limiter.redis_client.zcount = AsyncMock(side_effect=[5, 50, 500])

        status = await rate_limiter.get_quota_status("test_provider", 123)
        assert "per_minute" in status
        assert status["per_minute"].used == 5
        assert status["per_minute"].limit == 10
        assert status["per_minute"].remaining == 5

        assert "per_hour" in status
        assert status["per_hour"].used == 50

        assert "per_day" in status
        assert status["per_day"].used == 500

    @pytest.mark.asyncio
    async def test_cleanup(self, rate_limiter):
        rate_limiter.redis_client.close = AsyncMock()
        await rate_limiter.cleanup()
        rate_limiter.redis_client.close.assert_called_once()

    def test_default_rate_limits(self):
        assert "banana" in DEFAULT_RATE_LIMITS
        assert DEFAULT_RATE_LIMITS["banana"].requests_per_minute == 10