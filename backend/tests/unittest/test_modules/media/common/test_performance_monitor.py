"""
Tests for performance_monitor.py
"""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime, timedelta, timezone
import asyncio
import time
import statistics

from modules.media.common.performance_monitor import (
    PerformanceMetric,
    ProviderMetrics,
    MediaPerformanceMonitor
)

class TestPerformanceMetric:
    """Test PerformanceMetric dataclass functionality."""

    def test_performance_metric_init(self):
        now = datetime.utcnow()
        metric = PerformanceMetric(
            name="test_metric",
            value=10.5,
            timestamp=now,
            tags={"key": "value"},
            unit="s"
        )
        assert metric.name == "test_metric"
        assert metric.value == 10.5
        assert metric.timestamp == now
        assert metric.tags == {"key": "value"}
        assert metric.unit == "s"


class TestProviderMetrics:
    """Test ProviderMetrics dataclass functionality."""

    def test_provider_metrics_init(self):
        metrics = ProviderMetrics(provider_name="test_provider")
        assert metrics.provider_name == "test_provider"
        assert metrics.total_requests == 0
        assert metrics.successful_requests == 0
        assert metrics.failed_requests == 0
        assert metrics.total_latency == 0.0
        assert metrics.min_latency == float('inf')
        assert metrics.max_latency == 0.0
        assert len(metrics.latency_samples) == 0
        assert metrics.error_rate == 0.0
        assert metrics.throughput_per_minute == 0.0
        assert metrics.last_request_time is None

    def test_avg_latency(self):
        metrics = ProviderMetrics(provider_name="test")
        metrics.total_requests = 3
        metrics.total_latency = 300.0
        assert metrics.avg_latency == 100.0  # 300.0 / 3
        metrics.total_requests = 0
        assert metrics.avg_latency == 0.0

    def test_success_rate(self):
        metrics = ProviderMetrics(provider_name="test")
        metrics.total_requests = 10
        metrics.successful_requests = 8
        assert metrics.success_rate == 80.0  # (8 / 10) * 100
        metrics.total_requests = 0
        assert metrics.success_rate == 0.0

    def test_p95_latency(self):
        metrics = ProviderMetrics(provider_name="test")
        metrics.latency_samples.extend(range(1, 101)) # 1 to 100
        # 95th percentile of 1-100 is approximately 95.95
        assert metrics.p95_latency == 95.95
        metrics.latency_samples.clear()
        assert metrics.p95_latency == 0.0

    def test_p99_latency(self):
        metrics = ProviderMetrics(provider_name="test")
        metrics.latency_samples.extend(range(1, 101)) # 1 to 100
        # 99th percentile of 1-100 is approximately 99.99
        assert metrics.p99_latency == 99.99
        metrics.latency_samples.clear()
        assert metrics.p99_latency == 0.0


class TestMediaPerformanceMonitor:
    """Test MediaPerformanceMonitor functionality."""

    @pytest.fixture
    def monitor(self):
        return MediaPerformanceMonitor(metrics_retention_hours=0.1) # Short retention for testing

    @pytest.mark.asyncio
    @patch('time.time', side_effect=[1.0, 1.1, 1.2, 1.3, 1.4, 1.5]) # Mock time for latency calculation
    @patch('modules.media.common.performance_monitor.logger')
    async def test_track_request_success(self, mock_logger, mock_time, monitor):
        async def dummy_operation():
            await asyncio.sleep(0.05) # Simulate some work
            return "result"

        with patch('asyncio.sleep', new_callable=AsyncMock):
            async with monitor.track_request("provider1", "op1", "prod1") as request_id:
                result = await dummy_operation()

            assert result == "result"
            assert "request_id" not in monitor.system_metrics[0].tags # Implementation doesn't include request_id in tags
            assert monitor.provider_metrics["provider1"].total_requests == 1
            assert monitor.provider_metrics["provider1"].successful_requests == 1
            assert monitor.provider_metrics["provider1"].failed_requests == 0
            assert monitor.provider_metrics["provider1"].avg_latency > 0
            assert len(monitor.active_requests) == 0
            assert monitor.request_queue_size == 0
            mock_logger.warning.assert_not_called()

    @pytest.mark.asyncio
    @patch('time.time', side_effect=[1.0, 1.1, 1.2, 1.3, 1.4, 1.5])
    @patch('modules.media.common.performance_monitor.logger')
    async def test_track_request_failure(self, mock_logger, mock_time, monitor):
        async def dummy_operation_fail():
            await asyncio.sleep(0.05)
            raise ValueError("Operation failed")

        with patch('asyncio.sleep', new_callable=AsyncMock):
            with pytest.raises(ValueError, match="Operation failed"):
                async with monitor.track_request("provider2", "op2", "prod2") as request_id:
                    await dummy_operation_fail()

            assert monitor.provider_metrics["provider2"].total_requests == 1
            assert monitor.provider_metrics["provider2"].successful_requests == 0
            assert monitor.provider_metrics["provider2"].failed_requests == 1
            assert monitor.provider_metrics["provider2"].avg_latency > 0
            assert len(monitor.active_requests) == 0
            assert monitor.request_queue_size == 0
            mock_logger.warning.assert_called() # Implementation calls warning multiple times

    @patch('modules.media.common.performance_monitor.logger')
    def test_add_metric_retention(self, mock_logger, monitor):
        # Add a metric that should be retained
        monitor._add_metric("metric1", 10, timestamp=datetime.utcnow() - timedelta(minutes=1))
        assert len(monitor.system_metrics) == 1

        # Add a metric that should be cleaned up (older than retention)
        monitor._add_metric("metric2", 20, timestamp=datetime.utcnow() - timedelta(hours=1))
        assert len(monitor.system_metrics) == 1 # Only metric1 should remain

    @pytest.mark.asyncio
    @patch('modules.media.common.performance_monitor.logger')
    async def test_check_performance_alerts(self, mock_logger, monitor):
        if "alert_provider" not in monitor.provider_metrics:
            monitor.provider_metrics["alert_provider"] = ProviderMetrics(provider_name="alert_provider")
        metrics = monitor.provider_metrics["alert_provider"]

        # Trigger high latency alert
        metrics.max_latency = 30001 # Exceeds threshold
        metrics.successful_requests = 0  # Also trigger low success rate
        metrics.total_requests = 10
        await monitor._check_performance_alerts("alert_provider", metrics)
        # The first alert called should be high latency
        mock_logger.warning.assert_any_call('Performance Alert: High latency detected for alert_provider: 30001.00ms')
        mock_logger.warning.reset_mock()

        # Trigger low success rate alert
        metrics.total_requests = 100
        metrics.successful_requests = 90
        metrics.max_latency = 0 # Reset latency
        metrics.error_rate = 10.0 # Reset error rate
        await monitor._check_performance_alerts("alert_provider", metrics)
        mock_logger.warning.assert_any_call('Performance Alert: Low success rate for alert_provider: 90.00%')
        mock_logger.warning.reset_mock()

        # Trigger high error rate alert
        metrics.failed_requests = 6
        metrics.total_requests = 100
        metrics.error_rate = 6.0 # Exceeds threshold
        metrics.successful_requests = 94
        await monitor._check_performance_alerts("alert_provider", metrics)
        mock_logger.warning.assert_called_with('Performance Alert: High error rate for alert_provider: 6.00%')
        mock_logger.warning.reset_mock()

        # Trigger high queue size alert
        monitor.request_queue_size = 101
        await monitor._check_performance_alerts("alert_provider", metrics)
        mock_logger.warning.assert_called_with('Performance Alert: High queue size: 101 requests')
        mock_logger.warning.reset_mock()

    def test_get_provider_summary(self, monitor):
        if "provider_summary" not in monitor.provider_metrics:
            monitor.provider_metrics["provider_summary"] = ProviderMetrics(provider_name="provider_summary")
        metrics = monitor.provider_metrics["provider_summary"]
        metrics.total_requests = 10
        metrics.successful_requests = 9
        metrics.failed_requests = 1
        metrics.total_latency = 1000.0
        metrics.min_latency = 50.0
        metrics.max_latency = 150.0
        metrics.latency_samples.extend([100, 90, 110, 80, 120, 70, 130, 60, 140, 50])
        metrics.last_request_time = datetime(2023, 1, 1, 12, 0, 0)

        summary = monitor.get_provider_summary("provider_summary")
        assert summary["total_requests"] == 10
        assert summary["successful_requests"] == 9
        assert summary["success_rate_percent"] == 90.0
        assert summary["avg_latency_ms"] == 100.0
        assert summary["p95_latency_ms"] == 144.5 # Based on the sample data
        assert summary["last_request"] == "2023-01-01T12:00:00"

    def test_get_provider_summary_not_found(self, monitor):
        summary = monitor.get_provider_summary("non_existent_provider")
        assert summary == {"error": "Provider not found"}

    def test_get_system_summary(self, monitor):
        if "p1" not in monitor.provider_metrics:
            monitor.provider_metrics["p1"] = ProviderMetrics(provider_name="p1")
        monitor.provider_metrics["p1"].total_requests = 10
        monitor.provider_metrics["p1"].successful_requests = 8
        monitor.provider_metrics["p1"].failed_requests = 2

        if "p2" not in monitor.provider_metrics:
            monitor.provider_metrics["p2"] = ProviderMetrics(provider_name="p2")
        monitor.provider_metrics["p2"].total_requests = 5
        monitor.provider_metrics["p2"].successful_requests = 5
        monitor.provider_metrics["p2"].failed_requests = 0

        monitor.request_queue_size = 5
        monitor.active_requests["req1"] = datetime.utcnow()

        summary = monitor.get_system_summary()
        assert summary["total_requests"] == 15
        assert summary["successful_requests"] == 13
        assert summary["failed_requests"] == 2
        assert summary["overall_success_rate_percent"] == round((13/15)*100, 2)
        assert summary["overall_error_rate_percent"] == round((2/15)*100, 2)
        assert summary["active_requests"] == 1
        assert summary["queue_size"] == 5
        assert summary["providers_count"] == 2

    def test_get_all_providers_summary(self, monitor):
        if "p1" not in monitor.provider_metrics:
            monitor.provider_metrics["p1"] = ProviderMetrics(provider_name="p1")
        monitor.provider_metrics["p1"].total_requests = 1
        monitor.provider_metrics["p1"].successful_requests = 1

        if "p2" not in monitor.provider_metrics:
            monitor.provider_metrics["p2"] = ProviderMetrics(provider_name="p2")
        monitor.provider_metrics["p2"].total_requests = 1
        monitor.provider_metrics["p2"].successful_requests = 1

        all_summary = monitor.get_all_providers_summary()
        assert "system" in all_summary
        assert "providers" in all_summary
        assert "p1" in all_summary["providers"]
        assert "p2" in all_summary["providers"]

    @pytest.mark.asyncio
    async def test_record_custom_metric(self, monitor):
        await monitor.record_custom_metric("custom_event", 1, {"type": "info"}, "count")
        assert len(monitor.system_metrics) == 1
        metric = monitor.system_metrics[0]
        assert metric.name == "custom_event"
        assert metric.value == 1
        assert metric.tags == {"type": "info"}
        assert metric.unit == "count"

    @patch('modules.media.common.performance_monitor.logger')
    def test_reset_provider_metrics(self, mock_logger, monitor):
        if "p1" not in monitor.provider_metrics:
            monitor.provider_metrics["p1"] = ProviderMetrics(provider_name="p1")
        monitor.provider_metrics["p1"].total_requests = 10
        monitor.reset_provider_metrics("p1")
        assert "p1" not in monitor.provider_metrics
        mock_logger.info.assert_called_once_with("Reset metrics for provider: p1")

    @patch('modules.media.common.performance_monitor.logger')
    def test_reset_all_metrics(self, mock_logger, monitor):
        if "p1" not in monitor.provider_metrics:
            monitor.provider_metrics["p1"] = ProviderMetrics(provider_name="p1")
        monitor.provider_metrics["p1"].total_requests = 10
        monitor.system_metrics.append(PerformanceMetric("test", 1, datetime.utcnow()))
        monitor.active_requests["req1"] = datetime.utcnow()
        monitor.request_queue_size = 5

        monitor.reset_all_metrics()
        assert not monitor.provider_metrics
        assert not monitor.system_metrics
        assert not monitor.active_requests
        assert monitor.request_queue_size == 0
        mock_logger.info.assert_called_once_with("Reset all performance metrics")