"""
Tests for fallback_handlers.py
"""

import pytest
from unittest.mock import MagicMock, patch
from modules.media.common.fallback_handlers import (
    create_fallback_prompt,
    create_fallback_concept,
    _get_fallback_style_type,
    _get_fallback_camera_movements,
    _get_fallback_lighting,
    _get_fallback_music_style,
    create_fallback_text_content,
    handle_generation_fallback,
    _create_image_fallback,
    _create_video_fallback,
    _create_text_fallback
)
from modules.media.schemas import ProviderMediaRequest, ProviderMediaResult
from modules.media.engines.prompt_engine import MediaType

class TestFallbackHandlers:
    """Test Fallback Handlers functionality."""

    @pytest.fixture
    def mock_provider_media_request(self):
        mock_request = MagicMock(spec=ProviderMediaRequest)
        mock_request.product_title = "Test Product"
        mock_request.product_description = "A detailed description of the test product for various media types."
        mock_request.media_type = "image"
        return mock_request

    def test_create_fallback_prompt(self, mock_provider_media_request):
        variant = {"style_type": "lifestyle", "variant_name": "test_variant"}
        prompt_config = create_fallback_prompt(mock_provider_media_request, variant, MediaType.PRODUCT_PHOTOGRAPHY)

        assert "prompt" in prompt_config
        assert "negative_prompt" in prompt_config
        assert "style_type" in prompt_config
        assert prompt_config["style_type"] == "lifestyle"
        assert "Test Product" in prompt_config["prompt"]
        assert "lifestyle setting" in prompt_config["prompt"]

    def test_create_fallback_prompt_default_style(self, mock_provider_media_request):
        variant = {"variant_name": "test_variant"}
        prompt_config = create_fallback_prompt(mock_provider_media_request, variant, MediaType.PRODUCT_PHOTOGRAPHY)

        assert "style_type" in prompt_config
        assert prompt_config["style_type"] == "professional"
        assert "professional photography" in prompt_config["prompt"]

    def test_create_fallback_concept(self, mock_provider_media_request):
        variant = {"concept_type": "product_showcase", "duration_seconds": 60, "aspect_ratio": "9:16"}
        concept_config = create_fallback_concept(mock_provider_media_request, variant)

        assert "prompt" in concept_config
        assert "product_showcase" in concept_config["concept_type"]
        assert concept_config["duration_seconds"] == 60
        assert concept_config["aspect_ratio"] == "9:16"
        assert "Test Product" in concept_config["prompt"]
        assert "60-second video" in concept_config["prompt"]
        assert concept_config["style_type"] == "professional_showcase"
        assert concept_config["camera_movements"] == "smooth zoom and pan"
        assert concept_config["lighting"] == "professional studio lighting"
        assert concept_config["music_style"] == "upbeat corporate"

    @pytest.mark.parametrize(
        "concept_type, expected_style",
        [
            ("product_showcase", "professional_showcase"),
            ("lifestyle_integration", "lifestyle_natural"),
            ("social_media", "dynamic_engaging"),
            ("360_showcase", "product_rotation"),
            ("unknown", "professional"),
        ]
    )
    def test_get_fallback_style_type(self, concept_type, expected_style):
        assert _get_fallback_style_type(concept_type) == expected_style

    @pytest.mark.parametrize(
        "concept_type, expected_movement",
        [
            ("product_showcase", "smooth zoom and pan"),
            ("lifestyle_integration", "handheld natural movement"),
            ("social_media", "dynamic quick cuts"),
            ("360_showcase", "360-degree rotation"),
            ("unknown", "smooth"),
        ]
    )
    def test_get_fallback_camera_movements(self, concept_type, expected_movement):
        assert _get_fallback_camera_movements(concept_type) == expected_movement

    @pytest.mark.parametrize(
        "concept_type, expected_lighting",
        [
            ("product_showcase", "professional studio lighting"),
            ("lifestyle_integration", "natural ambient lighting"),
            ("social_media", "bright vibrant lighting"),
            ("360_showcase", "even diffused lighting"),
            ("unknown", "professional"),
        ]
    )
    def test_get_fallback_lighting(self, concept_type, expected_lighting):
        assert _get_fallback_lighting(concept_type) == expected_lighting

    @pytest.mark.parametrize(
        "concept_type, expected_music",
        [
            ("product_showcase", "upbeat corporate"),
            ("lifestyle_integration", "ambient lifestyle"),
            ("social_media", "trendy upbeat"),
            ("360_showcase", "minimal ambient"),
            ("unknown", "upbeat"),
        ]
    )
    def test_get_fallback_music_style(self, concept_type, expected_music):
        assert _get_fallback_music_style(concept_type) == expected_music

    def test_create_fallback_text_content_product_description(self, mock_provider_media_request):
        text_content = create_fallback_text_content(mock_provider_media_request, "product_description")
        assert "Discover Test Product" in text_content["text"]
        assert text_content["content_type"] == "product_description"

    def test_create_fallback_text_content_marketing_copy(self, mock_provider_media_request):
        text_content = create_fallback_text_content(mock_provider_media_request, "marketing_copy")
        assert "Introducing Test Product" in text_content["text"]
        assert text_content["content_type"] == "marketing_copy"

    def test_create_fallback_text_content_social_caption(self, mock_provider_media_request):
        text_content = create_fallback_text_content(mock_provider_media_request, "social_caption")
        assert "Check out this amazing Test Product" in text_content["text"]
        assert "#product #amazing" in text_content["text"]
        assert text_content["content_type"] == "social_caption"

    def test_create_fallback_text_content_generic(self, mock_provider_media_request):
        text_content = create_fallback_text_content(mock_provider_media_request, "generic_content")
        assert "This is a generic_content for Test Product." in text_content["text"]
        assert text_content["content_type"] == "generic_content"

    @patch('modules.media.common.fallback_handlers._create_image_fallback')
    @patch('modules.media.common.fallback_handlers.logger')
    def test_handle_generation_fallback_image(self, mock_logger, mock_create_image_fallback, mock_provider_media_request):
        mock_provider_media_request.media_type = "image"
        mock_create_image_fallback.return_value = MagicMock(spec=ProviderMediaResult, success=True)
        result = handle_generation_fallback(Exception("Image error"), mock_provider_media_request, {})
        mock_create_image_fallback.assert_called_once_with(mock_provider_media_request)
        assert result.success is True

    @patch('modules.media.common.fallback_handlers._create_video_fallback')
    @patch('modules.media.common.fallback_handlers.logger')
    def test_handle_generation_fallback_video(self, mock_logger, mock_create_video_fallback, mock_provider_media_request):
        mock_provider_media_request.media_type = "video"
        mock_create_video_fallback.return_value = MagicMock(spec=ProviderMediaResult, success=True)
        result = handle_generation_fallback(Exception("Video error"), mock_provider_media_request, {})
        mock_create_video_fallback.assert_called_once_with(mock_provider_media_request)
        assert result.success is True

    @patch('modules.media.common.fallback_handlers._create_text_fallback')
    @patch('modules.media.common.fallback_handlers.logger')
    def test_handle_generation_fallback_text(self, mock_logger, mock_create_text_fallback, mock_provider_media_request):
        mock_provider_media_request.media_type = "text"
        mock_create_text_fallback.return_value = MagicMock(spec=ProviderMediaResult, success=True)
        result = handle_generation_fallback(Exception("Text error"), mock_provider_media_request, {})
        mock_create_text_fallback.assert_called_once_with(mock_provider_media_request)
        assert result.success is True

    @patch('modules.media.common.fallback_handlers.logger')
    def test_handle_generation_fallback_unsupported_media_type(self, mock_logger, mock_provider_media_request):
        mock_provider_media_request.media_type = "unsupported"
        result = handle_generation_fallback(Exception("Unsupported error"), mock_provider_media_request, {})
        assert result.success is False
        assert "no fallback available" in result.error_message

    def test_create_image_fallback(self, mock_provider_media_request):
        result = _create_image_fallback(mock_provider_media_request)
        assert result.success is True
        assert result.provider_job_id == "fallback_image_generation"
        assert len(result.variants) == 1
        assert "data:image/svg+xml" in result.variants[0]["image_url"]

    def test_create_video_fallback(self, mock_provider_media_request):
        result = _create_video_fallback(mock_provider_media_request)
        assert result.success is True
        assert result.provider_job_id == "fallback_video_generation"
        assert len(result.variants) == 1
        assert "data:video/mp4" in result.variants[0]["video_url"]

    @patch('modules.media.common.fallback_handlers.create_fallback_text_content')
    def test_create_text_fallback(self, mock_create_fallback_text_content, mock_provider_media_request):
        mock_create_fallback_text_content.return_value = {"text": "fallback text"}
        result = _create_text_fallback(mock_provider_media_request)
        mock_create_fallback_text_content.assert_called_once_with(mock_provider_media_request, "product_description")
        assert result.success is True
        assert result.provider_job_id == "fallback_text_generation"
        assert len(result.variants) == 1
        assert result.variants[0]["text"] == "fallback text"