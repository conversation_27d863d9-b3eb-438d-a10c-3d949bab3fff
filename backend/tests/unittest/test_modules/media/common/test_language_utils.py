"""
Tests for language_utils.py
"""

import pytest
from modules.media.common.language_utils import (
    get_language_instruction,
    validate_language_code,
    get_supported_languages,
    get_language_display_name,
    LANGUAGE_INSTRUCTIONS
)

class TestLanguageUtils:
    """Test Language Utilities functionality."""

    def test_get_language_instruction_supported_language(self):
        """Test getting instruction for a supported language."""
        instruction = get_language_instruction("es")
        # The implementation adds fallback text for non-English languages
        expected = LANGUAGE_INSTRUCTIONS["es"] + " If unable to generate in es, fall back to English."
        assert instruction == expected

        instruction = get_language_instruction("es")
        assert instruction == expected

    def test_get_language_instruction_unsupported_language_with_fallback(self):
        """Test getting instruction for an unsupported language with fallback."""
        instruction = get_language_instruction("unsupported", fallback_language="es")
        expected = LANGUAGE_INSTRUCTIONS["es"]
        assert instruction == expected

    def test_get_language_instruction_unsupported_language_no_fallback(self):
        """Test getting instruction for an unsupported language with no specific fallback, should default to English."""
        instruction = get_language_instruction("unsupported")
        assert instruction == LANGUAGE_INSTRUCTIONS["en"] + " If unable to generate in unsupported, fall back to English."

    def test_get_language_instruction_unsupported_language_unsupported_fallback(self):
        """Test getting instruction for an unsupported language with an unsupported fallback, should default to English."""
        instruction = get_language_instruction("unsupported", fallback_language="unsupported2")
        assert instruction == LANGUAGE_INSTRUCTIONS["en"]

    def test_validate_language_code_supported(self):
        """Test validating a supported language code."""
        assert validate_language_code("en") is True
        assert validate_language_code("fr") is True

    def test_validate_language_code_unsupported(self):
        """Test validating an unsupported language code."""
        assert validate_language_code("xyz") is False
        assert validate_language_code("en-US") is False

    def test_get_supported_languages(self):
        """Test getting the list of supported languages."""
        supported_languages = get_supported_languages()
        assert isinstance(supported_languages, list)
        assert "en" in supported_languages
        assert "es" in supported_languages
        assert len(supported_languages) == len(LANGUAGE_INSTRUCTIONS)

    def test_get_language_display_name_supported(self):
        """Test getting display name for a supported language code."""
        assert get_language_display_name("en") == "English"
        assert get_language_display_name("ja") == "Japanese"

    def test_get_language_display_name_unsupported(self):
        """Test getting display name for an unsupported language code."""
        assert get_language_display_name("xyz") == "XYZ"
        assert get_language_display_name("unsupported") == "UNSUPPORTED"