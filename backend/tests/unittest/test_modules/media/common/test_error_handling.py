"""
Tests for error_handling.py
"""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime, timezone
import asyncio

from modules.media.common.error_handling import (
    ErrorSeverity,
    ErrorCategory,
    MediaError,
    MediaErrorHandler
)

class TestMediaError:
    """Test MediaError dataclass functionality."""

    def test_media_error_init(self):
        error = MediaError(
            category=ErrorCategory.NETWORK_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="Test message",
            provider="test_provider",
            product_id="123",
            job_id="abc",
            error_code="NET_001",
            details={"extra": "info"}
        )
        assert error.category == ErrorCategory.NETWORK_ERROR
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.message == "Test message"
        assert error.provider == "test_provider"
        assert error.product_id == "123"
        assert error.job_id == "abc"
        assert error.error_code == "NET_001"
        assert error.details == {"extra": "info"}
        assert isinstance(error.timestamp, datetime)
        assert error.retry_count == 0
        assert error.max_retries == 3

    def test_media_error_default_timestamp(self):
        error = MediaError(
            category=ErrorCategory.SYSTEM_ERROR,
            severity=ErrorSeverity.CRITICAL,
            message="System failure"
        )
        assert isinstance(error.timestamp, datetime)
        assert error.timestamp.tzinfo == timezone.utc


class TestMediaErrorHandler:
    """Test MediaErrorHandler functionality."""

    @pytest.fixture
    def error_handler(self):
        return MediaErrorHandler()

    @pytest.mark.parametrize(
        "error_message, error_type, context, expected_category",
        [
            ("Connection refused", "ConnectionError", {}, ErrorCategory.NETWORK_ERROR),
            ("API key invalid", "AuthenticationError", {}, ErrorCategory.AUTHENTICATION_ERROR),
            ("Quota exceeded", "RateLimitError", {}, ErrorCategory.QUOTA_ERROR),
            ("Content policy violation", "ContentError", {}, ErrorCategory.CONTENT_POLICY_ERROR),
            ("Invalid input", "ValidationError", {}, ErrorCategory.VALIDATION_ERROR),
            ("Operation timed out", "TimeoutError", {}, ErrorCategory.TIMEOUT_ERROR),
            ("Provider internal error", "ProviderError", {"provider": "test_provider"}, ErrorCategory.PROVIDER_ERROR),
            ("Unknown system issue", "Exception", {}, ErrorCategory.SYSTEM_ERROR),
        ]
    )
    @patch('traceback.format_exc', return_value="mock_traceback")
    def test_classify_error(self, mock_traceback, error_handler, error_message, error_type, context, expected_category):
        error = type(error_type, (Exception,), {})(error_message)
        media_error = error_handler.classify_error(error, context)
        assert media_error.category == expected_category
        assert media_error.message == error_message
        assert media_error.details["error_type"] == error_type
        assert media_error.details["traceback"] == "mock_traceback"
        assert media_error.details["context"] == context

    @pytest.mark.parametrize(
        "category, expected_severity",
        [
            (ErrorCategory.AUTHENTICATION_ERROR, ErrorSeverity.CRITICAL),
            (ErrorCategory.SYSTEM_ERROR, ErrorSeverity.CRITICAL),
            (ErrorCategory.PROVIDER_ERROR, ErrorSeverity.HIGH),
            (ErrorCategory.QUOTA_ERROR, ErrorSeverity.HIGH),
            (ErrorCategory.NETWORK_ERROR, ErrorSeverity.MEDIUM),
            (ErrorCategory.TIMEOUT_ERROR, ErrorSeverity.MEDIUM),
            (ErrorCategory.VALIDATION_ERROR, ErrorSeverity.LOW),
            (ErrorCategory.CONTENT_POLICY_ERROR, ErrorSeverity.LOW),
        ]
    )
    def test_determine_severity(self, error_handler, category, expected_severity):
        severity = error_handler._determine_severity(category, "message")
        assert severity == expected_severity

    @pytest.mark.parametrize(
        "category, retry_count, max_retries, expected_retry",
        [
            (ErrorCategory.NETWORK_ERROR, 0, 3, True),
            (ErrorCategory.PROVIDER_ERROR, 1, 3, True),
            (ErrorCategory.QUOTA_ERROR, 0, 1, True),
            (ErrorCategory.AUTHENTICATION_ERROR, 0, 3, False),
            (ErrorCategory.VALIDATION_ERROR, 0, 3, False),
            (ErrorCategory.CONTENT_POLICY_ERROR, 0, 3, False),
            (ErrorCategory.NETWORK_ERROR, 3, 3, False), # Max retries reached
        ]
    )
    def test_should_retry(self, error_handler, category, retry_count, max_retries, expected_retry):
        error = MediaError(category=category, severity=ErrorSeverity.LOW, message="test")
        error.retry_count = retry_count
        error.max_retries = max_retries
        assert error_handler.should_retry(error) == expected_retry

    @pytest.mark.parametrize(
        "category, retry_count, expected_delay",
        [
            (ErrorCategory.NETWORK_ERROR, 0, 2.0),
            (ErrorCategory.NETWORK_ERROR, 1, 4.0),
            (ErrorCategory.TIMEOUT_ERROR, 0, 3.0),
            (ErrorCategory.PROVIDER_ERROR, 2, 20.0),
            (ErrorCategory.QUOTA_ERROR, 1, 20.0),
            (ErrorCategory.SYSTEM_ERROR, 0, 1.0),
        ]
    )
    def test_get_retry_delay(self, error_handler, category, retry_count, expected_delay):
        error = MediaError(category=category, severity=ErrorSeverity.LOW, message="test")
        error.retry_count = retry_count
        assert error_handler.get_retry_delay(error) == expected_delay

    @pytest.mark.asyncio
    @patch('asyncio.sleep', new_callable=AsyncMock)
    @patch('modules.media.common.error_handling.logger')
    async def test_handle_error_with_retry_success(self, mock_logger, mock_sleep, error_handler):
        mock_operation = AsyncMock(return_value="Success")
        result = await error_handler.handle_error_with_retry(mock_operation, {"provider": "test"})
        mock_operation.assert_called_once()
        mock_sleep.assert_not_called()
        assert result == "Success"
        mock_logger.warning.assert_not_called()
        mock_logger.error.assert_not_called()

    @pytest.mark.asyncio
    @patch('asyncio.sleep', new_callable=AsyncMock)
    @patch('modules.media.common.error_handling.logger')
    @patch('traceback.format_exc', return_value="mock_traceback")
    async def test_handle_error_with_retry_failure_no_retry(self, mock_traceback, mock_logger, mock_sleep, error_handler):
        mock_operation = AsyncMock(side_effect=ValueError("Validation error"))
        with pytest.raises(Exception, match="Operation failed after retries: Validation error"):
            await error_handler.handle_error_with_retry(mock_operation, {"provider": "test"}, max_retries=0)
        mock_operation.assert_called_once()
        mock_sleep.assert_not_called()
        mock_logger.warning.assert_called_once()
        mock_logger.error.assert_called_once()

    @pytest.mark.asyncio
    @patch('asyncio.sleep', new_callable=AsyncMock)
    @patch('modules.media.common.error_handling.logger')
    @patch('traceback.format_exc', return_value="mock_traceback")
    async def test_handle_error_with_retry_retries_then_success(self, mock_traceback, mock_logger, mock_sleep, error_handler):
        mock_operation = AsyncMock(side_effect=[ConnectionError("Network issue"), "Success"])
        result = await error_handler.handle_error_with_retry(mock_operation, {"provider": "test"}, max_retries=1)
        assert mock_operation.call_count == 2
        mock_sleep.assert_called_once()
        assert result == "Success"
        mock_logger.warning.assert_called_once()
        mock_logger.error.assert_not_called()

    @pytest.mark.asyncio
    @patch('asyncio.sleep', new_callable=AsyncMock)
    @patch('modules.media.common.error_handling.logger')
    @patch('traceback.format_exc', return_value="mock_traceback")
    async def test_handle_error_with_retry_all_retries_fail(self, mock_traceback, mock_logger, mock_sleep, error_handler):
        mock_operation = AsyncMock(side_effect=ConnectionError("Network issue"))
        with pytest.raises(Exception, match="Operation failed after retries: Network issue"):
            await error_handler.handle_error_with_retry(mock_operation, {"provider": "test"}, max_retries=2)
        assert mock_operation.call_count == 3 # 1 initial + 2 retries
        assert mock_sleep.call_count == 2
        assert mock_logger.warning.call_count == 3
        mock_logger.error.assert_called_once()

    @pytest.mark.asyncio
    @patch('asyncio.sleep', new_callable=AsyncMock)
    @patch('modules.media.common.error_handling.logger')
    @patch('traceback.format_exc', return_value="mock_traceback")
    async def test_handle_error_with_retry_non_retryable_error(self, mock_traceback, mock_logger, mock_sleep, error_handler):
        mock_operation = AsyncMock(side_effect=ValueError("Invalid API Key"))
        with pytest.raises(Exception, match="Operation failed after retries: Invalid API Key"):
            await error_handler.handle_error_with_retry(mock_operation, {"provider": "test"}, max_retries=3)
        mock_operation.assert_called_once()
        mock_sleep.assert_not_called()
        mock_logger.warning.assert_called_once()
        mock_logger.error.assert_called_once()