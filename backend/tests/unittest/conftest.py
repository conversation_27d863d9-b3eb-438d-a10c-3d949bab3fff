# tests/conftest.py
import sys
from pathlib import Path

# Add src/ to sys.path so imports like `modules.media.models` work
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent / "src"))

import os
from typing import AsyncGenerator, Generator
from unittest.mock import patch

import pytest
import pytest_asyncio
from httpx import ASGITransport, AsyncClient
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import sessionmaker  # <-- missing import

# Set testing environment
os.environ["TESTING"] = "True"
# Set provider overrides for testing to use example providers
os.environ["IMAGE_PROVIDER_OVERRIDE"] = "example_image"
os.environ["VIDEO_PROVIDER_OVERRIDE"] = "example_video"
os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"
os.environ["PLATFORM_PUSH_OVERRIDE"] = "example"

from passlib.context import CryptContext

from core.config import get_settings
from core.db.database import Base, get_db
from core.utils.logging import setup_logging
from modules.auth.service import auth_service
from modules.auth.models import User, Tenant
from modules.stores.models import Store
from servers.api.main import app

setup_logging()

settings = get_settings()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


@pytest_asyncio.fixture(scope="function")
async def db_engine() -> AsyncGenerator[AsyncEngine, None]:
    """
    Pytest fixture to create a test database engine for each test.
    Uses PostgreSQL for testing.
    """
    # Use the configured database URL for testing
    settings = get_settings()
    test_db_url = settings.DATABASE_URL
    engine = create_async_engine(test_db_url, echo=False)

    # Create schema for each test
    # async with engine.begin() as conn:
    #     await conn.run_sync(Base.metadata.drop_all)
    #     await conn.run_sync(Base.metadata.create_all)

    yield engine

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def db_session(db_engine: AsyncEngine) -> AsyncGenerator[AsyncSession, None]:
    """
    Pytest fixture to provide an async database session for each test function.
    """
    Session = async_sessionmaker(bind=db_engine, expire_on_commit=False)
    async with Session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """
    Pytest fixture to create a basic, unauthenticated async test client.
    """

    def override_get_db() -> Generator[AsyncSession, None, None]:
        yield db_session

    app.dependency_overrides[get_db] = override_get_db

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as c:
        yield c

    del app.dependency_overrides[get_db]


@pytest_asyncio.fixture(scope="function")
async def test_user(db_session: AsyncSession) -> User:
    """
    Pytest fixture to create a test user.
    """
    import uuid
    unique_email = f"test-{uuid.uuid4()}@example.com"
    test_user = User(
        email=unique_email,
        password_hash=pwd_context.hash("testpassword"),
        first_name="Test",
        last_name="User",
        is_active=True,
    )

    db_session.add(test_user)
    await db_session.commit()
    await db_session.refresh(test_user)

    return test_user


@pytest_asyncio.fixture(scope="function")
async def test_tenant(db_session: AsyncSession, test_user: User) -> Tenant:
    """
    Pytest fixture to create a test tenant for the test user.
    """
    import uuid
    unique_slug = f"test-tenant-{uuid.uuid4().hex[:8]}"

    test_tenant = Tenant(
        name=f"{test_user.first_name or 'Test'}'s Workspace",
        slug=unique_slug,
        owner_id=test_user.id,
        is_active=True,
        plan_tier="starter",
        storage_limit_gb=5.0,
        storage_used_gb=0.0,
        billing_email=test_user.email,
        credits=1000.0
    )

    db_session.add(test_tenant)
    await db_session.commit()
    await db_session.refresh(test_tenant)

    return test_tenant


@pytest_asyncio.fixture(scope="function")
async def test_store(db_session: AsyncSession, test_user: User, test_tenant: Tenant) -> Store:
    """
    Pytest fixture to create a test store for the test user.
    """
    test_store = Store(
        platform="shopify",
        shop_domain="test-shop.myshopify.com",
        shop_name="Test Shop",
        shop_id="123456789",
        admin_access_token="test_access_token",
        is_active=True,
        owner_id=test_user.id,
        tenant_id=test_tenant.id
    )

    db_session.add(test_store)
    await db_session.commit()
    await db_session.refresh(test_store)

    return test_store


@pytest_asyncio.fixture(scope="function")
async def authenticated_client(
    db_session: AsyncSession, client: AsyncClient, test_user: User
) -> AsyncClient:
    """
    Pytest fixture to create an authenticated async test client.
    """
    token = auth_service.create_access_token(data={"sub": str(test_user.id), "email": test_user.email})
    client.headers.update({"Authorization": f"Bearer {token}"})

    return client

@pytest.fixture
def auth_headers(test_user):
    """Authentication headers for test requests."""
    from modules.auth.service import auth_service
    token = auth_service.create_access_token(data={"sub": str(test_user.id), "email": test_user.email})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def mock_shopify_oauth():
    """Mock Shopify OAuth service for testing."""
    with patch("plugins.shopify.oauth_service.shopify_oauth_service") as mock:
        mock.generate_install_url.return_value = "https://test-shop.myshopify.com/admin/oauth/authorize?..."
        mock.exchange_code_for_token.return_value = {
            "access_token": "test_token",
            "scope": "read_products,write_products"
        }
        mock.get_shop_info.return_value = {
            "id": 12345,
            "name": "Test Shop",
            "domain": "test-shop.myshopify.com"
        }
        yield mock


@pytest.fixture
def banana_test_data():
    """Test data for Banana image provider."""
    import json
    with open(Path(__file__).parent.parent / "test_data" / "banana_metadata.json", "r") as f:
        data = json.load(f)
    return data["result"]


@pytest.fixture
def veo3_test_data():
    """Test data for Veo3 video provider."""
    import json
    with open(Path(__file__).parent.parent / "test_data" / "veo3_metadata_with_image.json", "r") as f:
        data = json.load(f)
    return data["result"]


@pytest.fixture
def gemini_test_data():
    """Test data for Gemini text provider."""
    import json
    with open(Path(__file__).parent.parent / "test_data" / "gemini_metadata.json", "r") as f:
        data = json.load(f)
    return data["result"]


@pytest.fixture
def sample_media_request_dict():
    """Sample media generation request dictionary."""
    return {
        "mode": "image",
        "media_type": "image",
        "model": "banana",
        "items": [
            {
                "product_id": 123,
                "product_context": {
                    "title": "Premium Wireless Headphones",
                    "description": "High-quality wireless headphones with noise cancellation",
                    "category": "electronics",
                    "price": 199.99,
                    "currency": "USD"
                }
            }
        ],
        "product_ids": [123]
    }
