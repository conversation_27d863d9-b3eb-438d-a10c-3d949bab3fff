#!/usr/bin/env python3
"""
Script to reset test environment by dropping all database tables and clearing Redis queues.

Usage:
    python init_tables.py              # Clear both Redis queues and reset database
    python init_tables.py redis        # Clear only Redis queues
    python init_tables.py db          # Reset only database tables
"""
import asyncio
import sys
import subprocess
from pathlib import Path

# Add src/ to sys.path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent / "backend" / "src"))

import os
os.environ["TESTING"] = "True"

from core.config import get_settings
from core.db.database import Base
from core.db import models # Import models to ensure Base.metadata is populated
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import redis

def drop_and_recreate_database():
    """Drop and recreate the test database using init-db.sql approach"""
    import psycopg2

    # Connect to postgres database to manage test database
    conn = psycopg2.connect(
        host="localhost",
        port=5432,
        user="app_user",
        password="dev_password",
        database="postgres"
    )
    conn.autocommit = True  # Required for DROP DATABASE
    
    database_name = "test_ecommerce_db"

    try:
        with conn.cursor() as cursor:
            # Terminate active connections to test database
            cursor.execute(f"""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '{database_name}' AND pid <> pg_backend_pid()
            """)

            # Drop and recreate test database
            cursor.execute(f"DROP DATABASE IF EXISTS {database_name}")
            cursor.execute(f"CREATE DATABASE {database_name}")
            cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {database_name} TO app_user")

        print("Test database has been dropped and recreated successfully.")
    finally:
        conn.close()

def create_all_tables():
    """Create all tables using alembic migrations"""
    settings = get_settings()

    # Set environment variable for alembic
    env = os.environ.copy()
    env["DATABASE_URL"] = settings.DATABASE_URL

    try:
        # Run alembic upgrade head to create all tables
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=Path(__file__).resolve().parent.parent,
            env=env,
            capture_output=True,
            text=True,
            check=True
        )
        print("Successfully applied alembic migrations to create all tables.")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Failed to apply alembic migrations: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        raise


def clear_redis_queues():
    """Clear all Redis queues used by Celery."""
    settings = get_settings()

    try:
        # Connect to Redis
        redis_client = redis.Redis.from_url(settings.REDIS_URL)

        # Common Celery queue patterns
        queue_patterns = [
            "celery",  # Default Celery queue
            "media-generation",  # Media generation queue
            "media-push",  # Media push queue
            "webhook-processing",  # Webhook processing queue
            "analytics",  # Analytics queue
            "sync",  # Sync queue
        ]

        cleared_queues = []
        total_keys_deleted = 0

        # Clear each queue pattern
        for pattern in queue_patterns:
            # Get all keys matching the pattern
            keys = redis_client.keys(f"{pattern}*")

            if keys:
                # Delete the keys
                deleted_count = redis_client.delete(*keys)
                total_keys_deleted += deleted_count
                cleared_queues.append(f"{pattern}* ({deleted_count} keys)")
                print(f"Cleared {deleted_count} keys from {pattern}*")

        # Also clear any unacked tasks
        unacked_keys = redis_client.keys("unacked*")
        if unacked_keys:
            unacked_deleted = redis_client.delete(*unacked_keys)
            total_keys_deleted += unacked_deleted
            cleared_queues.append(f"unacked* ({unacked_deleted} keys)")
            print(f"Cleared {unacked_deleted} unacked task keys")

        # Clear any scheduled tasks (eta keys)
        eta_keys = redis_client.keys("*_eta*")
        if eta_keys:
            eta_deleted = redis_client.delete(*eta_keys)
            total_keys_deleted += eta_deleted
            cleared_queues.append(f"*_eta* ({eta_deleted} keys)")
            print(f"Cleared {eta_deleted} scheduled task keys")

        redis_client.close()

        if cleared_queues:
            print(f"Redis queues cleared successfully: {', '.join(cleared_queues)}")
            print(f"Total Redis keys deleted: {total_keys_deleted}")
        else:
            print("No Redis queues found to clear")

    except Exception as e:
        print(f"Warning: Failed to clear Redis queues: {e}")
        print("Continuing with database cleanup...")

if __name__ == "__main__":
    import sys

    # Check command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "redis":
            print("Clearing Redis queues only...")
            clear_redis_queues()
            print("Redis cleanup complete.")
            sys.exit(0)
        elif command == "db":
            print("Resetting database tables only...")
            drop_and_recreate_database()
            create_all_tables()
            print("Database cleanup complete.")
            sys.exit(0)
        else:
            print("Usage: python init_tables.py [redis|db]")
            print("  redis - Clear only Redis queues")
            print("  db    - Reset only database tables")
            print("  (no args) - Clear both Redis and reset database")
            sys.exit(1)

    # Default behavior: clear both
    print("Starting test database and Redis cleanup...")

    # Clear Redis queues first
    print("\n=== Clearing Redis Queues ===")
    clear_redis_queues()

    # Then handle database tables
    print("\n=== Dropping and Recreating Database ===")
    drop_and_recreate_database()

    print("\n=== Creating Database Tables ===")
    create_all_tables()

    print("\n=== Cleanup Complete ===")
    print("Test database and Redis queues have been reset successfully.")