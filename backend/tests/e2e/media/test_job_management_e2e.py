"""
End-to-End tests for Media Job Management APIs.

Tests job listing, status checking, cancellation, and retry functionality.
"""

import asyncio
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.stores.models import Store


class TestJobManagementE2E:
    """End-to-end tests for job management APIs."""

    @pytest.mark.asyncio
    async def test_list_jobs_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job listing with pagination and filtering."""
        
        # Create multiple jobs
        jobs_created = []
        
        for i, media_type in enumerate(["image", "video", "text"]):
            request_data = sample_media_requests[f"{media_type}_request"].copy()
            request_data["shop_id"] = test_store.id
            request_data["items"][0]["product_id"] = 100 + i
            
            response = await e2e_client.post("/api/media/generate", json=request_data)
            assert response.status_code == 200
            
            job_info = response.json()["jobs"][0]
            jobs_created.append(job_info["job_id"])
            cleanup_test_jobs(job_info["job_id"])
        
        # Wait a moment for jobs to be created
        await asyncio.sleep(1)
        
        # Test basic job listing
        response = await e2e_client.get("/api/media/jobs")
        assert response.status_code == 200
        
        jobs_data = response.json()
        assert "jobs" in jobs_data
        assert "total" in jobs_data
        assert "page" in jobs_data
        assert "per_page" in jobs_data
        
        # Should have at least the jobs we created
        assert jobs_data["total"] >= 3
        assert len(jobs_data["jobs"]) >= 3

    @pytest.mark.asyncio
    async def test_list_jobs_with_pagination(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job listing with pagination parameters."""
        
        # Create several jobs
        for i in range(5):
            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id
            request_data["items"][0]["product_id"] = 200 + i
            
            response = await e2e_client.post("/api/media/generate", json=request_data)
            job_info = response.json()["jobs"][0]
            cleanup_test_jobs(job_info["job_id"])
        
        await asyncio.sleep(1)
        
        # Test pagination
        response = await e2e_client.get("/api/media/jobs?page=1&per_page=2")
        assert response.status_code == 200
        
        jobs_data = response.json()
        assert jobs_data["page"] == 1
        assert jobs_data["per_page"] == 2
        assert len(jobs_data["jobs"]) <= 2

    @pytest.mark.asyncio
    async def test_list_jobs_with_status_filter(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test job listing with status filtering."""
        
        # Create a job and let it complete
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        await wait_for_job_completion(e2e_client, str(db_job.external_id))
        
        # Test filtering by completed status
        response = await e2e_client.get("/api/media/jobs?status_filter=completed")
        assert response.status_code == 200
        
        jobs_data = response.json()
        # Should have at least one completed job
        completed_jobs = [job for job in jobs_data["jobs"] if job.get("status") == "completed"]
        assert len(completed_jobs) >= 1

    @pytest.mark.asyncio
    async def test_list_jobs_with_product_filter(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job listing with product ID filtering."""
        
        # Create jobs for specific products
        product_ids = [301, 302, 303]
        
        for product_id in product_ids:
            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id
            request_data["items"][0]["product_id"] = product_id
            
            response = await e2e_client.post("/api/media/generate", json=request_data)
            job_info = response.json()["jobs"][0]
            cleanup_test_jobs(job_info["job_id"])
        
        await asyncio.sleep(1)
        
        # Filter by specific product
        response = await e2e_client.get(f"/api/media/jobs?product_id=301")
        assert response.status_code == 200
        
        jobs_data = response.json()
        # Should have jobs for product 301
        product_jobs = [job for job in jobs_data["jobs"] if job.get("product_id") == 301]
        assert len(product_jobs) >= 1

    @pytest.mark.asyncio
    async def test_get_job_status_detailed_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test detailed job status endpoint."""
        
        # Create and complete a job
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Test status endpoint during processing
        status_response = await e2e_client.get(f"/api/media/jobs/{job_id}/status")
        assert status_response.status_code == 200

        status_data = status_response.json()
        assert "job_id" in status_data
        assert "status" in status_data
        assert "progress" in status_data
        assert "variants" in status_data

        # Wait for completion and check final status
        await wait_for_job_completion(e2e_client, job_id)

        final_status_response = await e2e_client.get(f"/api/media/jobs/{job_id}/status")
        assert final_status_response.status_code == 200

        final_status_data = final_status_response.json()
        assert final_status_data["status"] == "completed"
        assert len(final_status_data["variants"]) == 4

    @pytest.mark.asyncio
    async def test_job_retry_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job retry functionality."""
        
        # Create a job
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait a moment for job to start
        await asyncio.sleep(2)

        # Try to retry the job
        retry_response = await e2e_client.post(f"/api/media/jobs/{job_id}/retry")
        
        # Response depends on job state - might succeed or fail
        if retry_response.status_code == 200:
            retry_data = retry_response.json()
            assert "message" in retry_data
            assert "new_job_id" in retry_data
            
            # Register new job for cleanup
            new_job_id = retry_data["new_job_id"]
            cleanup_test_jobs(str(new_job_id))
        else:
            # Job might not be in a retryable state
            assert retry_response.status_code in [400, 404]

    @pytest.mark.asyncio
    async def test_job_status_unauthorized_access(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession
    ):
        """Test that users can't access other users' jobs."""
        
        # Try to access a non-existent job (simulates unauthorized access)
        response = await e2e_client.get("/api/media/jobs/99999")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_job_progress_tracking(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job progress tracking throughout execution."""
        
        # Create a job
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Track progress over time
        progress_values = []

        for _ in range(15):  # Check for 30 seconds
            status_response = await e2e_client.get(f"/api/media/jobs/{job_id}")
            if status_response.status_code == 200:
                status_data = status_response.json()
                progress = status_data.get("progress", 0)
                progress_values.append(progress)

                if status_data.get("status") in ["completed", "failed"]:
                    break

            await asyncio.sleep(2)
        
        # Progress should be tracked (even if mocked)
        assert len(progress_values) > 0
        # Final progress should be 100 if completed
        if progress_values and progress_values[-1] is not None:
            assert 0 <= progress_values[-1] <= 100

    @pytest.mark.asyncio
    async def test_concurrent_job_processing(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test that multiple jobs can be processed concurrently."""
        
        # Submit multiple jobs simultaneously
        job_ids = []
        
        for i in range(3):
            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id
            request_data["items"][0]["product_id"] = 400 + i
            
            response = await e2e_client.post("/api/media/generate", json=request_data)
            assert response.status_code == 200
            
            job_info = response.json()["jobs"][0]
            job_ids.append(job_info["job_id"])
            cleanup_test_jobs(job_info["job_id"])
        
        # Wait a moment for jobs to start
        await asyncio.sleep(2)
        
        # Check that all jobs are being processed
        active_jobs = 0
        
        for job_id in job_ids:
            # Get database job by external_id
            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.external_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()

            if db_job:
                status_response = await e2e_client.get(f"/api/media/jobs/{job_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    if status_data.get("status") in ["pending", "processing"]:
                        active_jobs += 1
        
        # Should have multiple jobs active (or completed quickly)
        assert active_jobs >= 0  # At least some jobs should be tracked