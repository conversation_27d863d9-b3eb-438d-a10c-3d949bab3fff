"""
Advanced End-to-End tests for Image Generation Workflows.

These tests cover advanced image generation scenarios including:
- Multiple aspect ratios and formats
- Batch processing
- Template integration
- Error handling and edge cases
- Storage and CDN verification
- Performance and quality settings
"""

import asyncio
import pytest
import time
from pathlib import Path
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.stores.models import Store


class TestAdvancedImageGenerationE2E:
    """Advanced E2E tests for image generation workflows."""

    @pytest.mark.asyncio
    async def test_multiple_aspect_ratios_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test image generation with multiple aspect ratios in a single request."""

        # Create request with multiple aspect ratios
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        request_data["settings"]["aspect_ratios"] = ["1:1", "9:16", "16:9", "4:5"]

        # Submit generation request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)

        # Get database job
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        assert db_job is not None

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        assert completed_job["status"] == "completed"

        # Verify variants for each aspect ratio
        variants_result = await db_session.execute(
            select(MediaVariant).filter(MediaVariant.job_id == db_job.id)
        )
        variants = variants_result.scalars().all()

        # Should have variants for each aspect ratio
        assert len(variants) == 4

        # Verify all variants are completed with generic names
        for variant in variants:
            assert variant.status == MediaVariantStatus.COMPLETED
            assert variant.image_url is not None
            assert variant.variant_name in ["variant_1", "variant_2", "variant_3", "variant_4"]

    @pytest.mark.asyncio
    async def test_batch_product_processing_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test batch processing of multiple products in a single request."""

        # Create request with multiple products
        request_data = {
            "mode": "image",
            "media_type": "image",
            "model": "example_image",
            "shop_id": test_store.id,
            "items": [
                {
                    "product_id": 1001,
                    "product_context": {
                        "title": "Wireless Headphones",
                        "description": "Premium wireless headphones",
                        "category": "electronics",
                        "colors": ["black", "white"]
                    }
                },
                {
                    "product_id": 1002,
                    "product_context": {
                        "title": "Running Shoes",
                        "description": "Lightweight running shoes",
                        "category": "footwear",
                        "colors": ["blue", "red"]
                    }
                },
                {
                    "product_id": 1003,
                    "product_context": {
                        "title": "Coffee Maker",
                        "description": "Automatic coffee maker",
                        "category": "home_decor",
                        "colors": ["silver", "black"]
                    }
                }
            ],
            "settings": {
                "aspect_ratio": "1:1",
                "quality": "high"
            }
        }

        # Submit batch request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        assert len(response_data["jobs"]) == 3  # One job per product

        # Register all jobs for cleanup
        for job_info in response_data["jobs"]:
            cleanup_test_jobs(job_info["job_id"])

        # Verify all jobs were created and completed
        for job_info in response_data["jobs"]:
            job_id = job_info["job_id"]

            # Get database job
            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.external_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()
            assert db_job is not None
            assert db_job.media_type == "image"

            # Wait for completion
            completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
            assert completed_job["status"] == "completed"

            # Verify variants were created
            variants_result = await db_session.execute(
                select(MediaVariant).filter(MediaVariant.job_id == db_job.id)
            )
            variants = variants_result.scalars().all()
            assert len(variants) > 0

            for variant in variants:
                assert variant.status == MediaVariantStatus.COMPLETED
                assert variant.image_url is not None

    @pytest.mark.asyncio
    async def test_template_integration_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test image generation with template integration."""

        # Create request with template
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        request_data["template_id"] = "product_showcase"  # Assuming this template exists
        request_data["settings"]["template_customizations"] = {
            "background_color": "#ffffff",
            "text_overlay": True,
            "brand_logo": True
        }

        # Submit request with template
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)

        # Get database job
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        assert db_job is not None
        assert db_job.template_id == "product_showcase"

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        assert completed_job["status"] == "completed"

        # Verify template was applied (check custom config)
        settings = db_job.custom_config.get("settings", {})
        assert "template_customizations" in settings
        assert settings["template_customizations"]["background_color"] == "#ffffff"

    @pytest.mark.asyncio
    async def test_quality_settings_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test image generation with different quality settings."""

        quality_settings = [
            {"guidance": 7.5, "steps": 25, "upscale": True},
            {"guidance": 12.0, "steps": 50, "upscale": False},
            {"guidance": 5.0, "steps": 15, "upscale": True}
        ]

        for i, quality in enumerate(quality_settings):
            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id
            request_data["settings"].update(quality)
            request_data["items"][0]["product_id"] = 2000 + i  # Unique product ID

            # Submit request
            response = await e2e_client.post("/api/media/generate", json=request_data)
            assert response.status_code == 200

            response_data = response.json()
            job_info = response_data["jobs"][0]
            job_id = job_info["job_id"]
            cleanup_test_jobs(job_id)

            # Get database job
            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.external_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()
            assert db_job is not None

            # Verify quality settings were stored
            stored_settings = db_job.custom_config.get("settings", {})
            assert stored_settings.get("guidance") == quality["guidance"]
            assert stored_settings.get("steps") == quality["steps"]
            assert stored_settings.get("upscale") == quality["upscale"]

            # Wait for completion
            completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
            assert completed_job["status"] == "completed"

    @pytest.mark.asyncio
    async def test_error_handling_invalid_request_e2e(
        self,
        e2e_client: AsyncClient,
        test_store: Store
    ):
        """Test error handling for invalid image generation requests."""

        # Test missing required fields
        invalid_requests = [
            {
                "media_type": "image",
                "shop_id": test_store.id
                # Missing model and items
            },
            {
                "media_type": "image",
                "model": "invalid_provider",
                "shop_id": test_store.id,
                "items": []
            },
            {
                "media_type": "image",
                "model": "example_image",
                "shop_id": test_store.id,
                "items": [
                    {
                        "product_id": "invalid_id",  # Should be integer
                        "product_context": {}
                    }
                ]
            }
        ]

        for invalid_request in invalid_requests:
            response = await e2e_client.post("/api/media/generate", json=invalid_request)
            # Should return error status
            assert response.status_code in [400, 422]

    @pytest.mark.asyncio
    async def test_storage_and_cdn_integration_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test storage and CDN integration for generated images."""

        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        request_data["settings"]["store_in_cdn"] = True

        # Submit request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)

        # Get database job
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        assert completed_job["status"] == "completed"

        # Verify storage integration
        variants_result = await db_session.execute(
            select(MediaVariant).filter(MediaVariant.job_id == db_job.id)
        )
        variants = variants_result.scalars().all()

        for variant in variants:
            assert variant.status == MediaVariantStatus.COMPLETED

            # Check if image is stored (either as file or data URL)
            if variant.image_url:
                if variant.image_url.startswith("data:"):
                    # Base64 data URL
                    assert "base64," in variant.image_url
                else:
                    # File path - verify file exists
                    file_path = Path(variant.image_url)
                    if file_path.exists():
                        assert file_path.stat().st_size > 0

            # Check for CDN URL if enabled
            if request_data["settings"].get("store_in_cdn"):
                # Should have CDN URL or storage path
                assert variant.image_url or hasattr(variant, 'cdn_url')

    @pytest.mark.asyncio
    async def test_performance_batch_processing_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        cleanup_test_jobs
    ):
        """Test performance of batch image processing."""

        # Create a larger batch for performance testing
        items = []
        for i in range(5):  # Process 5 products
            items.append({
                "product_id": 3000 + i,
                "product_context": {
                    "title": f"Product {i}",
                    "description": f"Description for product {i}",
                    "category": "electronics",
                    "colors": ["blue", "red"]
                }
            })

        request_data = {
            "mode": "image",
            "media_type": "image",
            "model": "example_image",
            "shop_id": test_store.id,
            "items": items,
            "settings": {
                "aspect_ratio": "1:1",
                "quality": "standard"  # Use standard quality for faster processing
            }
        }

        start_time = time.time()

        # Submit batch request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        assert len(response_data["jobs"]) == 5

        # Register all jobs for cleanup
        for job_info in response_data["jobs"]:
            cleanup_test_jobs(job_info["job_id"])

        # Wait for all jobs to complete
        completed_count = 0
        max_wait = 120  # 2 minutes for batch processing

        for job_info in response_data["jobs"]:
            job_id = job_info["job_id"]

            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.external_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()

            # Wait for this specific job
            job_start = time.time()
            while time.time() - job_start < 60:  # 1 minute per job
                await db_session.refresh(db_job)
                if db_job.status == MediaJobStatus.COMPLETED:
                    completed_count += 1
                    break
                await asyncio.sleep(2)

        # Verify performance metrics
        total_time = time.time() - start_time
        assert completed_count == 5, f"Only {completed_count} out of 5 jobs completed"
        assert total_time < max_wait, f"Batch processing took too long: {total_time}s"

        # Log performance metrics
        avg_time_per_job = total_time / 5
        print(f"Batch processing completed in {total_time:.2f}s")
        print(f"Average time per job: {avg_time_per_job:.2f}s")

    @pytest.mark.asyncio
    async def test_image_variant_regeneration_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test regeneration of specific image variants."""

        # First, create an initial job
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id

        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)

        # Get database job
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for initial completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        assert completed_job["status"] == "completed"

        # Get a variant to regenerate
        variants_result = await db_session.execute(
            select(MediaVariant).filter(MediaVariant.job_id == db_job.id)
        )
        variants = variants_result.scalars().all()
        assert len(variants) > 0

        target_variant = variants[0]
        original_url = target_variant.image_url

        # Regenerate the variant with different settings
        regenerate_data = {
            "override_params": {
                "guidance": 10.0,
                "style": "minimalist"
            }
        }

        regenerate_response = await e2e_client.post(
            f"/api/media/variants/{target_variant.external_id}/regenerate",
            json=regenerate_data
        )
        assert regenerate_response.status_code == 200

        # Wait for regeneration to complete
        await asyncio.sleep(5)  # Give time for regeneration

        # Refresh variant data
        await db_session.refresh(target_variant)

        # Verify the variant was updated
        assert target_variant.image_url is not None
        # URL should be different after regeneration (in real scenario)
        # assert target_variant.image_url != original_url