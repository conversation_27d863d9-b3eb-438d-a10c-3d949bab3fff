"""
End-to-End tests for Error Handling in Media APIs.

Tests various error scenarios and edge cases.
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import patch

from modules.stores.models import Store


class TestErrorHandlingE2E:
    """End-to-end tests for error handling scenarios."""

    @pytest.mark.asyncio
    async def test_invalid_media_request_format(
        self,
        e2e_client: AsyncClient,
        test_store: Store
    ):
        """Test handling of invalid request formats."""
        
        # Test missing required fields
        invalid_request = {
            "mode": "image",
            # Missing items and other required fields
        }
        
        response = await e2e_client.post("/api/media/generate", json=invalid_request)
        assert response.status_code in [400, 422]  # Validation error

    @pytest.mark.asyncio
    async def test_unauthorized_access(
        self,
        client: AsyncClient  # Unauthenticated client
    ):
        """Test unauthorized access to media APIs."""
        
        request_data = {
            "mode": "image",
            "media_type": "image",
            "model": "banana",
            "items": [{"product_id": 123}]
        }
        
        response = await client.post("/api/media/generate", json=request_data)
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_invalid_shop_access(
        self,
        e2e_client: AsyncClient,
        test_store: Store
    ):
        """Test access to non-existent or unauthorized shop."""

        request_data = {
            "mode": "image",
            "media_type": "image",
            "model": "banana",
            "shop_id": 99999,  # Non-existent shop
            "items": [{"product_id": 123}]
        }

        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_provider_failure_handling(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test handling when media provider fails."""

        # Mock provider to fail
        with patch("modules.media.providers.image.banana.BananaProvider.generate_media") as mock_provider:
            mock_provider.side_effect = Exception("Provider API error")

            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id

            response = await e2e_client.post("/api/media/generate", json=request_data)

            # Should still create job successfully
            assert response.status_code == 200

            job_info = response.json()["jobs"][0]
            # Register job for cleanup (the fixture will handle cleanup after test)
            cleanup_test_jobs(job_info["job_id"])

            # Job should eventually fail
            # Note: In real E2E, we'd wait and check the job status

    @pytest.mark.asyncio
    async def test_database_connection_failure(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test handling of database connection issues."""

        # This is harder to test in E2E without actually breaking the DB
        # In a real scenario, you might use a separate test DB that you can manipulate

        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id

        # For now, just ensure the endpoint is robust
        response = await e2e_client.post("/api/media/generate", json=request_data)

        # Should either succeed or fail gracefully
        assert response.status_code in [200, 500]

        # Clean up any jobs created
        if response.status_code == 200:
            job_info = response.json()["jobs"][0]
            cleanup_test_jobs(job_info["job_id"])

    @pytest.mark.asyncio
    async def test_queue_service_failure(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test handling when queue service is unavailable."""

        # Mock queue service to fail
        with patch("modules.queue.queue_service.celery_service.enqueue_media_generation") as mock_queue:
            mock_queue.side_effect = Exception("Queue service unavailable")

            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id

            response = await e2e_client.post("/api/media/generate", json=request_data)

            # Should handle queue failure gracefully
            assert response.status_code in [200, 500]

            # Clean up any jobs created (even if queue failed, job might still be created)
            if response.status_code == 200:
                job_info = response.json()["jobs"][0]
                cleanup_test_jobs(job_info["job_id"])

    @pytest.mark.asyncio
    async def test_invalid_job_id_access(
        self,
        e2e_client: AsyncClient
    ):
        """Test access to invalid job IDs."""
        
        # Test non-existent job ID
        response = await e2e_client.get("/api/media/jobs/99999")
        assert response.status_code == 404
        
        # Test invalid job ID format
        response = await e2e_client.get("/api/media/jobs/invalid")
        assert response.status_code in [400, 404, 422]

    @pytest.mark.asyncio
    async def test_malformed_json_request(
        self,
        e2e_client: AsyncClient
    ):
        """Test handling of malformed JSON requests."""
        
        # Send invalid JSON
        response = await e2e_client.post(
            "/api/media/generate",
            content="invalid json content",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_oversized_request(
        self,
        e2e_client: AsyncClient,
        test_store: Store
    ):
        """Test handling of oversized requests."""

        # Create a request with fewer items to avoid creating too many jobs
        large_request = {
            "mode": "image",
            "media_type": "image",
            "model": "banana",
            "shop_id": test_store.id,
            "items": [
                {
                    "product_id": i,
                    "product_context": {
                        "title": f"Product {i}",
                        "description": "A" * 1000  # Large description
                    }
                }
                for i in range(60)  # Use 60 items to test the 50-item limit
            ]
        }

        response = await e2e_client.post("/api/media/generate", json=large_request)

        # Should reject requests with too many items
        assert response.status_code == 422
        assert "Too many items" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_concurrent_request_limits(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test handling of too many concurrent requests."""

        import asyncio

        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id

        # Submit fewer concurrent requests to avoid overwhelming the system
        tasks = []
        created_jobs = []
        for i in range(3):  # Reduced from 10 to 3
            request_copy = request_data.copy()
            request_copy["items"][0]["product_id"] = 500 + i
            task = e2e_client.post("/api/media/generate", json=request_copy)
            tasks.append(task)

        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # Should handle concurrent requests gracefully
        success_count = 0
        for response in responses:
            if hasattr(response, 'status_code') and response.status_code == 200:
                success_count += 1
                # Collect job IDs for cleanup
                job_info = response.json()["jobs"][0]
                created_jobs.append(job_info["job_id"])

        # At least some requests should succeed
        assert success_count > 0

        # Clean up all created jobs
        for job_id in created_jobs:
            cleanup_test_jobs(job_id)

    @pytest.mark.asyncio
    async def test_invalid_model_specification(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        cleanup_test_jobs
    ):
        """Test handling of invalid model specifications."""

        request_data = {
            "mode": "image",
            "media_type": "image",
            "model": "nonexistent_model",  # Invalid model
            "shop_id": test_store.id,
            "items": [{"product_id": 123}]
        }

        response = await e2e_client.post("/api/media/generate", json=request_data)

        # Should either fallback to default model or reject
        assert response.status_code in [200, 400, 422]

        # Clean up any jobs created
        if response.status_code == 200:
            job_info = response.json()["jobs"][0]
            cleanup_test_jobs(job_info["job_id"])

    @pytest.mark.asyncio
    async def test_network_timeout_simulation(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test handling of network timeouts."""

        # Mock provider to simulate timeout
        with patch("modules.media.providers.image.banana.BananaProvider.generate_media") as mock_provider:
            import asyncio

            async def slow_response(*args, **kwargs):
                await asyncio.sleep(10)  # Simulate slow response
                return {"success": False, "error": "Timeout"}

            mock_provider.side_effect = slow_response

            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id

            # This test might timeout itself, so we'll just ensure the endpoint exists
            response = await e2e_client.post("/api/media/generate", json=request_data)
            assert response.status_code in [200, 500, 504]

            # Clean up any jobs created
            if response.status_code == 200:
                job_info = response.json()["jobs"][0]
                cleanup_test_jobs(job_info["job_id"])

    @pytest.mark.asyncio
    async def test_storage_service_failure(
        self,
        e2e_client: AsyncClient,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test handling when storage service fails."""

        # Mock storage service to fail
        with patch("modules.storage.storage_service.media_storage_service.upload_media") as mock_storage:
            mock_storage.side_effect = Exception("Storage service unavailable")

            request_data = sample_media_requests["image_request"].copy()
            request_data["shop_id"] = test_store.id

            response = await e2e_client.post("/api/media/generate", json=request_data)

            # Should handle storage failure gracefully
            assert response.status_code in [200, 500]

            # Clean up any jobs created
            if response.status_code == 200:
                job_info = response.json()["jobs"][0]
                cleanup_test_jobs(job_info["job_id"])