"""
E2E test configuration for media generation APIs.
This file sets up the test environment with real database, queue, and worker processes.
Only the provider implementations are mocked.
"""

import asyncio
import os
import signal
import subprocess
import time
from pathlib import Path
from typing import AsyncGenerator, Dict, Any, List
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.media.schemas import ProviderMediaResult
from core.db.database import Base
from modules.storage.storage_service import media_storage_service


@pytest.fixture(scope="session", autouse=True)
async def setup_test_database():
    """Drop all tables and recreate them for E2E tests."""
    from sqlalchemy.ext.asyncio import create_async_engine
    from core.config import get_settings

    settings = get_settings()
    engine = create_async_engine(settings.DATABASE_URL, echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)

    await engine.dispose()



@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for the test session."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def worker_process():
    """Start the Celery worker process for E2E testing."""
    # Ensure we're in testing mode
    os.environ["TESTING"] = "True"
    # Set DEBUG logging for detailed test output
    os.environ["LOG_LEVEL"] = "INFO"
    # Set provider override for testing
    os.environ["IMAGE_PROVIDER_OVERRIDE"] = "example_image"
    os.environ["VIDEO_PROVIDER_OVERRIDE"] = "example_video"
    os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"
    os.environ["PLATFORM_PUSH_OVERRIDE"] = "example"

    settings = get_settings()

    # Start worker process
    worker_cmd = [
        "uv", "run", "celery",
        "-A", "servers.worker.main", "worker",
        "--loglevel=info",
        "-Q", "media-generation,media-push,celery"
    ]

    # Change to backend/src directory
    backend_src_dir = Path(__file__).parent.parent.parent.parent / "src"

    print(f"Starting Celery worker in {backend_src_dir}")
    worker_process = subprocess.Popen(
        worker_cmd,
        cwd=str(backend_src_dir),
        env={**os.environ, "PYTHONPATH": str(backend_src_dir)},
        # Don't capture stdout/stderr so we can see worker output
        preexec_fn=os.setsid  # Create new process group
    )

    # Check if worker is running
    if worker_process.poll() is not None:
        stdout, stderr = worker_process.communicate()
        raise RuntimeError(f"Worker failed to start (exit code: {worker_process.returncode})\nSTDOUT: {stdout.decode()}\nSTDERR: {stderr.decode()}")

    # Give worker time to initialize and check for early errors
    time.sleep(3)
    if worker_process.poll() is not None:
        stdout, stderr = worker_process.communicate()
        raise RuntimeError(f"Worker crashed shortly after start (exit code: {worker_process.returncode})\nSTDOUT: {stdout.decode()}\nSTDERR: {stderr.decode()}")

    print("Celery worker started successfully")

    yield worker_process

    # Cleanup: terminate worker
    print("Terminating Celery worker")
    try:
        # Send SIGTERM to the process group
        os.killpg(os.getpgid(worker_process.pid), signal.SIGTERM)
        worker_process.wait(timeout=10)
    except (subprocess.TimeoutExpired, ProcessLookupError):
        # Force kill if graceful shutdown fails
        try:
            os.killpg(os.getpgid(worker_process.pid), signal.SIGKILL)
        except ProcessLookupError:
            pass

    print("Celery worker terminated")


@pytest_asyncio.fixture
async def e2e_client(
    db_session: AsyncSession,
    authenticated_client: AsyncClient,
    worker_process,
    # mock_providers,
    test_tenant  # Ensure tenant with credits exists
) -> AsyncClient:
    """
    E2E test client with worker process running and example providers initialized.
    """
    # Ensure worker is ready
    await asyncio.sleep(1)

    return authenticated_client


@pytest.fixture
def sample_media_requests():
    """Sample media generation requests for testing."""
    return {
        "image_request": {
            "mode": "image",
            "media_type": "image",
            "model": "example_image",  # Explicit provider specification required
            "items": [
                {
                    "product_id": 123,
                    "product_context": {
                        "title": "Premium Wireless Headphones",
                        "description": "High-quality wireless headphones with noise cancellation",
                        "category": "electronics",
                        "price": 199.99,
                        "currency": "USD",
                        "colors": ["black", "white"],
                        "key_features": ["wireless", "noise cancellation", "long battery life"]
                    }
                }
            ],
            "settings": {
                "aspect_ratio": "1:1",
                "guidance": 7.5,
                "steps": 25,
                "upscale": True
            }
        },
        "video_request": {
            "mode": "video",
            "media_type": "video",
            "model": "example_video",  # Explicit provider specification required
            "items": [
                {
                    "product_id": 456,
                    "product_context": {
                        "title": "Running Shoes",
                        "description": "Lightweight running shoes for athletes",
                        "category": "footwear",
                        "price": 129.99,
                        "currency": "USD",
                        "colors": ["red", "blue"],
                        "key_features": ["lightweight", "breathable", "durable"]
                    }
                }
            ],
            "settings": {
                "aspect_ratio": "16:9",
                "quality": "high"
            }
        },
        "text_request": {
            "mode": "text",
            "media_type": "text",
            "model": "example_text",  # Explicit provider specification required
            "items": [
                {
                    "product_id": 789,
                    "product_context": {
                        "title": "Leather Handbag",
                        "description": "Elegant leather handbag for professionals",
                        "category": "accessories",
                        "price": 299.99,
                        "currency": "USD",
                        "materials": ["leather"],
                        "target_audience": ["professionals", "luxury_buyers"]
                    }
                }
            ],
            "settings": {
                "style": "professional",
                "tone": "elegant"
            }
        }
    }


@pytest.fixture
def wait_for_job_completion():
    """Helper function to wait for job completion."""
    async def _wait_for_completion(client: AsyncClient, job_id: str, timeout: int = 60):
        """Wait for a job to complete with timeout."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = await client.get(f"/api/media/jobs/{job_id}")
            if response.status_code == 200:
                job_data = response.json()
                status = job_data.get("status")
                
                if status in ["completed", "failed"]:
                    return job_data
                    
            await asyncio.sleep(2)  # Check every 2 seconds
        
        raise TimeoutError(f"Job {job_id} did not complete within {timeout} seconds")
    
    return _wait_for_completion


@pytest_asyncio.fixture
async def cleanup_test_jobs(db_session: AsyncSession):
    """Cleanup test jobs after each test."""
    created_jobs = []

    def register_job(job_id):
        created_jobs.append(job_id)

    yield register_job

    # Implement proper cleanup to prevent job accumulation
    if created_jobs:
        from sqlalchemy import delete
        from modules.media.models import MediaJob, MediaVariant

        try:
            from sqlalchemy import select
            for external_job_id in created_jobs:
                # Fetch the MediaJob to get its internal integer ID
                job_result = await db_session.execute(
                    select(MediaJob).filter(MediaJob.external_id == external_job_id)
                )
                job = job_result.scalar_one_or_none()

                if job:
                    # Delete variants using the internal job.id
                    await db_session.execute(
                        delete(MediaVariant).where(MediaVariant.job_id == job.id)
                    )
                    # Delete the job using its external_id
                    await db_session.execute(
                        delete(MediaJob).where(MediaJob.external_id == external_job_id)
                    )
                else:
                    print(f"Warning: Job with external_id {external_job_id} not found during cleanup.")
            await db_session.commit()
            print(f"Cleaned up {len(created_jobs)} test jobs")
        except Exception as e:
            print(f"Failed to cleanup test jobs: {e}")
            await db_session.rollback()
