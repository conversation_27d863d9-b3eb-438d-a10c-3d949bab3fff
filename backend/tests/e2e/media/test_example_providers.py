"""
Test script to verify example providers work correctly.
Run this to ensure the new example providers are functioning properly.
"""

import asyncio
import pytest
from pathlib import Path

from modules.media.providers.config import ProviderConfig
from modules.media.providers.image import ExampleImageProvider
from modules.media.providers.text import ExampleTextProvider
from modules.media.providers.video import ExampleVideoProvider
from modules.media.schemas import ProviderMediaRequest


@pytest.mark.asyncio
async def test_example_image_provider():
    """Test the ExampleImageProvider works correctly."""

    # Create provider configuration
    config = ProviderConfig.from_dict("example_image", {
        "type": "image",
        "api_key": "",
        "timeout": 30,
        "model": "example_model",
        "capabilities": {
            "supported_formats": ["image"],
            "supported_styles": ["example_style", "test_style"],
            "supported_categories": ["test", "demo"]
        },
        "limits": {
            "max_variants_per_request": 2,
            "requests_per_minute": 100,
            "requests_per_hour": 1000
        },
        "costs": {"cost_per_unit": 0.0, "currency": "USD"},
        "quality": {"quality_score": 0.9, "average_generation_time": 5},
        "features": ["Example image generation", "Test data provider", "No API key required"],
        "image_config": {
            "aspect_ratios": {
                "square": {"width": 1024, "height": 1024},
                "landscape": {"width": 1920, "height": 1080}
            },
            "generation_params": {
                "quality": "high",
                "style": "example",
                "negative_prompt": "",
                "guidance_scale": 1.0,
                "num_inference_steps": 1
            }
        },
        "metadata": {
            "provider_name": "Example Image Provider",
            "description": "Example provider for testing and demonstration purposes"
        }
    })

    # Create and initialize provider
    provider = ExampleImageProvider()
    success = await provider.initialize(config)
    assert success, "Provider initialization failed"

    # Set up storage service
    from modules.storage.storage_service import media_storage_service
    provider.set_storage_service(media_storage_service)

    # Test media generation
    request = ProviderMediaRequest(
        product_title="Test Wireless Headphones",
        media_type="image",
        num_images=2,
        tenant_id=1  # Set tenant_id to avoid issues
    )

    result = await provider.generate_media(request)

    # Verify results
    assert result.success, f"Generation failed: {result.error_message}"
    assert len(result.variants) == 2, f"Expected 2 images, got {len(result.variants)}"
    assert result.provider_job_id == "example_image_job_123"

    # Verify image data
    for image in result.variants:
        assert "image_url" in image
        assert "thumbnail_url" in image
        assert "width" in image
        assert "height" in image
        assert "style" in image
        assert "variant_name" in image
        assert "prompt_used" in image
        assert "generation_metadata" in image

        # Check that URLs are properly formatted (storage URLs for uploaded images)
        assert image["image_url"].startswith("https://")
        assert "test-storage.com" in image["image_url"]
        assert image["image_url"].endswith(".png")

    print("✅ ExampleImageProvider test passed!")


@pytest.mark.asyncio
async def test_example_text_provider():
    """Test the ExampleTextProvider works correctly."""

    # Create provider configuration
    config = ProviderConfig.from_dict("example_text", {
        "type": "text",
        "api_key": "",
        "timeout": 30,
        "model": "example_model",
        "capabilities": {
            "supported_formats": ["text"],
            "content_types": ["product_description", "marketing_copy", "social_caption", "seo_snippet"],
            "supported_languages": ["en"]
        },
        "limits": {
            "max_variants_per_request": 4,
            "requests_per_minute": 100,
            "requests_per_hour": 1000,
            "token_limits": {
                "product_description": 300,
                "marketing_copy": 400,
                "social_caption": 100,
                "seo_snippet": 200,
                "default": 300
            }
        },
        "costs": {"cost_per_unit": 0.0, "currency": "USD"},
        "quality": {"quality_score": 0.95, "average_generation_time": 2},
        "features": ["Example text generation", "Test data provider", "No API key required"],
        "text_config": {
            "temperature_settings": {"default": 0.7},
            "generation_params": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50,
                "max_tokens": 1000,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
                "stop_sequences": []
            },
            "estimated_completion_time_per_variant": 2
        },
        "metadata": {
            "provider_name": "Example Text Provider",
            "description": "Example provider for testing and demonstration purposes"
        }
    })

    # Create and initialize provider
    provider = ExampleTextProvider()
    success = await provider.initialize(config)
    assert success, "Provider initialization failed"

    # Test media generation
    request = ProviderMediaRequest(
        product_title="Test Leather Handbag",
        media_type="text",
        variants_count=2
    )

    result = await provider.generate_media(request)

    # Verify results
    assert result.success, f"Generation failed: {result.error_message}"
    assert len(result.variants) == 2, f"Expected 2 variants, got {len(result.variants)}"
    assert result.provider_job_id == "example_text_job_456"

    # Verify text data
    for variant in result.variants:
        assert "content_type" in variant
        assert "text" in variant
        assert "word_count" in variant
        assert "character_count" in variant
        assert "variant_name" in variant
        assert "language" in variant
        assert "prompt_used" in variant

        # Verify text content is not empty
        assert len(variant["text"]) > 0
        assert variant["word_count"] > 0
        assert variant["character_count"] > 0

    print("✅ ExampleTextProvider test passed!")


@pytest.mark.asyncio
async def test_example_video_provider():
    """Test the ExampleVideoProvider works correctly."""

    # Create provider configuration
    config = ProviderConfig.from_dict("example_video", {
        "type": "video",
        "api_key": "",
        "timeout": 60,
        "model": "example_model",
        "capabilities": {
            "supported_formats": ["video"],
            "supported_aspect_ratios": ["16:9", "1:1", "9:16"],
            "max_duration_seconds": 10,
            "supported_person_generation": []
        },
        "limits": {
            "max_variants_per_request": 1,
            "requests_per_minute": 50,
            "requests_per_hour": 500
        },
        "costs": {"cost_per_unit": 0.0, "currency": "USD"},
        "quality": {"quality_score": 0.88, "average_generation_time": 10},
        "features": ["Example video generation", "Test data provider", "No API key required"],
        "video_config": {
            "variant_aspect_ratios": {
                "square": "1:1",
                "vertical": "9:16",
                "horizontal": "16:9"
            },
            "aspect_resolutions": {
                "1:1": "1024x1024",
                "9:16": "1080x1920",
                "16:9": "1920x1080"
            },
            "generation_params": {
                "resolution": "1080p",
                "fps": 30,
                "duration_seconds": 8,
                "codec": "H.264",
                "bitrate": "high"
            }
        },
        "metadata": {
            "provider_name": "Example Video Provider",
            "description": "Example provider for testing and demonstration purposes"
        }
    })

    # Create and initialize provider
    provider = ExampleVideoProvider()
    success = await provider.initialize(config)
    assert success, "Provider initialization failed"

    # Set up storage service
    from modules.storage.storage_service import media_storage_service
    provider.set_storage_service(media_storage_service)

    # Test media generation
    request = ProviderMediaRequest(
        product_title="Test Running Shoes",
        media_type="video",
        num_videos=1,
        tenant_id=1  # Set tenant_id to avoid issues
    )

    result = await provider.generate_media(request)

    # Verify results
    assert result.success, f"Generation failed: {result.error_message}"
    assert len(result.variants) == 4, f"Expected 4 variants, got {len(result.variants)}"  # Video provider generates 4 variants by default
    assert result.provider_job_id == "example_video_job_789"

    # Verify video data
    variant = result.variants[0]
    assert "variant_name" in variant
    assert "video_url" in variant
    assert "thumbnail_url" in variant
    assert "duration_seconds" in variant
    assert "resolution" in variant
    assert "aspect_ratio" in variant
    assert "style_type" in variant
    assert "prompt_used" in variant
    assert "generation_metadata" in variant

    # Verify video URL is properly formatted (storage URL for uploaded video)
    assert variant["video_url"].startswith("https://")
    assert "test-storage.com" in variant["video_url"]
    assert variant["video_url"].endswith(".mp4")

    # Verify expected values
    assert variant["duration_seconds"] == 8.0
    assert variant["resolution"] == "1920x1080"
    assert variant["aspect_ratio"] == "16:9"

    print("✅ ExampleVideoProvider test passed!")


if __name__ == "__main__":
    # Run tests directly
    asyncio.run(test_example_image_provider())
    asyncio.run(test_example_text_provider())
    asyncio.run(test_example_video_provider())
    print("\n🎉 All example provider tests passed!")