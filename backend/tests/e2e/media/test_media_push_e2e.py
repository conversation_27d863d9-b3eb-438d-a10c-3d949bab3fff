"""
End-to-End tests for Media Push APIs.

Tests the complete workflow of pushing generated media to platforms.
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import os
from pathlib import Path

from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.stores.models import Store
from core.config import get_settings


@pytest.fixture(autouse=True)
def clear_example_log():
    """Fixture to clear the example plugin's log file before and after each test."""
    settings = get_settings()
    log_path = Path(settings.LOG_DIR) / 'example_push.log'
    
    if log_path.exists():
        log_path.unlink()
    
    yield


class TestMediaPushE2E:
    """End-to-end tests for media push workflow."""

    def get_example_log_path(self) -> Path:
        """Helper to get the path to the example plugin's log file."""
        settings = get_settings()
        return Path(settings.LOG_DIR) / 'example_push.log'

    @pytest.mark.asyncio
    async def test_push_media_to_platform_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test complete workflow of pushing media to platform."""
        
        # Step 1: Generate media first
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200
        
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        assert completed_job["status"] == "completed"

        # Get a variant ID
        variant_id = completed_job["variants"][0]["variant_id"]
        product_id = db_job.product_id
        
        # Step 2: Prepare push request to use the example plugin
        push_request = {
            "shop_id": test_store.id,
            "product_id": int(product_id),
            "variant_id": variant_id,
            "publish_targets": ["example"], # Use the example plugin
            "publish_options": {
                "alt_text": "Premium wireless headphones",
                "position": 1,
                "replace_existing": False
            }
        }
        
        # Step 3: Push media
        push_response = await e2e_client.post("/api/media/push", json=push_request)
        assert push_response.status_code == 200
        
        push_data = push_response.json()
        assert push_data["success"] is True
        assert push_data["status"] == "completed"
        assert "push_id" in push_data
        
        # Verify the example plugin logged the push
        log_path = self.get_example_log_path()
        with open(log_path, 'r') as f:
            log_content = f.read()
        media_variant = completed_job['variants'][0]
        media_url_to_check = media_variant.get('image_url') or media_variant.get('video_url')
        assert media_url_to_check is not None, "Media URL not found in variant"
        expected_log_prefix = f"Pushed media: {media_url_to_check} to product {product_id} on {test_store.shop_domain}"
        print(f"\n--- Log Content ---\n{log_content}--- End Log Content ---")
        assert any(expected_log_prefix in line for line in log_content.splitlines()), f"Log entry with prefix '{expected_log_prefix}' not found in log file."

    @pytest.mark.asyncio
    async def test_push_media_unauthorized_store(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store
    ):
        """Test push request with unauthorized store access."""
        
        # Try to push to a store that doesn't belong to the user
        push_request = {
            "shop_id": test_store.id + 1,  # Unauthorized store ID
            "product_id": 123,
            "variant_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", # Dummy UUID for testing invalid access
            "publish_targets": ["shopify"]
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        assert response.status_code == 403
        assert "Access denied" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_push_media_multiple_platforms(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test pushing media to multiple platforms."""
        
        # Generate media first
        request_data = sample_media_requests["video_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        variant_id = completed_job["variants"][0]["variant_id"]
        
        # Push to multiple platforms using the example plugin
        push_request = {
            "shop_id": test_store.id,
            "product_id": int(db_job.product_id),
            "variant_id": variant_id,
            "publish_targets": ["example", "example"], # Use the example plugin twice
            "publish_options": {
                "alt_text": "Running shoes video",
                "replace_existing": True
            }
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        assert response.status_code == 200
        
        push_data = response.json()
        assert push_data["success"] is True
        
        # Verify the example plugin logged the push twice
        log_path = self.get_example_log_path()
        with open(log_path, 'r') as f:
            log_content = f.read()
        
        media_variant = completed_job['variants'][0]
        media_url_to_check = media_variant.get('image_url') or media_variant.get('video_url')
        assert media_url_to_check is not None, "Media URL not found in variant"
        expected_log_prefix = f"Pushed media: {media_url_to_check} to product {db_job.product_id} on {test_store.shop_domain}"
        print(f"\n--- Log Content ---\n{log_content}--- End Log Content ---")
        assert sum(1 for line in log_content.splitlines() if expected_log_prefix in line) == 2, f"Expected 2 log entries with prefix '{expected_log_prefix}' but found a different count."

    @pytest.mark.asyncio
    async def test_push_media_with_custom_options(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test push with custom publishing options."""
        
        # Generate media first
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        variant_id = completed_job["variants"][0]["variant_id"]

        # Push with detailed options using the example plugin
        push_request = {
            "shop_id": test_store.id,
            "product_id": int(db_job.product_id),
            "variant_id": variant_id,
            "publish_targets": ["example"], # Use the example plugin
            "publish_options": {
                "alt_text": "Premium wireless headphones in black and white",
                "position": 2,
                "replace_existing": True
            }
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        assert response.status_code == 200
        
        # Verify the example plugin logged the push
        log_path = self.get_example_log_path()
        with open(log_path, 'r') as f:
            log_content = f.read()
        media_variant = completed_job['variants'][0]
        media_url_to_check = media_variant.get('image_url') or media_variant.get('video_url')
        assert media_url_to_check is not None, "Media URL not found in variant"
        expected_log_prefix = f"Pushed media: {media_url_to_check} to product {db_job.product_id} on {test_store.shop_domain}"
        print(f"\n--- Log Content ---\n{log_content}--- End Log Content ---\nExpected prefix: {expected_log_prefix}")
        assert any(expected_log_prefix in line for line in log_content.splitlines()), f"Log entry with prefix '{expected_log_prefix}' not found in log file."

    @pytest.mark.asyncio
    async def test_push_nonexistent_variant(
        self,
        e2e_client: AsyncClient,
        test_store: Store
    ):
        """Test push request with non-existent variant."""
        
        push_request = {
            "shop_id": test_store.id,
            "product_id": 123,
            "variant_id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",  # Non-existent variant (dummy UUID)
            "publish_targets": ["shopify"]
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        # Should return 404 or 400 depending on implementation
        assert response.status_code in [400, 404, 500]

    @pytest.mark.asyncio
    async def test_push_media_platform_failure(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test push workflow when platform upload fails."""
        
        # Generate media first
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job by external_id
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.external_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, str(db_job.external_id))
        variant_id = completed_job["variants"][0]["variant_id"]

        # Push with the example plugin, which will succeed and log
        push_request = {
            "shop_id": test_store.id,
            "product_id": int(db_job.product_id),
            "variant_id": variant_id,
            "publish_targets": ["example"] # Use the example plugin
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        
        # The example plugin always succeeds, so we expect a 200 and success=True
        assert response.status_code == 200
        push_data = response.json()
        assert push_data["success"] is True
        assert push_data["status"] == "completed"
        
        # Verify the example plugin logged the push
        log_path = self.get_example_log_path()
        with open(log_path, 'r') as f:
            log_content = f.read()
        media_variant = completed_job['variants'][0]
        media_url_to_check = media_variant.get('image_url') or media_variant.get('video_url')
        assert media_url_to_check is not None, "Media URL not found in variant"
        expected_log_prefix = f"Pushed media: {media_url_to_check} to product {db_job.product_id} on {test_store.shop_domain}"
        print(f"\n--- Log Content ---\n{log_content}--- End Log Content ---\nExpected prefix: {expected_log_prefix}")
        assert any(expected_log_prefix in line for line in log_content.splitlines()), f"Log entry with prefix '{expected_log_prefix}' not found in log file."
