# Media Generation E2E Testing Guide

This guide provides step-by-step instructions for running the comprehensive E2E tests for the Media Generation APIs.

## Quick Start

```bash
# Navigate to the E2E test directory
cd backend/tests/e2e/media

# Run all tests
make test

# Or run with the Python script directly
python run_e2e_tests.py
```

## Prerequisites Checklist

### ✅ Required Services

1. **Redis Server**
   ```bash
   # Check if Red<PERSON> is running
   redis-cli ping
   # Should return: PONG
   
   # If not running, start Redis
   redis-server
   ```

2. **PostgreSQL Database**
   ```bash
   # Verify database connection
   echo $DATABASE_URL
   # Should show: postgresql+asyncpg://user:pass@host/db
   
   # Test connection
   psql $DATABASE_URL -c "SELECT 1;"
   ```

3. **Python Environment**
   ```bash
   # Install dependencies
   cd backend
   uv pip install -e ".[all]"
   
   # Verify installation
   python -c "import pytest, httpx, sqlalchemy; print('✓ All packages available')"
   ```

### ✅ Environment Variables

```bash
export TESTING=True
export REDIS_URL=redis://localhost:6379/0
export DATABASE_URL=postgresql+asyncpg://user:pass@localhost/test_db
export LOG_LEVEL=INFO  # Optional: DEBUG for verbose logging
```

## Test Execution Options

### 1. Using Makefile (Recommended)

```bash
# Run all tests
make test

# Run with verbose output
make test-verbose

# Run specific test categories
make test-generation    # Generation workflow tests
make test-push         # Media push tests  
make test-jobs         # Job management tests
make test-errors       # Error handling tests

# Development helpers
make quick-test        # Single test for development
make test-coverage     # Run with coverage report
make test-debug        # Run with debugger
```

### 2. Using Python Script

```bash
# Run all tests
python run_e2e_tests.py

# Run specific test file
python run_e2e_tests.py --pattern "test_media_generation_e2e.py"

# Run with verbose output
python run_e2e_tests.py --verbose
```

### 3. Using pytest Directly

```bash
# From backend directory
cd backend

# Run all E2E tests
uv run python -m pytest tests/e2e/media/ -v

# Run specific test
uv run python -m pytest tests/e2e/media/test_media_generation_e2e.py::TestMediaGenerationE2E::test_generate_image_e2e_workflow -v

# Run with coverage
uv run python -m pytest tests/e2e/media/ --cov=modules.media --cov-report=html
```

## Test Categories Explained

### 🎯 Generation Workflow Tests (`test_media_generation_e2e.py`)
Tests the core media generation pipeline:
- Image generation (Banana provider)
- Video generation (Veo3 provider)  
- Text generation (Gemini provider)
- Job status tracking
- Multiple product processing
- Job cancellation

**Key Test**: `test_generate_image_e2e_workflow`
- Submits generation request
- Verifies job creation in database
- Waits for worker to process job
- Checks final status and variants

### 📤 Media Push Tests (`test_media_push_e2e.py`)
Tests publishing generated media to platforms:
- Push to Shopify
- Multiple platform publishing
- Custom publishing options
- Authorization validation
- Platform failure handling

**Key Test**: `test_push_media_to_shopify_e2e`
- Generates media first
- Pushes to mocked Shopify API
- Verifies platform integration

### 📋 Job Management Tests (`test_job_management_e2e.py`)
Tests job listing and management APIs:
- Job listing with pagination
- Status and product filtering
- Detailed status checking
- Job retry functionality
- Progress tracking
- Concurrent processing

**Key Test**: `test_list_jobs_e2e`
- Creates multiple jobs
- Tests pagination and filtering
- Verifies job visibility

### ⚠️ Error Handling Tests (`test_error_handling_e2e.py`)
Tests error scenarios and edge cases:
- Invalid request formats
- Unauthorized access
- Provider failures
- Database issues
- Network timeouts

**Key Test**: `test_provider_failure_handling`
- Mocks provider to fail
- Verifies graceful error handling

### 🔄 Complete Workflow Tests (`test_complete_workflow_e2e.py`)
Tests end-to-end scenarios:
- Full generation → push workflow
- Mixed media types for same product
- Error recovery
- Concurrent processing

**Key Test**: `test_complete_media_generation_and_push_workflow`
- Complete workflow from request to platform push
- Multiple products and media types
- Comprehensive verification

## Understanding Test Output

### ✅ Successful Test Run
```
✓ Environment configured
✓ Redis is available
✓ Celery worker started
Running E2E tests...
test_generate_image_e2e_workflow PASSED
test_generate_video_e2e_workflow PASSED
...
✓ All tests passed!
✓ Process terminated gracefully
```

### ❌ Common Failure Scenarios

1. **Worker Failed to Start**
   ```
   ✗ Worker failed to start
   STDERR: ModuleNotFoundError: No module named 'servers.worker.celery_app'
   ```
   **Solution**: Check PYTHONPATH and ensure you're in the correct directory

2. **Redis Not Available**
   ```
   ✗ Redis not available: ConnectionError
   ```
   **Solution**: Start Redis server: `redis-server`

3. **Database Connection Error**
   ```
   ✗ Database configuration error: could not connect to server
   ```
   **Solution**: Check DATABASE_URL and ensure PostgreSQL is running

4. **Test Timeout**
   ```
   ✗ Job did not complete within 60 seconds
   ```
   **Solution**: Check worker logs, increase timeout, or verify mocks

## Debugging Tests

### 1. Enable Debug Logging
```bash
export LOG_LEVEL=DEBUG
python run_e2e_tests.py --verbose
```

### 2. Run Single Test with Debugger
```bash
make test-debug
# Or
uv run python -m pytest tests/e2e/media/test_media_generation_e2e.py::TestMediaGenerationE2E::test_generate_image_e2e_workflow -v --pdb
```

### 3. Check Worker Status
```bash
# In another terminal, check if worker is processing
cd backend/src
uv run python -m celery inspect active -A servers.worker.celery_app:celery_app
```

### 4. Monitor Redis Queue
```bash
# Check Redis for queued tasks
redis-cli
> KEYS *
> LLEN celery
```

### 5. Database Inspection
```bash
# Connect to test database
psql $DATABASE_URL

# Check job status
SELECT id, status, media_type, created_at FROM media_jobs ORDER BY created_at DESC LIMIT 5;

# Check variants
SELECT job_id, status, image_url, video_url FROM media_variants WHERE job_id = <job_id>;
```

## Performance Expectations

### Typical Execution Times
- **Single test**: 30-60 seconds
- **Full test suite**: 5-10 minutes
- **Worker startup**: ~5 seconds
- **Job completion**: 2-10 seconds (mocked)

### Resource Usage
- **Memory**: ~500MB (including worker)
- **CPU**: Moderate (2 worker processes)
- **Disk**: Minimal (test database)
- **Network**: None (all external calls mocked)

## Troubleshooting Common Issues

### Issue: "Permission denied" when running scripts
```bash
chmod +x run_e2e_tests.py
```

### Issue: "Port already in use" 
```bash
# Find and kill processes using the port
lsof -ti:6379 | xargs kill -9  # For Redis
```

### Issue: Tests pass but no worker activity
- Check that `mock_providers` fixture is working
- Verify Celery configuration
- Check worker logs for errors

### Issue: Database permission errors
- Ensure test database exists and user has permissions
- Check DATABASE_URL format
- Verify PostgreSQL is running

## CI/CD Integration

### GitHub Actions Example
```yaml
name: E2E Tests
on: [push, pull_request]
jobs:
  e2e:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      - name: Install dependencies
        run: |
          pip install uv
          uv pip install -e ".[all]"
      - name: Run E2E tests
        env:
          DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost/postgres
          REDIS_URL: redis://localhost:6379/0
        run: |
          cd backend/tests/e2e/media
          python run_e2e_tests.py
```

## Next Steps

After running the E2E tests successfully:

1. **Review test coverage** - Check which scenarios are covered
2. **Add custom tests** - Create tests for your specific use cases  
3. **Performance testing** - Add load tests for high-volume scenarios
4. **Integration testing** - Test with real external services (staging)
5. **Monitoring setup** - Add observability for production systems

## Getting Help

If you encounter issues:

1. Check this guide for common solutions
2. Review test logs for detailed error messages
3. Verify all prerequisites are met
4. Check the main README for system requirements
5. Look at individual test files for specific test logic

The E2E tests are designed to be comprehensive and reliable. They should give you confidence that the entire media generation system works correctly from API to database to worker processing.
