"""init migration

Revision ID: 9a33775c6e05
Revises: 
Create Date: 2025-09-17 13:46:52.888174

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '9a33775c6e05'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assets',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('uuid_external_id', sa.UUID(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('product_external_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('src', sa.String(), nullable=False),
    sa.Column('alt', sa.String(), nullable=True),
    sa.Column('external_id', sa.String(), nullable=True),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('source_type', sa.String(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('src')
    )
    op.create_index(op.f('ix_assets_id'), 'assets', ['id'], unique=False)
    op.create_index(op.f('ix_assets_uuid_external_id'), 'assets', ['uuid_external_id'], unique=True)
    op.create_table('email_verifications',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_verifications_email'), 'email_verifications', ['email'], unique=False)
    op.create_index(op.f('ix_email_verifications_external_id'), 'email_verifications', ['external_id'], unique=True)
    op.create_index(op.f('ix_email_verifications_id'), 'email_verifications', ['id'], unique=False)
    op.create_index(op.f('ix_email_verifications_token'), 'email_verifications', ['token'], unique=True)
    op.create_table('holidays',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('holiday_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('holiday_date')
    )
    op.create_index(op.f('ix_holidays_id'), 'holidays', ['id'], unique=False)
    op.create_table('inventory_levels',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('platform_inventory_id', sa.String(), nullable=False),
    sa.Column('inventory_item_id', sa.String(), nullable=False),
    sa.Column('location_id', sa.String(), nullable=False),
    sa.Column('available', sa.Integer(), nullable=True),
    sa.Column('updated_at_location', sa.DateTime(timezone=True), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('platform_inventory_id')
    )
    op.create_index(op.f('ix_inventory_levels_external_id'), 'inventory_levels', ['external_id'], unique=True)
    op.create_index(op.f('ix_inventory_levels_id'), 'inventory_levels', ['id'], unique=False)
    op.create_table('scraping_platforms',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('domains', sa.JSON(), nullable=False),
    sa.Column('features', sa.JSON(), nullable=False),
    sa.Column('limitations', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_scraping_platforms_external_id'), 'scraping_platforms', ['external_id'], unique=True)
    op.create_index(op.f('ix_scraping_platforms_id'), 'scraping_platforms', ['id'], unique=False)
    op.create_table('templates',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('provider', sa.String(), nullable=False),
    sa.Column('template_config', sa.JSON(), nullable=False),
    sa.Column('preview_url', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('plan_tier_required', sa.Enum('FREE', 'STARTER', 'GROWTH', 'PRO', 'ENTERPRISE', name='plantier'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_templates_external_id'), 'templates', ['external_id'], unique=True)
    op.create_index(op.f('ix_templates_id'), 'templates', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=100), nullable=True),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('password_hash', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_login_at', sa.DateTime(), nullable=True),
    sa.Column('email_verification_token', sa.String(length=255), nullable=True),
    sa.Column('email_verification_expires', sa.DateTime(), nullable=True),
    sa.Column('password_reset_token', sa.String(length=255), nullable=True),
    sa.Column('password_reset_expires', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_uuid'), 'users', ['uuid'], unique=True)
    op.create_table('generated_assets',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('product_id', sa.BigInteger(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('file_uri', sa.String(), nullable=False),
    sa.Column('preview_uri', sa.String(), nullable=True),
    sa.Column('prompt', sa.Text(), nullable=True),
    sa.Column('model', sa.String(), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generated_assets_external_id'), 'generated_assets', ['external_id'], unique=True)
    op.create_index(op.f('ix_generated_assets_id'), 'generated_assets', ['id'], unique=False)
    op.create_table('generation_batches',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('mode', sa.String(), nullable=False),
    sa.Column('aspect_ratio', sa.String(), nullable=True),
    sa.Column('quality', sa.String(), nullable=True),
    sa.Column('model', sa.String(), nullable=False),
    sa.Column('requested_count', sa.Integer(), nullable=True),
    sa.Column('completed_count', sa.Integer(), nullable=True),
    sa.Column('failed_count', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generation_batches_external_id'), 'generation_batches', ['external_id'], unique=True)
    op.create_index(op.f('ix_generation_batches_id'), 'generation_batches', ['id'], unique=False)
    op.create_table('media_jobs',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('product_id', sa.BigInteger(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', name='mediajobstatus'), nullable=False),
    sa.Column('media_type', sa.String(), nullable=False),
    sa.Column('provider', sa.String(), nullable=False),
    sa.Column('resolved_provider', sa.String(), nullable=True),
    sa.Column('template_id', sa.String(), nullable=True),
    sa.Column('custom_config', sa.JSON(), nullable=True),
    sa.Column('full_payload', sa.JSON(), nullable=True),
    sa.Column('mode', sa.String(), nullable=True),
    sa.Column('model', sa.String(), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('items', sa.JSON(), nullable=True),
    sa.Column('shop_id', sa.BigInteger(), nullable=True),
    sa.Column('product_ids', sa.JSON(), nullable=True),
    sa.Column('celery_task_id', sa.String(), nullable=True),
    sa.Column('progress_percentage', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('idempotency_key', sa.String(length=32), nullable=True),
    sa.Column('product_version', sa.String(length=16), nullable=True),
    sa.Column('needs_manual_review', sa.Boolean(), nullable=True),
    sa.Column('qa_metadata', sa.JSON(), nullable=True),
    sa.Column('language', sa.String(length=10), nullable=True),
    sa.Column('fallback_language', sa.String(length=10), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_jobs_external_id'), 'media_jobs', ['external_id'], unique=True)
    op.create_index(op.f('ix_media_jobs_id'), 'media_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_media_jobs_idempotency_key'), 'media_jobs', ['idempotency_key'], unique=False)
    op.create_table('oauth_accounts',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('provider', sa.String(length=50), nullable=False),
    sa.Column('provider_user_id', sa.String(length=255), nullable=False),
    sa.Column('provider_username', sa.String(length=255), nullable=True),
    sa.Column('provider_email', sa.String(length=255), nullable=True),
    sa.Column('access_token', sa.Text(), nullable=True),
    sa.Column('refresh_token', sa.Text(), nullable=True),
    sa.Column('token_expires_at', sa.DateTime(), nullable=True),
    sa.Column('provider_data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('provider', 'provider_user_id', name='unique_provider_user')
    )
    op.create_index(op.f('ix_oauth_accounts_external_id'), 'oauth_accounts', ['external_id'], unique=True)
    op.create_index(op.f('ix_oauth_accounts_id'), 'oauth_accounts', ['id'], unique=False)
    op.create_table('password_resets',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_password_resets_external_id'), 'password_resets', ['external_id'], unique=True)
    op.create_index(op.f('ix_password_resets_id'), 'password_resets', ['id'], unique=False)
    op.create_index(op.f('ix_password_resets_token'), 'password_resets', ['token'], unique=True)
    op.create_table('tenants',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('plan_tier', sa.String(length=50), nullable=True),
    sa.Column('storage_limit_gb', sa.Float(), nullable=True),
    sa.Column('storage_used_gb', sa.Float(), nullable=True),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=True),
    sa.Column('trial_ends_at', sa.DateTime(), nullable=True),
    sa.Column('billing_email', sa.String(length=255), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('credits', sa.Float(), nullable=True),
    sa.Column('credit_expires_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenants_id'), 'tenants', ['id'], unique=False)
    op.create_index(op.f('ix_tenants_slug'), 'tenants', ['slug'], unique=True)
    op.create_index(op.f('ix_tenants_uuid'), 'tenants', ['uuid'], unique=True)
    op.create_table('user_sessions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('refresh_token', sa.String(length=255), nullable=False),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('device_name', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_sessions_external_id'), 'user_sessions', ['external_id'], unique=True)
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_user_sessions_refresh_token'), 'user_sessions', ['refresh_token'], unique=True)
    op.create_index(op.f('ix_user_sessions_session_token'), 'user_sessions', ['session_token'], unique=True)
    op.create_table('billing_usage',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('usage_type', sa.Enum('VIDEO_GENERATION', 'STORAGE_GB', 'BANDWIDTH_GB', 'API_CALLS', name='usagetype'), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit_price', sa.DECIMAL(precision=10, scale=4), nullable=True),
    sa.Column('total_cost', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('billing_period_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('billing_period_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('stripe_usage_record_id', sa.String(length=255), nullable=True),
    sa.Column('stripe_subscription_item_id', sa.String(length=255), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('usage_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_billing_usage_external_id'), 'billing_usage', ['external_id'], unique=True)
    op.create_index(op.f('ix_billing_usage_id'), 'billing_usage', ['id'], unique=False)
    op.create_index(op.f('ix_billing_usage_stripe_usage_record_id'), 'billing_usage', ['stripe_usage_record_id'], unique=True)
    op.create_table('credit_transactions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('transaction_type', sa.Enum('SUBSCRIPTION_GRANT', 'PURCHASE', 'USAGE', 'EXPIRATION', 'ADJUSTMENT', name='credittransactiontype'), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('balance_after', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('description', sa.String(length=500), nullable=True),
    sa.Column('stripe_payment_id', sa.String(length=255), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_credit_transactions_external_id'), 'credit_transactions', ['external_id'], unique=True)
    op.create_index(op.f('ix_credit_transactions_id'), 'credit_transactions', ['id'], unique=False)
    op.create_table('generation_requests',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('batch_id', sa.BigInteger(), nullable=False),
    sa.Column('product_id', sa.BigInteger(), nullable=False),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('prompt', sa.Text(), nullable=False),
    sa.Column('params_json', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('result_url', sa.String(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['batch_id'], ['generation_batches.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generation_requests_id'), 'generation_requests', ['id'], unique=False)
    op.create_table('invoices',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('stripe_invoice_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=False),
    sa.Column('amount_due', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('amount_paid', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('invoice_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('paid_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('invoice_pdf_url', sa.String(length=500), nullable=True),
    sa.Column('hosted_invoice_url', sa.String(length=500), nullable=True),
    sa.Column('invoice_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_invoices_external_id'), 'invoices', ['external_id'], unique=True)
    op.create_index(op.f('ix_invoices_id'), 'invoices', ['id'], unique=False)
    op.create_index(op.f('ix_invoices_stripe_invoice_id'), 'invoices', ['stripe_invoice_id'], unique=True)
    op.create_table('media_variants',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('variant_name', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('GENERATING', 'COMPLETED', 'FAILED', 'PROCESSING', 'CANCELLED', name='mediavariantstatus'), nullable=False),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('resolution', sa.String(), nullable=True),
    sa.Column('file_size_bytes', sa.Integer(), nullable=True),
    sa.Column('provider', sa.String(), nullable=True),
    sa.Column('video_url', sa.String(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('thumbnail_url', sa.String(), nullable=True),
    sa.Column('provider_media_id', sa.String(), nullable=True),
    sa.Column('provider_metadata', sa.JSON(), nullable=True),
    sa.Column('is_favorite', sa.Boolean(), nullable=True),
    sa.Column('user_rating', sa.Integer(), nullable=True),
    sa.Column('push_status', sa.Enum('pending', 'pushing', 'completed', 'failed', name='pushstatus'), nullable=False),
    sa.Column('media_id', sa.String(), nullable=True),
    sa.Column('pushed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('push_error_message', sa.Text(), nullable=True),
    sa.Column('alt_text', sa.Text(), nullable=True),
    sa.Column('captions', sa.Text(), nullable=True),
    sa.Column('text_content', sa.Text(), nullable=True),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('needs_manual_review', sa.Boolean(), nullable=True),
    sa.Column('qa_metadata', sa.JSON(), nullable=True),
    sa.Column('brand_safety_checked', sa.Boolean(), nullable=True),
    sa.Column('copyright_validated', sa.Boolean(), nullable=True),
    sa.Column('content_flags', sa.JSON(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['media_jobs.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_variants_external_id'), 'media_variants', ['external_id'], unique=True)
    op.create_index(op.f('ix_media_variants_id'), 'media_variants', ['id'], unique=False)
    op.create_table('payment_methods',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('stripe_payment_method_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('card_brand', sa.String(length=50), nullable=True),
    sa.Column('card_last4', sa.String(length=4), nullable=True),
    sa.Column('card_exp_month', sa.Integer(), nullable=True),
    sa.Column('card_exp_year', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('payment_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_methods_external_id'), 'payment_methods', ['external_id'], unique=True)
    op.create_index(op.f('ix_payment_methods_id'), 'payment_methods', ['id'], unique=False)
    op.create_index(op.f('ix_payment_methods_stripe_payment_method_id'), 'payment_methods', ['stripe_payment_method_id'], unique=True)
    op.create_table('scraped_documents',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('workspace_id', sa.BigInteger(), nullable=False),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'SCRAPING', 'COMPLETED', 'FAILED', name='scrapingstatus'), nullable=False),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('product_count', sa.Integer(), nullable=True),
    sa.Column('collection_count', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('scraping_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraped_documents_domain'), 'scraped_documents', ['domain'], unique=False)
    op.create_index(op.f('ix_scraped_documents_external_id'), 'scraped_documents', ['external_id'], unique=True)
    op.create_index(op.f('ix_scraped_documents_id'), 'scraped_documents', ['id'], unique=False)
    op.create_table('stores',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('platform', sa.String(), nullable=False),
    sa.Column('api_key', sa.String(), nullable=True),
    sa.Column('api_secret_key', sa.String(), nullable=True),
    sa.Column('admin_access_token', sa.String(), nullable=True),
    sa.Column('shop_domain', sa.String(), nullable=True),
    sa.Column('shop_id', sa.String(), nullable=True),
    sa.Column('shop_name', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_source_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_destination_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_connection_id', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('owner_id', sa.BigInteger(), nullable=True),
    sa.Column('tenant_id', sa.BigInteger(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stores_airbyte_connection_id'), 'stores', ['airbyte_connection_id'], unique=False)
    op.create_index(op.f('ix_stores_airbyte_source_id'), 'stores', ['airbyte_source_id'], unique=False)
    op.create_index(op.f('ix_stores_external_id'), 'stores', ['external_id'], unique=True)
    op.create_index(op.f('ix_stores_id'), 'stores', ['id'], unique=False)
    op.create_table('subscriptions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('stripe_subscription_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_price_id', sa.String(length=255), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'PAST_DUE', 'CANCELED', 'UNPAID', 'INCOMPLETE', 'INCOMPLETE_EXPIRED', 'TRIALING', name='subscriptionstatus'), nullable=False),
    sa.Column('current_period_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('current_period_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('trial_start', sa.DateTime(timezone=True), nullable=True),
    sa.Column('trial_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('subscription_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscriptions_external_id'), 'subscriptions', ['external_id'], unique=True)
    op.create_index(op.f('ix_subscriptions_id'), 'subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_subscriptions_stripe_subscription_id'), 'subscriptions', ['stripe_subscription_id'], unique=True)
    op.create_table('ab_test_experiments',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('product_ids', sa.JSON(), nullable=False),
    sa.Column('control_variant_id', sa.Integer(), nullable=False),
    sa.Column('test_variant_ids', sa.JSON(), nullable=False),
    sa.Column('traffic_allocation', sa.Float(), nullable=False),
    sa.Column('control_split', sa.Float(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('start_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('statistical_significance', sa.Float(), nullable=True),
    sa.Column('confidence_level', sa.Float(), nullable=False),
    sa.Column('winner_variant_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['control_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['winner_variant_id'], ['media_variants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ab_test_experiments_external_id'), 'ab_test_experiments', ['external_id'], unique=True)
    op.create_index(op.f('ix_ab_test_experiments_id'), 'ab_test_experiments', ['id'], unique=False)
    op.create_table('analytics_events',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('event_id', sa.String(length=255), nullable=False),
    sa.Column('event_type', sa.String(length=50), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.String(length=255), nullable=True),
    sa.Column('media_variant_id', sa.Integer(), nullable=True),
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('duration', sa.Float(), nullable=True),
    sa.Column('position', sa.Float(), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('country', sa.String(length=2), nullable=True),
    sa.Column('device_type', sa.String(length=50), nullable=True),
    sa.Column('referrer', sa.Text(), nullable=True),
    sa.Column('utm_source', sa.String(length=255), nullable=True),
    sa.Column('utm_medium', sa.String(length=255), nullable=True),
    sa.Column('utm_campaign', sa.String(length=255), nullable=True),
    sa.Column('utm_content', sa.String(length=255), nullable=True),
    sa.Column('utm_term', sa.String(length=255), nullable=True),
    sa.Column('order_id', sa.String(length=255), nullable=True),
    sa.Column('order_value', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('properties', sa.JSON(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=False),
    sa.Column('is_conversion', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_analytics_events_conversion', 'analytics_events', ['is_conversion', 'timestamp'], unique=False)
    op.create_index('idx_analytics_events_product_timestamp', 'analytics_events', ['product_id', 'timestamp'], unique=False)
    op.create_index('idx_analytics_events_session_timestamp', 'analytics_events', ['session_id', 'timestamp'], unique=False)
    op.create_index('idx_analytics_events_tenant_timestamp', 'analytics_events', ['tenant_id', 'timestamp'], unique=False)
    op.create_index(op.f('ix_analytics_events_event_id'), 'analytics_events', ['event_id'], unique=True)
    op.create_index(op.f('ix_analytics_events_external_id'), 'analytics_events', ['external_id'], unique=True)
    op.create_index(op.f('ix_analytics_events_id'), 'analytics_events', ['id'], unique=False)
    op.create_index(op.f('ix_analytics_events_order_id'), 'analytics_events', ['order_id'], unique=False)
    op.create_index(op.f('ix_analytics_events_product_id'), 'analytics_events', ['product_id'], unique=False)
    op.create_index(op.f('ix_analytics_events_session_id'), 'analytics_events', ['session_id'], unique=False)
    op.create_index(op.f('ix_analytics_events_timestamp'), 'analytics_events', ['timestamp'], unique=False)
    op.create_index(op.f('ix_analytics_events_user_id'), 'analytics_events', ['user_id'], unique=False)
    op.create_table('customers',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('shopify_customer_id', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('accepts_marketing', sa.Boolean(), nullable=True),
    sa.Column('tax_exempt', sa.Boolean(), nullable=True),
    sa.Column('verified_email', sa.Boolean(), nullable=True),
    sa.Column('address1', sa.String(), nullable=True),
    sa.Column('address2', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('province', sa.String(), nullable=True),
    sa.Column('province_code', sa.String(), nullable=True),
    sa.Column('zip', sa.String(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('store_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tags', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('total_spent', sa.Float(), nullable=True),
    sa.Column('orders_count', sa.Integer(), nullable=True),
    sa.Column('state', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('shopify_customer_id')
    )
    op.create_index(op.f('ix_customers_external_id'), 'customers', ['external_id'], unique=True)
    op.create_index(op.f('ix_customers_id'), 'customers', ['id'], unique=False)
    op.create_table('dead_letter_queue',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('source_type', sa.String(length=50), nullable=False),
    sa.Column('source_id', sa.String(length=255), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('original_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('failure_reason', sa.String(length=100), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=False),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('last_retry_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolved', sa.Boolean(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_dlq_created_at', 'dead_letter_queue', ['created_at'], unique=False)
    op.create_index('idx_dlq_store_id', 'dead_letter_queue', ['store_id'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_external_id'), 'dead_letter_queue', ['external_id'], unique=True)
    op.create_index(op.f('ix_dead_letter_queue_id'), 'dead_letter_queue', ['id'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_resolved'), 'dead_letter_queue', ['resolved'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_source_type'), 'dead_letter_queue', ['source_type'], unique=False)
    op.create_table('forecasts',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('store_id', sa.BigInteger(), nullable=True),
    sa.Column('forecast_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('predicted_sales', sa.Float(), nullable=False),
    sa.Column('confidence_lower', sa.Float(), nullable=True),
    sa.Column('confidence_upper', sa.Float(), nullable=True),
    sa.Column('forecast_period', sa.String(), nullable=True),
    sa.Column('raw_forecast_data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_forecasts_id'), 'forecasts', ['id'], unique=False)
    op.create_table('media_analytics',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('media_variant_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('views', sa.Integer(), nullable=False),
    sa.Column('unique_views', sa.Integer(), nullable=False),
    sa.Column('plays', sa.Integer(), nullable=False),
    sa.Column('completions', sa.Integer(), nullable=False),
    sa.Column('total_watch_time', sa.Float(), nullable=False),
    sa.Column('average_watch_time', sa.Float(), nullable=False),
    sa.Column('completion_rate', sa.Float(), nullable=False),
    sa.Column('cta_clicks', sa.Integer(), nullable=False),
    sa.Column('cta_click_rate', sa.Float(), nullable=False),
    sa.Column('add_to_carts', sa.Integer(), nullable=False),
    sa.Column('purchases', sa.Integer(), nullable=False),
    sa.Column('conversion_rate', sa.Float(), nullable=False),
    sa.Column('total_revenue', sa.Float(), nullable=False),
    sa.Column('mobile_views', sa.Integer(), nullable=False),
    sa.Column('desktop_views', sa.Integer(), nullable=False),
    sa.Column('tablet_views', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_media_analytics_product_date', 'media_analytics', ['product_id', 'date'], unique=False)
    op.create_index('idx_media_analytics_tenant_date', 'media_analytics', ['tenant_id', 'date'], unique=False)
    op.create_index('idx_media_analytics_variant_date', 'media_analytics', ['media_variant_id', 'date'], unique=False)
    op.create_index(op.f('ix_media_analytics_date'), 'media_analytics', ['date'], unique=False)
    op.create_index(op.f('ix_media_analytics_external_id'), 'media_analytics', ['external_id'], unique=True)
    op.create_index(op.f('ix_media_analytics_id'), 'media_analytics', ['id'], unique=False)
    op.create_index(op.f('ix_media_analytics_product_id'), 'media_analytics', ['product_id'], unique=False)
    op.create_table('media_reviews',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('variant_id', sa.BigInteger(), nullable=False),
    sa.Column('reviewer_id', sa.BigInteger(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.Column('rejection_reasons', sa.JSON(), nullable=True),
    sa.Column('suggested_improvements', sa.Text(), nullable=True),
    sa.Column('review_criteria', sa.JSON(), nullable=True),
    sa.Column('review_time_seconds', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['reviewer_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['variant_id'], ['media_variants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_reviews_id'), 'media_reviews', ['id'], unique=False)
    op.create_table('product_performance',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('store_id', sa.BigInteger(), nullable=False),
    sa.Column('total_sold', sa.Integer(), nullable=True),
    sa.Column('total_revenue', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('product_id')
    )
    op.create_index(op.f('ix_product_performance_id'), 'product_performance', ['id'], unique=False)
    op.create_table('products',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('platform_product_id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('handle', sa.String(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=True),
    sa.Column('product_type', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('published', sa.Boolean(), nullable=True),
    sa.Column('store_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('options', sa.Text(), nullable=True),
    sa.Column('seo', sa.Text(), nullable=True),
    sa.Column('metafields', sa.Text(), nullable=True),
    sa.Column('collections', sa.Text(), nullable=True),
    sa.Column('featured_media', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('platform_product_id')
    )
    op.create_index(op.f('ix_products_external_id'), 'products', ['external_id'], unique=True)
    op.create_index(op.f('ix_products_id'), 'products', ['id'], unique=False)
    op.create_table('rejected_assets',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('original_variant_id', sa.BigInteger(), nullable=False),
    sa.Column('job_id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('rejection_reason', sa.String(), nullable=False),
    sa.Column('rejection_category', sa.String(), nullable=True),
    sa.Column('rejection_details', sa.JSON(), nullable=True),
    sa.Column('original_media_url', sa.String(), nullable=True),
    sa.Column('original_prompt', sa.Text(), nullable=True),
    sa.Column('original_settings', sa.JSON(), nullable=True),
    sa.Column('regeneration_attempts', sa.Integer(), nullable=True),
    sa.Column('last_regeneration_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('regenerated_variant_id', sa.BigInteger(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['media_jobs.id'], ),
    sa.ForeignKeyConstraint(['original_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['regenerated_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rejected_assets_id'), 'rejected_assets', ['id'], unique=False)
    op.create_table('scraped_collections',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.BigInteger(), nullable=False),
    sa.Column('slug', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('product_count', sa.Integer(), nullable=True),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('scraped_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['scraped_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraped_collections_domain'), 'scraped_collections', ['domain'], unique=False)
    op.create_index(op.f('ix_scraped_collections_external_id'), 'scraped_collections', ['external_id'], unique=True)
    op.create_index(op.f('ix_scraped_collections_id'), 'scraped_collections', ['id'], unique=False)
    op.create_table('scraped_products',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.BigInteger(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('handle', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=True),
    sa.Column('product_type', sa.String(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('images', sa.JSON(), nullable=True),
    sa.Column('variants', sa.JSON(), nullable=True),
    sa.Column('collections', sa.JSON(), nullable=True),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('scraped_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['scraped_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraped_products_domain'), 'scraped_products', ['domain'], unique=False)
    op.create_index(op.f('ix_scraped_products_external_id'), 'scraped_products', ['external_id'], unique=True)
    op.create_index(op.f('ix_scraped_products_id'), 'scraped_products', ['id'], unique=False)
    op.create_table('scraping_jobs',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.BigInteger(), nullable=False),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('QUEUED', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='jobstatus'), nullable=False),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('job_metadata', sa.JSON(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['scraped_documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraping_jobs_external_id'), 'scraping_jobs', ['external_id'], unique=True)
    op.create_index(op.f('ix_scraping_jobs_id'), 'scraping_jobs', ['id'], unique=False)
    op.create_table('store_analytics_snapshots',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('store_id', sa.BigInteger(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('total_sales', sa.Float(), nullable=True),
    sa.Column('total_orders', sa.Integer(), nullable=True),
    sa.Column('average_order_value', sa.Float(), nullable=True),
    sa.Column('new_customers', sa.Integer(), nullable=True),
    sa.Column('conversion_rate', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('date')
    )
    op.create_index(op.f('ix_store_analytics_snapshots_id'), 'store_analytics_snapshots', ['id'], unique=False)
    op.create_table('store_sales',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('store_id', sa.BigInteger(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=True),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('sale_date', sa.Date(), nullable=False),
    sa.Column('quantity_sold', sa.Integer(), nullable=False),
    sa.Column('revenue', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_store_sales_id'), 'store_sales', ['id'], unique=False)
    op.create_table('sync_checkpoints',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_records', sa.Integer(), nullable=True),
    sa.Column('last_sync_status', sa.String(length=20), nullable=True),
    sa.Column('last_error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_successful_sync_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_last_sync_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_sync_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_sync_finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('local_sync_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('local_sync_finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sync_duration_seconds', sa.Float(), nullable=True),
    sa.Column('airbyte_job_id', sa.String(length=255), nullable=True),
    sa.Column('sync_trigger_type', sa.String(length=20), nullable=True),
    sa.Column('current_sync_stage', sa.String(length=30), nullable=True),
    sa.Column('records_processed_in_sync', sa.Integer(), nullable=True),
    sa.Column('source_table_name', sa.String(length=100), nullable=True),
    sa.Column('source_database_name', sa.String(length=100), nullable=True),
    sa.Column('source_record_count_before', sa.Integer(), nullable=True),
    sa.Column('source_record_count_after', sa.Integer(), nullable=True),
    sa.Column('source_new_records_count', sa.Integer(), nullable=True),
    sa.Column('source_updated_records_count', sa.Integer(), nullable=True),
    sa.Column('source_deleted_records_count', sa.Integer(), nullable=True),
    sa.Column('source_last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_filter_criteria', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('destination_table_name', sa.String(length=100), nullable=True),
    sa.Column('destination_database_name', sa.String(length=100), nullable=True),
    sa.Column('destination_record_count_before', sa.Integer(), nullable=True),
    sa.Column('destination_record_count_after', sa.Integer(), nullable=True),
    sa.Column('destination_inserted_count', sa.Integer(), nullable=True),
    sa.Column('destination_updated_count', sa.Integer(), nullable=True),
    sa.Column('destination_deleted_count', sa.Integer(), nullable=True),
    sa.Column('destination_failed_count', sa.Integer(), nullable=True),
    sa.Column('destination_last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sync_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sync_finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('batch_count', sa.Integer(), nullable=True),
    sa.Column('average_batch_size', sa.Float(), nullable=True),
    sa.Column('max_batch_size', sa.Integer(), nullable=True),
    sa.Column('min_batch_size', sa.Integer(), nullable=True),
    sa.Column('total_batches_processed', sa.Integer(), nullable=True),
    sa.Column('batches_with_errors', sa.Integer(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('records_per_second', sa.Float(), nullable=True),
    sa.Column('average_batch_processing_time', sa.Float(), nullable=True),
    sa.Column('max_batch_processing_time', sa.Float(), nullable=True),
    sa.Column('min_batch_processing_time', sa.Float(), nullable=True),
    sa.Column('memory_usage_peak', sa.Float(), nullable=True),
    sa.Column('database_connection_count', sa.Integer(), nullable=True),
    sa.Column('error_count_total', sa.Integer(), nullable=True),
    sa.Column('error_count_by_type', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('last_error_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('consecutive_error_count', sa.Integer(), nullable=True),
    sa.Column('error_rate_percentage', sa.Float(), nullable=True),
    sa.Column('duplicate_records_found', sa.Integer(), nullable=True),
    sa.Column('invalid_records_skipped', sa.Integer(), nullable=True),
    sa.Column('null_values_count', sa.Integer(), nullable=True),
    sa.Column('data_transformation_errors', sa.Integer(), nullable=True),
    sa.Column('batch_statistics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('sync_version', sa.String(length=50), nullable=True),
    sa.Column('platform_version', sa.String(length=50), nullable=True),
    sa.Column('airbyte_version', sa.String(length=50), nullable=True),
    sa.Column('configuration_hash', sa.String(length=255), nullable=True),
    sa.Column('environment_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('store_id', 'entity_type', name='uq_sync_checkpoints_store_entity')
    )
    op.create_index('idx_sync_checkpoints_store_entity', 'sync_checkpoints', ['store_id', 'entity_type'], unique=False)
    op.create_index('idx_sync_checkpoints_updated_at', 'sync_checkpoints', ['last_updated_at'], unique=False)
    op.create_index(op.f('ix_sync_checkpoints_external_id'), 'sync_checkpoints', ['external_id'], unique=True)
    op.create_index(op.f('ix_sync_checkpoints_id'), 'sync_checkpoints', ['id'], unique=False)
    op.create_table('sync_jobs',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('job_type', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('triggered_by', sa.String(length=50), nullable=True),
    sa.Column('airbyte_job_id', sa.BigInteger(), nullable=True),
    sa.Column('airbyte_connection_id', sa.String(length=255), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('records_processed', sa.Integer(), nullable=True),
    sa.Column('records_failed', sa.Integer(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('celery_task_id', sa.String(length=255), nullable=True),
    sa.Column('job_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_sync_jobs_created_at', 'sync_jobs', ['created_at'], unique=False)
    op.create_index('idx_sync_jobs_store_entity', 'sync_jobs', ['store_id', 'entity_type'], unique=False)
    op.create_index(op.f('ix_sync_jobs_airbyte_job_id'), 'sync_jobs', ['airbyte_job_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_celery_task_id'), 'sync_jobs', ['celery_task_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_external_id'), 'sync_jobs', ['external_id'], unique=True)
    op.create_index(op.f('ix_sync_jobs_id'), 'sync_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_status'), 'sync_jobs', ['status'], unique=False)
    op.create_table('sync_progress',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('store_id', sa.BigInteger(), nullable=False),
    sa.Column('sync_type', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('total_items', sa.Integer(), nullable=True),
    sa.Column('processed_items', sa.Integer(), nullable=True),
    sa.Column('current_batch', sa.Integer(), nullable=True),
    sa.Column('total_batches', sa.Integer(), nullable=True),
    sa.Column('last_update', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sync_progress_external_id'), 'sync_progress', ['external_id'], unique=True)
    op.create_index(op.f('ix_sync_progress_id'), 'sync_progress', ['id'], unique=False)
    op.create_table('webhook_events',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('event_id', sa.String(length=255), nullable=False),
    sa.Column('topic', sa.String(length=100), nullable=False),
    sa.Column('shop_domain', sa.String(length=255), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.Integer(), nullable=True),
    sa.Column('event_type', sa.String(length=50), nullable=True),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('hmac_verified', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('last_error', sa.Text(), nullable=True),
    sa.Column('processing_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_webhook_events_created_at', 'webhook_events', ['created_at'], unique=False)
    op.create_index('idx_webhook_events_shop_topic', 'webhook_events', ['shop_domain', 'topic'], unique=False)
    op.create_index(op.f('ix_webhook_events_event_id'), 'webhook_events', ['event_id'], unique=True)
    op.create_index(op.f('ix_webhook_events_external_id'), 'webhook_events', ['external_id'], unique=True)
    op.create_index(op.f('ix_webhook_events_id'), 'webhook_events', ['id'], unique=False)
    op.create_index(op.f('ix_webhook_events_shop_domain'), 'webhook_events', ['shop_domain'], unique=False)
    op.create_index(op.f('ix_webhook_events_status'), 'webhook_events', ['status'], unique=False)
    op.create_index(op.f('ix_webhook_events_topic'), 'webhook_events', ['topic'], unique=False)
    op.create_table('conversion_funnels',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('user_id', sa.String(length=255), nullable=True),
    sa.Column('media_view_event_id', sa.String(length=255), nullable=True),
    sa.Column('media_play_event_id', sa.String(length=255), nullable=True),
    sa.Column('media_complete_event_id', sa.String(length=255), nullable=True),
    sa.Column('cta_click_event_id', sa.String(length=255), nullable=True),
    sa.Column('add_to_cart_event_id', sa.String(length=255), nullable=True),
    sa.Column('purchase_event_id', sa.String(length=255), nullable=True),
    sa.Column('first_media_variant_id', sa.Integer(), nullable=True),
    sa.Column('converting_media_variant_id', sa.Integer(), nullable=True),
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('time_to_conversion', sa.Integer(), nullable=True),
    sa.Column('conversion_value', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('funnel_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('funnel_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['add_to_cart_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['converting_media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['cta_click_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['first_media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['media_complete_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['media_play_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['media_view_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['purchase_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversion_funnels_external_id'), 'conversion_funnels', ['external_id'], unique=True)
    op.create_index(op.f('ix_conversion_funnels_id'), 'conversion_funnels', ['id'], unique=False)
    op.create_index(op.f('ix_conversion_funnels_product_id'), 'conversion_funnels', ['product_id'], unique=False)
    op.create_index(op.f('ix_conversion_funnels_session_id'), 'conversion_funnels', ['session_id'], unique=False)
    op.create_index(op.f('ix_conversion_funnels_user_id'), 'conversion_funnels', ['user_id'], unique=False)
    op.create_table('customer_addresses',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.BigInteger(), nullable=True),
    sa.Column('shopify_address_id', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('company', sa.String(), nullable=True),
    sa.Column('address1', sa.String(), nullable=True),
    sa.Column('address2', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('province', sa.String(), nullable=True),
    sa.Column('province_code', sa.String(), nullable=True),
    sa.Column('zip', sa.String(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_addresses_external_id'), 'customer_addresses', ['external_id'], unique=True)
    op.create_index(op.f('ix_customer_addresses_id'), 'customer_addresses', ['id'], unique=False)
    op.create_table('orders',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('platform_order_id', sa.String(), nullable=False),
    sa.Column('store_id', sa.BigInteger(), nullable=False),
    sa.Column('customer_id', sa.BigInteger(), nullable=True),
    sa.Column('order_number', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('subtotal_price', sa.Float(), nullable=True),
    sa.Column('total_tax', sa.Float(), nullable=True),
    sa.Column('total_discounts', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('financial_status', sa.String(), nullable=True),
    sa.Column('fulfillment_status', sa.String(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('platform_order_id')
    )
    op.create_index(op.f('ix_orders_external_id'), 'orders', ['external_id'], unique=True)
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_table('product_variants',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('platform_variant_id', sa.String(), nullable=False),
    sa.Column('product_id', sa.BigInteger(), nullable=False),
    sa.Column('product_external_id', sa.UUID(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('barcode', sa.String(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('compare_at_price', sa.Float(), nullable=True),
    sa.Column('cost', sa.Float(), nullable=True),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('weight_unit', sa.String(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('inventory_policy', sa.String(), nullable=True),
    sa.Column('inventory_item_id', sa.String(), nullable=True),
    sa.Column('option1', sa.String(), nullable=True),
    sa.Column('option2', sa.String(), nullable=True),
    sa.Column('option3', sa.String(), nullable=True),
    sa.Column('taxable', sa.Boolean(), nullable=True),
    sa.Column('requires_shipping', sa.Boolean(), nullable=True),
    sa.Column('fulfillment_service', sa.String(), nullable=True),
    sa.Column('available_for_sale', sa.Boolean(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('metafields', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('platform_variant_id')
    )
    op.create_index(op.f('ix_product_variants_external_id'), 'product_variants', ['external_id'], unique=True)
    op.create_index(op.f('ix_product_variants_id'), 'product_variants', ['id'], unique=False)
    op.create_index(op.f('ix_product_variants_product_external_id'), 'product_variants', ['product_external_id'], unique=False)
    op.create_table('order_line_items',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('order_id', sa.BigInteger(), nullable=False),
    sa.Column('platform_line_item_id', sa.String(), nullable=True),
    sa.Column('product_id', sa.String(), nullable=True),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_line_items_external_id'), 'order_line_items', ['external_id'], unique=True)
    op.create_index(op.f('ix_order_line_items_id'), 'order_line_items', ['id'], unique=False)
    op.create_table('product_images',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('platform_image_id', sa.String(), nullable=False),
    sa.Column('product_id', sa.BigInteger(), nullable=False),
    sa.Column('variant_id', sa.BigInteger(), nullable=True),
    sa.Column('src', sa.String(), nullable=False),
    sa.Column('alt', sa.String(), nullable=True),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('metafields', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['variant_id'], ['product_variants.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('platform_image_id')
    )
    op.create_index(op.f('ix_product_images_external_id'), 'product_images', ['external_id'], unique=True)
    op.create_index(op.f('ix_product_images_id'), 'product_images', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_product_images_id'), table_name='product_images')
    op.drop_index(op.f('ix_product_images_external_id'), table_name='product_images')
    op.drop_table('product_images')
    op.drop_index(op.f('ix_order_line_items_id'), table_name='order_line_items')
    op.drop_index(op.f('ix_order_line_items_external_id'), table_name='order_line_items')
    op.drop_table('order_line_items')
    op.drop_index(op.f('ix_product_variants_product_external_id'), table_name='product_variants')
    op.drop_index(op.f('ix_product_variants_id'), table_name='product_variants')
    op.drop_index(op.f('ix_product_variants_external_id'), table_name='product_variants')
    op.drop_table('product_variants')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_external_id'), table_name='orders')
    op.drop_table('orders')
    op.drop_index(op.f('ix_customer_addresses_id'), table_name='customer_addresses')
    op.drop_index(op.f('ix_customer_addresses_external_id'), table_name='customer_addresses')
    op.drop_table('customer_addresses')
    op.drop_index(op.f('ix_conversion_funnels_user_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_session_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_product_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_external_id'), table_name='conversion_funnels')
    op.drop_table('conversion_funnels')
    op.drop_index(op.f('ix_webhook_events_topic'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_status'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_shop_domain'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_external_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_event_id'), table_name='webhook_events')
    op.drop_index('idx_webhook_events_shop_topic', table_name='webhook_events')
    op.drop_index('idx_webhook_events_created_at', table_name='webhook_events')
    op.drop_table('webhook_events')
    op.drop_index(op.f('ix_sync_progress_id'), table_name='sync_progress')
    op.drop_index(op.f('ix_sync_progress_external_id'), table_name='sync_progress')
    op.drop_table('sync_progress')
    op.drop_index(op.f('ix_sync_jobs_status'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_external_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_celery_task_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_airbyte_job_id'), table_name='sync_jobs')
    op.drop_index('idx_sync_jobs_store_entity', table_name='sync_jobs')
    op.drop_index('idx_sync_jobs_created_at', table_name='sync_jobs')
    op.drop_table('sync_jobs')
    op.drop_index(op.f('ix_sync_checkpoints_id'), table_name='sync_checkpoints')
    op.drop_index(op.f('ix_sync_checkpoints_external_id'), table_name='sync_checkpoints')
    op.drop_index('idx_sync_checkpoints_updated_at', table_name='sync_checkpoints')
    op.drop_index('idx_sync_checkpoints_store_entity', table_name='sync_checkpoints')
    op.drop_table('sync_checkpoints')
    op.drop_index(op.f('ix_store_sales_id'), table_name='store_sales')
    op.drop_table('store_sales')
    op.drop_index(op.f('ix_store_analytics_snapshots_id'), table_name='store_analytics_snapshots')
    op.drop_table('store_analytics_snapshots')
    op.drop_index(op.f('ix_scraping_jobs_id'), table_name='scraping_jobs')
    op.drop_index(op.f('ix_scraping_jobs_external_id'), table_name='scraping_jobs')
    op.drop_table('scraping_jobs')
    op.drop_index(op.f('ix_scraped_products_id'), table_name='scraped_products')
    op.drop_index(op.f('ix_scraped_products_external_id'), table_name='scraped_products')
    op.drop_index(op.f('ix_scraped_products_domain'), table_name='scraped_products')
    op.drop_table('scraped_products')
    op.drop_index(op.f('ix_scraped_collections_id'), table_name='scraped_collections')
    op.drop_index(op.f('ix_scraped_collections_external_id'), table_name='scraped_collections')
    op.drop_index(op.f('ix_scraped_collections_domain'), table_name='scraped_collections')
    op.drop_table('scraped_collections')
    op.drop_index(op.f('ix_rejected_assets_id'), table_name='rejected_assets')
    op.drop_table('rejected_assets')
    op.drop_index(op.f('ix_products_id'), table_name='products')
    op.drop_index(op.f('ix_products_external_id'), table_name='products')
    op.drop_table('products')
    op.drop_index(op.f('ix_product_performance_id'), table_name='product_performance')
    op.drop_table('product_performance')
    op.drop_index(op.f('ix_media_reviews_id'), table_name='media_reviews')
    op.drop_table('media_reviews')
    op.drop_index(op.f('ix_media_analytics_product_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_external_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_date'), table_name='media_analytics')
    op.drop_index('idx_media_analytics_variant_date', table_name='media_analytics')
    op.drop_index('idx_media_analytics_tenant_date', table_name='media_analytics')
    op.drop_index('idx_media_analytics_product_date', table_name='media_analytics')
    op.drop_table('media_analytics')
    op.drop_index(op.f('ix_forecasts_id'), table_name='forecasts')
    op.drop_table('forecasts')
    op.drop_index(op.f('ix_dead_letter_queue_source_type'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_resolved'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_id'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_external_id'), table_name='dead_letter_queue')
    op.drop_index('idx_dlq_store_id', table_name='dead_letter_queue')
    op.drop_index('idx_dlq_created_at', table_name='dead_letter_queue')
    op.drop_table('dead_letter_queue')
    op.drop_index(op.f('ix_customers_id'), table_name='customers')
    op.drop_index(op.f('ix_customers_external_id'), table_name='customers')
    op.drop_table('customers')
    op.drop_index(op.f('ix_analytics_events_user_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_timestamp'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_session_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_product_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_order_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_external_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_event_id'), table_name='analytics_events')
    op.drop_index('idx_analytics_events_tenant_timestamp', table_name='analytics_events')
    op.drop_index('idx_analytics_events_session_timestamp', table_name='analytics_events')
    op.drop_index('idx_analytics_events_product_timestamp', table_name='analytics_events')
    op.drop_index('idx_analytics_events_conversion', table_name='analytics_events')
    op.drop_table('analytics_events')
    op.drop_index(op.f('ix_ab_test_experiments_id'), table_name='ab_test_experiments')
    op.drop_index(op.f('ix_ab_test_experiments_external_id'), table_name='ab_test_experiments')
    op.drop_table('ab_test_experiments')
    op.drop_index(op.f('ix_subscriptions_stripe_subscription_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_external_id'), table_name='subscriptions')
    op.drop_table('subscriptions')
    op.drop_index(op.f('ix_stores_id'), table_name='stores')
    op.drop_index(op.f('ix_stores_external_id'), table_name='stores')
    op.drop_index(op.f('ix_stores_airbyte_source_id'), table_name='stores')
    op.drop_index(op.f('ix_stores_airbyte_connection_id'), table_name='stores')
    op.drop_table('stores')
    op.drop_index(op.f('ix_scraped_documents_id'), table_name='scraped_documents')
    op.drop_index(op.f('ix_scraped_documents_external_id'), table_name='scraped_documents')
    op.drop_index(op.f('ix_scraped_documents_domain'), table_name='scraped_documents')
    op.drop_table('scraped_documents')
    op.drop_index(op.f('ix_payment_methods_stripe_payment_method_id'), table_name='payment_methods')
    op.drop_index(op.f('ix_payment_methods_id'), table_name='payment_methods')
    op.drop_index(op.f('ix_payment_methods_external_id'), table_name='payment_methods')
    op.drop_table('payment_methods')
    op.drop_index(op.f('ix_media_variants_id'), table_name='media_variants')
    op.drop_index(op.f('ix_media_variants_external_id'), table_name='media_variants')
    op.drop_table('media_variants')
    op.drop_index(op.f('ix_invoices_stripe_invoice_id'), table_name='invoices')
    op.drop_index(op.f('ix_invoices_id'), table_name='invoices')
    op.drop_index(op.f('ix_invoices_external_id'), table_name='invoices')
    op.drop_table('invoices')
    op.drop_index(op.f('ix_generation_requests_id'), table_name='generation_requests')
    op.drop_table('generation_requests')
    op.drop_index(op.f('ix_credit_transactions_id'), table_name='credit_transactions')
    op.drop_index(op.f('ix_credit_transactions_external_id'), table_name='credit_transactions')
    op.drop_table('credit_transactions')
    op.drop_index(op.f('ix_billing_usage_stripe_usage_record_id'), table_name='billing_usage')
    op.drop_index(op.f('ix_billing_usage_id'), table_name='billing_usage')
    op.drop_index(op.f('ix_billing_usage_external_id'), table_name='billing_usage')
    op.drop_table('billing_usage')
    op.drop_index(op.f('ix_user_sessions_session_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_refresh_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_external_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index(op.f('ix_tenants_uuid'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_slug'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_id'), table_name='tenants')
    op.drop_table('tenants')
    op.drop_index(op.f('ix_password_resets_token'), table_name='password_resets')
    op.drop_index(op.f('ix_password_resets_id'), table_name='password_resets')
    op.drop_index(op.f('ix_password_resets_external_id'), table_name='password_resets')
    op.drop_table('password_resets')
    op.drop_index(op.f('ix_oauth_accounts_id'), table_name='oauth_accounts')
    op.drop_index(op.f('ix_oauth_accounts_external_id'), table_name='oauth_accounts')
    op.drop_table('oauth_accounts')
    op.drop_index(op.f('ix_media_jobs_idempotency_key'), table_name='media_jobs')
    op.drop_index(op.f('ix_media_jobs_id'), table_name='media_jobs')
    op.drop_index(op.f('ix_media_jobs_external_id'), table_name='media_jobs')
    op.drop_table('media_jobs')
    op.drop_index(op.f('ix_generation_batches_id'), table_name='generation_batches')
    op.drop_index(op.f('ix_generation_batches_external_id'), table_name='generation_batches')
    op.drop_table('generation_batches')
    op.drop_index(op.f('ix_generated_assets_id'), table_name='generated_assets')
    op.drop_index(op.f('ix_generated_assets_external_id'), table_name='generated_assets')
    op.drop_table('generated_assets')
    op.drop_index(op.f('ix_users_uuid'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_templates_id'), table_name='templates')
    op.drop_index(op.f('ix_templates_external_id'), table_name='templates')
    op.drop_table('templates')
    op.drop_index(op.f('ix_scraping_platforms_id'), table_name='scraping_platforms')
    op.drop_index(op.f('ix_scraping_platforms_external_id'), table_name='scraping_platforms')
    op.drop_table('scraping_platforms')
    op.drop_index(op.f('ix_inventory_levels_id'), table_name='inventory_levels')
    op.drop_index(op.f('ix_inventory_levels_external_id'), table_name='inventory_levels')
    op.drop_table('inventory_levels')
    op.drop_index(op.f('ix_holidays_id'), table_name='holidays')
    op.drop_table('holidays')
    op.drop_index(op.f('ix_email_verifications_token'), table_name='email_verifications')
    op.drop_index(op.f('ix_email_verifications_id'), table_name='email_verifications')
    op.drop_index(op.f('ix_email_verifications_external_id'), table_name='email_verifications')
    op.drop_index(op.f('ix_email_verifications_email'), table_name='email_verifications')
    op.drop_table('email_verifications')
    op.drop_index(op.f('ix_assets_uuid_external_id'), table_name='assets')
    op.drop_index(op.f('ix_assets_id'), table_name='assets')
    op.drop_table('assets')
    # ### end Alembic commands ###
