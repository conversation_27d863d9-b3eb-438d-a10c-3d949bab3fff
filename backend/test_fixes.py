#!/usr/bin/env python3
"""
Test script to verify the fixes for the failing tests.
This script tests the main issues without running the full test suite.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set testing environment
os.environ["TESTING"] = "True"
os.environ["IMAGE_PROVIDER_OVERRIDE"] = "example_image"
os.environ["VIDEO_PROVIDER_OVERRIDE"] = "example_video"
os.environ["TEXT_PROVIDER_OVERRIDE"] = "example_text"
os.environ["PLATFORM_PUSH_OVERRIDE"] = "example"

async def test_provider_manager():
    """Test that provider manager works with overrides."""
    print("Testing provider manager...")
    
    try:
        from modules.media.providers.manager import get_text_provider, get_image_provider, get_video_provider
        
        # Test text provider override
        text_provider = await get_text_provider("gemini")
        print(f"✅ Text provider: {text_provider.provider_name}")
        assert text_provider.provider_name == "example_text"
        
        # Test image provider override
        image_provider = await get_image_provider("banana")
        print(f"✅ Image provider: {image_provider.provider_name}")
        assert image_provider.provider_name == "example_image"
        
        # Test video provider override
        video_provider = await get_video_provider("veo3")
        print(f"✅ Video provider: {video_provider.provider_name}")
        assert video_provider.provider_name == "example_video"
        
        print("✅ Provider manager tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Provider manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_media_service():
    """Test that media service can be imported and basic operations work."""
    print("Testing media service...")
    
    try:
        from modules.media.service import media_service
        from modules.media.models import MediaJob
        print("✅ Media service imported successfully")
        
        # Test that we can create a MediaJob instance
        job = MediaJob()
        job.media_type = "image"
        job.provider = "banana"
        job.product_id = 12345
        job.user_id = 1
        print(f"✅ MediaJob created with external_id: {job.external_id}")
        
        print("✅ Media service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Media service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_imports():
    """Test that basic imports work."""
    print("Testing basic imports...")
    
    try:
        from modules.media.models import MediaJob, MediaJobStatus
        from modules.media.schemas import MediaGenerateRequest
        from modules.auth.models import User
        print("✅ Basic imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🧪 Running test fixes verification...")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Media Service", test_media_service),
        ("Provider Manager", test_provider_manager),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        result = await test_func()
        results.append((test_name, result))
        print()
    
    print("=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All tests passed! The fixes are working.")
    else:
        print("⚠️  Some tests failed. Need to investigate further.")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
