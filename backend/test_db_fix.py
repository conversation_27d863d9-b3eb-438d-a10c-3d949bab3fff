#!/usr/bin/env python3
"""
Test script to verify the database lazy initialization fix.
This simulates the conditions that would occur in a forked Celery worker.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.db.database import get_session_factory, get_db
from sqlalchemy import select, text

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test database connection in an async context."""
    try:
        logger.info("Testing database connection...")

        # Get session factory
        session_factory = get_session_factory()
        logger.info(f"Session factory type: {type(session_factory)}")

        # Create a session and test a simple query
        async with session_factory() as session:
            logger.info("Created database session")

            # Test a simple query
            result = await session.execute(text("SELECT 1 as test"))
            row = result.first()
            logger.info(f"Query result: {row.test}")

            logger.info("Database connection test successful!")

    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        raise

async def main():
    """Main test function."""
    logger.info("Starting database fix test...")

    # Test the database connection
    await test_database_connection()

    logger.info("All tests passed!")

if __name__ == "__main__":
    # Simulate what happens in a Celery worker
    logger.info("Simulating Celery worker environment...")

    # Run the async test
    asyncio.run(main())