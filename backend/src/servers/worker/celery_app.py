"""
Celery application configuration for the worker.

This module provides the main Celery app instance used by all tasks and main.py.
"""

import json
import logging
from pathlib import Path

from celery import Celery

from core.config import get_settings

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create Celery app (shared instance)
celery_app = Celery(
    'productvideo_worker',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['servers.worker.tasks']
)

# Load configuration from JSON
config_path = Path(__file__).parent / "config.json"
with open(config_path) as f:
    celery_config = json.load(f)

# Add logging configuration to prevent Celery from overriding our logging
celery_config.update({
    'worker_log_format': '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - PID:%(process)d [%(processName)s] - %(message)s',
    'worker_task_log_format': '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - PID:%(process)d [%(processName)s] - %(message)s',
    'worker_log_color': False,
    'worker_redirect_stdouts': True,
    'worker_redirect_stdouts_level': settings.LOG_LEVEL.upper(),
    # Reduce Celery's built-in task logging to prevent duplication with LoggedTask
    'worker_log_level': 'WARNING',
    'task_log_level': 'WARNING'
})

celery_app.conf.update(celery_config)

logger.info("Shared Celery app initialized successfully")