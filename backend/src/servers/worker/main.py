"""
Main entry point for Ce<PERSON>y worker.
Replaces BullMQ with Celery for distributed task processing.
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for absolute imports
current_dir = Path(__file__).parent
src_dir = current_dir.parent
sys.path.insert(0, str(src_dir))

from core.config import get_settings
from core.utils.logging import setup_logging

# Setup structured logging IMMEDIATELY to override Celery's logging
setup_logging(log_filename="worker.log")

# Test that structured logging is working
logger = logging.getLogger(__name__)
logger.info("Worker starting up with structured logging enabled", extra={
    'startup_time': datetime.now().isoformat(),
    'worker_type': 'celery'
})

# Import all models to ensure they are registered with SQLAlchemy
import core.db.models  # noqa: F401

# Get settings after logging is set up
settings = get_settings()

from celery.signals import worker_ready, worker_shutdown

from modules.media.processors import MediaProcessor, MediaPushProcessor
from modules.analytics.processors import AnalyticsProcessor
from servers.worker.celery_app import celery_app

# Make celery_app available as 'main' for Celery CLI
main = celery_app

logger = logging.getLogger(__name__)

# Celery signal handlers
@worker_ready.connect
def worker_ready_handler(sender, **kwargs):
    """Called when Celery worker is ready."""
    logger.info(f"Celery worker {sender.hostname} is ready")

@worker_shutdown.connect
def worker_shutdown_handler(sender, **kwargs):
    """Called when Celery worker shuts down."""
    logger.info(f"Celery worker {sender.hostname} is shutting down")

# Import tasks to register them with Celery
try:
    from . import tasks
    logger.info("Celery tasks imported successfully")
except ImportError as e:
    logger.error(f"Failed to import Celery tasks: {e}")
    raise


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'beat':
        # Run Celery Beat scheduler for periodic tasks
        print("Starting Celery Beat scheduler...")
        from celery.bin.beat import beat
        beat(celery_app).run()
    else:
        # Run Celery worker (default)
        print("Starting Celery worker...")
        from celery.bin.worker import worker
        w = worker(app=celery_app)
        w.run()

    # Alternative usage instructions
    print("\nUsage:")
    print("  Celery worker: python main.py")
    print("  Celery beat:   python main.py beat")
    print("  Or via docker-compose: docker-compose up worker")
    print("  Or manually: celery -A servers.worker.main worker --loglevel=info")
    print("  Beat manually: celery -A servers.worker.main beat --loglevel=info")
