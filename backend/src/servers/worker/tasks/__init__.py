"""
Organized Celery tasks module.

This module provides a clean organization of all Celery tasks by category:
- sync_tasks: Data synchronization tasks
- webhook_tasks: Webhook processing tasks  
- media_tasks: Media generation and processing tasks
"""

# Import all task modules to register them with Celery
from .sync_tasks import *
from .webhook_tasks import *
from .media_tasks import *


# Export commonly used tasks for easy importing
__all__ = [
    # Sync tasks
    'bulk_sync_store',
    'check_sync_status',
    'monitor_scheduled_syncs',
    'cleanup_old_sync_jobs',

    # Webhook tasks
    'process_webhook_event',
    'cleanup_old_webhook_events',
    'retry_failed_webhooks',

    # Media tasks
    'generate_product_video',
    'generate_product_images',
    'push_media_to_platform',
    'batch_generate_videos',
    'cleanup_old_media',
]
