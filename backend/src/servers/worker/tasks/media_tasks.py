"""
E-commerce Media Generation and Processing Celery Tasks.

This module contains all tasks related to generating and processing
professional media content for e-commerce stores including images, videos, and text.
Integrates with context engine, prompt engine, and provider system for high-quality results.
"""

import logging
from typing import Dict, Any, Optional, List

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask
from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus

logger = logging.getLogger(__name__)




@celery_app.task(name='media.generate_media', bind=True, max_retries=3)
def generate_media(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate professional e-commerce media using comprehensive AI pipeline.

    This task handles the complete media generation workflow including:
    - Product context analysis and enrichment
    - Professional prompt generation for different media types
    - Multi-provider generation with fallback support
    - Quality assessment and optimization
    - Storage and variant management

    Args:
        task_data: Complete task data including full payload

    Returns:
        Generation results with comprehensive metadata
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    # In Celery forked workers, we need to ensure we create a fresh event loop
    try:
        return asyncio.run(_generate_media_async(task_data))
    except Exception as e:
        logger.exception(f"Error generating media for task_data: {task_data}. Error: {e}", exc_info=True)
        raise


async def _update_job_variants(db, job_external_id: str, result_data: Dict[str, Any], status):
    """Update job variants with generation results."""
    from sqlalchemy import select
    from modules.media.models import MediaVariant, MediaJob
    from datetime import datetime

    # First get the job by external_id to get the database ID
    job_result = await db.execute(
        select(MediaJob).filter(MediaJob.external_id == job_external_id)
    )
    job = job_result.scalar_one_or_none()
    if not job:
        logger.error(f"Job with external_id {job_external_id} not found")
        return

    variants_result = await db.execute(
        select(MediaVariant).filter(MediaVariant.job_id == job.id)
    )
    variants = variants_result.scalars().all()
    logger.info(f"DEBUG: Found {len(variants)} variants for job {job_external_id} in _update_job_variants")
    logger.info(f"DEBUG: Updating variants with status {status}")

    # Directly use the standardized variants from result_data
    provider_variants = result_data.get("variants", [])
    media_type = result_data.get("media_type", "image")  # Get media type from result data, default to "image"

    for i, variant in enumerate(variants):
        logger.info(f"DEBUG: Updating variant {i+1}/{len(variants)}: {variant.variant_name}, current status: {variant.status}")
        variant.status = status
        variant.updated_at = datetime.now()

        if status.name == "COMPLETED" and i < len(provider_variants):
            provider_variant = provider_variants[i]
            # Assign URLs and duration based on media type and provider variant data
            if media_type == "image":
                variant.image_url = provider_variant.get("image_url")
                logger.info(f"DEBUG: Set image_url for variant {variant.variant_name}: {variant.image_url}")
            elif media_type == "video":
                variant.video_url = provider_variant.get("video_url")
                logger.info(f"DEBUG: Set video_url for variant {variant.variant_name}: {variant.video_url}")
                variant.thumbnail_url = provider_variant.get("thumbnail_url")
                logger.info(f"DEBUG: Set thumbnail_url for variant {variant.variant_name}: {variant.thumbnail_url}")
                variant.duration_seconds = provider_variant.get("duration_seconds")
                logger.info(f"DEBUG: Set duration_seconds for variant {variant.variant_name}: {variant.duration_seconds}")
            elif media_type == "text":
                variant.text_content = provider_variant.get("text")
                logger.info(f"DEBUG: Set text_content for variant {variant.variant_name}: {variant.text_content}")
            else:
                # Fallback for other types, try to determine from URL content
                if provider_variant.get("image_url"):
                    variant.image_url = provider_variant.get("image_url")
                elif provider_variant.get("video_url"):
                    variant.video_url = provider_variant.get("video_url")
        elif status.name == "FAILED":
            variant.error_message = result_data.get("error", "Generation failed")
            logger.info(f"DEBUG: Set error for variant {variant.variant_name}: {variant.error_message}")

    logger.info(f"DEBUG: Finished updating {len(variants)} variants for job {job_external_id}")

async def _update_job_variants(db, job_external_id: str, result_data: Dict[str, Any], status):
    """Update job variants with generation results."""
    from sqlalchemy import select
    from modules.media.models import MediaVariant, MediaJob
    from datetime import datetime

    # First get the job by external_id to get the database ID
    job_result = await db.execute(
        select(MediaJob).filter(MediaJob.external_id == job_external_id)
    )
    job = job_result.scalar_one_or_none()
    if not job:
        logger.error(f"Job with external_id {job_external_id} not found")
        return

    variants_result = await db.execute(
        select(MediaVariant).filter(MediaVariant.job_id == job.id)
    )
    variants = variants_result.scalars().all()
    logger.info(f"DEBUG: Found {len(variants)} variants for job {job_external_id} in _update_job_variants")
    logger.info(f"DEBUG: Updating variants with status {status}")

    # Directly use the standardized variants from result_data
    provider_variants = result_data.get("variants", [])
    media_type = result_data.get("media_type", "image")  # Get media type from result data, default to "image"

    for i, variant in enumerate(variants):
        logger.info(f"DEBUG: Updating variant {i+1}/{len(variants)}: {variant.variant_name}, current status: {variant.status}")
        variant.status = status
        variant.updated_at = datetime.now()

        if status.name == "COMPLETED" and i < len(provider_variants):
            provider_variant = provider_variants[i]
            # Assign URLs and duration based on media type and provider variant data
            if media_type == "image":
                variant.image_url = provider_variant.get("image_url")
                logger.info(f"DEBUG: Set image_url for variant {variant.variant_name}: {variant.image_url}")
            elif media_type == "video":
                variant.video_url = provider_variant.get("video_url")
                logger.info(f"DEBUG: Set video_url for variant {variant.variant_name}: {variant.video_url}")
                variant.thumbnail_url = provider_variant.get("thumbnail_url")
                logger.info(f"DEBUG: Set thumbnail_url for variant {variant.variant_name}: {variant.thumbnail_url}")
                variant.duration_seconds = provider_variant.get("duration_seconds")
                logger.info(f"DEBUG: Set duration_seconds for variant {variant.variant_name}: {variant.duration_seconds}")
            elif media_type == "text":
                variant.text_content = provider_variant.get("text")
                logger.info(f"DEBUG: Set text_content for variant {variant.variant_name}: {variant.text_content}")
            else:
                # Fallback for other types, try to determine from URL content
                if provider_variant.get("image_url"):
                    variant.image_url = provider_variant.get("image_url")
                elif provider_variant.get("video_url"):
                    variant.video_url = provider_variant.get("video_url")
        elif status.name == "FAILED":
            variant.error_message = result_data.get("error", "Generation failed")
            logger.info(f"DEBUG: Set error for variant {variant.variant_name}: {variant.error_message}")

    logger.info(f"DEBUG: Finished updating {len(variants)} variants for job {job_external_id}")

async def _generate_media_async(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Async implementation of media generation to avoid event loop conflicts.
    """
    # Use async database session for proper async operations
    from core.db.database import get_session_factory
    from sqlalchemy import select, update, func
    from modules.media.service import media_service
    from modules.media.engines.context_engine import context_engine
    from modules.media.schemas import (
        ProviderMediaRequest,
        ProductContext,
        ShopBranding,
        ProductCategory,
        TargetAudience,
        ContentStyle,
        UsageContext
    )
    from modules.storage.storage_service import media_storage_service
    from datetime import datetime

    session_factory = get_session_factory()
    async with session_factory() as db:
        job = None
        try:
            job_id = task_data.get("job_id")
            user_id = task_data.get("user_id")
            full_payload = task_data.get("full_payload")

            if not job_id:
                raise ValueError("Job ID is required")

            try:
                # Convert job_id string to UUID if needed
                from uuid import UUID
                try:
                    job_uuid = UUID(job_id) if isinstance(job_id, str) else job_id
                except ValueError:
                    raise ValueError(f"Invalid job_id format: {job_id}")

                # Get the job from database by external_id
                job_result = await db.execute(select(MediaJob).filter(MediaJob.external_id == job_uuid))
                job = job_result.scalar_one_or_none()
    
                if not job:
                    raise ValueError(f"Job {job_id} not found")
    
                # Store the actual database ID for internal operations
                db_job_id = job.id

                # Update job status to processing
                logger.info(f"DEBUG: Setting job {job_id} status to PROCESSING")
                job.status = MediaJobStatus.PROCESSING
                job.started_at = datetime.now()
                await db.commit()
                logger.info(f"DEBUG: Committed job {job_id} as PROCESSING")
            except ValueError as e:
                logger.error(f"Media generation failed for job {job_id} (db_id: {db_job_id if 'db_job_id' in locals() else 'unknown'}): {e}")
                # Mark the job as failed and do not retry
                if job: # If job object exists, update its status
                    job.status = MediaJobStatus.FAILED
                    job.ended_at = datetime.now()
                    await db.commit()
                    logger.info(f"DEBUG: Set job {job_id} (db_id: {db_job_id if 'db_job_id' in locals() else 'unknown'}) to FAILED due to exception")
                return # Exit the task, do not retry

            logger.info(f"Starting media generation for job {job_id} (db_id: {db_job_id})")

            # Extract generation parameters from full payload
            media_type = full_payload.get("media_type", full_payload.get("mode", "image"))
            model = full_payload.get("model", "banana")
            settings = full_payload.get("settings") or {}
            items = full_payload.get("items", [])

            # Use resolved provider if available (from override system)
            resolved_provider = job.resolved_provider or model
            logger.info(f"WORKER: Using resolved provider: {resolved_provider} (original: {model})")

            # Generate idempotency key if not provided
            idempotency_key = full_payload.get("idempotency_key")
            if not idempotency_key:
                from modules.media.schemas import MediaGenerateRequest
                temp_request = MediaGenerateRequest(**full_payload)
                idempotency_key = temp_request.generate_idempotency_key()

            # Check for existing results with same idempotency key
            if not full_payload.get("force_regenerate", False):
                cached_result = await _check_cached_result(idempotency_key, user_id)
                if cached_result:
                    logger.info(f"Returning cached result for idempotency key {idempotency_key}")
                    return cached_result

            # Quota and budget checks are now performed in the API endpoint before queuing

            # Process each product item with comprehensive context
            product_id = None # Initialize product_id for error logging
            results = []
            for i, item in enumerate(items):
                try:
                    # Update progress for current product
                    progress = (i / len(items)) * 100.0
                    job.progress_percentage = progress
                    await db.commit()
                    logger.info(f"DEBUG: Updated job {job_id} progress to {progress}%")

                    # Debug logging for item structure
                    logger.info(f"DEBUG: Processing item {i}: type={type(item)}, value={item}")

                    # Extract product information
                    if item is None:
                        logger.error(f"Item {i} is None, skipping")
                        continue

                    if not isinstance(item, dict):
                        logger.error(f"Item {i} is not a dict, it's {type(item)}, skipping")
                        continue

                    product_id = str(item.get("productId", item.get("product_id", "")) or "")
                    if not product_id:
                        product_id = f"product_{i+1}"  # Fallback for missing product ID

                    product_title = item.get("title", item.get("prompt", "")) or product_id

                    logger.info(f"Processing product {product_id}: {product_title}")

                    # Create comprehensive product context
                    logger.info(f"DEBUG: Creating product context for item: {item}")
                    product_context = await _create_enhanced_product_context(item, full_payload)
                    logger.info(f"DEBUG: Product context created: {product_context}")

                    # Create shop branding context
                    logger.info(f"DEBUG: Creating shop branding context")
                    shop_branding = await _create_shop_branding_context(full_payload)
                    logger.info(f"DEBUG: Shop branding context created: {shop_branding}")

                    # Create media generation request
                    logger.info(f"DEBUG: Creating ProviderMediaRequest with shop_branding: {shop_branding}")
                    media_request = ProviderMediaRequest(
                        product_title=product_title,
                        media_type=media_type,
                        product_context=product_context,
                        shop_branding=shop_branding,
                        custom_prompt=item.get("prompt"),
                        num_images=4 if media_type == "image" else 1,
                        variants_count=4,
                        aspect_ratio=settings.get("aspectRatio", "1:1"),
                        style="professional",
                        model=resolved_provider,
                        settings=settings,
                        custom_config={
                            "mode": full_payload.get("mode"),
                            "settings": settings,
                            "reference_images": item.get("referenceImageUrls", []),
                            "guidance": settings.get("guidance", 7.5),
                            "steps": settings.get("steps", 25),
                            "seed": settings.get("seed"),
                            "upscale": settings.get("upscale", True),
                            "safety": settings.get("safety", True)
                        },
                        target_platforms=full_payload.get("target_platforms", ["website"]),
                        campaign_theme=full_payload.get("campaign_theme"),
                        call_to_action=full_payload.get("call_to_action")
                    )
                    logger.info(f"DEBUG: ProviderMediaRequest created successfully")

                    # Provider override checking is handled internally by the media service
                    logger.info(f"DEBUG: Using resolved provider {resolved_provider} (original: {model}) for media_type {media_type}")

                    # Generate media using enhanced provider system
                    try:
                        result = await media_service.generate_media_with_provider(model, media_request)
                    except Exception as gen_error:
                        logger.error(f"Media generation failed for product {product_id}: {gen_error}")
                        await _update_job_variants(db, job_id, {"error": f"Generation failed: {str(gen_error)}"}, MediaVariantStatus.FAILED)
                        results.append({
                            "product_id": product_id,
                            "status": "failed",
                            "error": f"Generation failed: {str(gen_error)}"
                        })
                        continue # Skip to the next product

                    if result is None:
                        logger.error(f"Media generation failed for product {product_id}. generate_media_with_provider returned None.")
                        await _update_job_variants(db, job_id, {"error": "Media generation failed"}, MediaVariantStatus.FAILED)
                        results.append({
                            "product_id": product_id,
                            "status": "failed",
                            "error": "Media generation failed"
                        })
                        continue # Skip to the next product

                    if result.success:
                        logger.info(f"DEBUG: Result success for product {product_id}, processing result")
                        # Apply content safety checks
                        safety_result = await _apply_content_safety_checks(result, media_request, product_context)

                        # Create processed_result in the format expected by the rest of the code
                        processed_result = {
                            "metadata": {},
                            "quality_score": 0.8,
                            "safety_checked": True,
                            "content_flags": safety_result.get("flags", []),
                            "needs_review": not safety_result.get("safe", True),
                            "media_type": media_type,
                            "variants": result.variants # Directly use the standardized variants from the provider
                        }

                        # Update variants with results
                        variant_status = MediaVariantStatus.COMPLETED if safety_result.get("safe", True) else MediaVariantStatus.PROCESSING
                        logger.info(f"DEBUG: Calling _update_job_variants for job {job.id} with status {variant_status}")
                        await _update_job_variants(db, job_id, processed_result, variant_status)
                        logger.info(f"DEBUG: _update_job_variants completed for job {job.id}")

                        # Quota deduction is now handled at the API level after job completion

                        results.append({
                            "product_id": product_id,
                            "status": "success",
                            "variants": processed_result.get("variants", []),
                            "metadata": processed_result.get("metadata", {}),
                            "quality_score": processed_result.get("quality_score", 0.8),
                            "safety_checked": processed_result.get("safety_checked", False),
                            "content_flags": processed_result.get("content_flags", [])
                        })
                    else:
                        # Mark variants as failed
                        await _update_job_variants(db, job_id, {"error": result.error_message}, MediaVariantStatus.FAILED)

                        results.append({
                            "product_id": product_id,
                            "status": "failed",
                            "error": result.error_message
                        })

                except Exception as e:
                    logger.exception(f"Error processing product {product_id}: {e}")
                    results.append({
                        "product_id": product_id,
                        "status": "failed",
                        "error": str(e)
                    })

            # Determine final job status based on results
            successful_products = len([r for r in results if r["status"] == "success"])
            total_products = len(items)

            logger.info(f"DEBUG: Job {job_id} (db_id: {db_job_id}) results: {successful_products}/{total_products} products succeeded")

            if successful_products == 0:
                # All products failed - job should be failed
                job.status = MediaJobStatus.FAILED
                job.error_message = "All products failed to generate"
                job.progress_percentage = 0.0
                final_status = "failed"
                logger.info(f"DEBUG: Setting job {job_id} status to FAILED")
            elif successful_products < total_products:
                # Some products failed - job completed with partial success
                job.status = MediaJobStatus.COMPLETED
                job.error_message = f"{total_products - successful_products} out of {total_products} products failed"
                job.progress_percentage = 100.0
                final_status = "completed_with_errors"
                logger.info(f"DEBUG: Setting job {job_id} status to COMPLETED (partial)")
            else:
                # All products succeeded
                job.status = MediaJobStatus.COMPLETED
                job.progress_percentage = 100.0
                final_status = "completed"
                logger.info(f"DEBUG: Setting job {job_id} status to COMPLETED")

            job.completed_at = datetime.now()
            logger.info(f"DEBUG: Committing job {job_id} status: {job.status}")
            await db.commit()
            logger.info(f"DEBUG: Successfully committed job {job_id}")

            # Log comprehensive result summary
            result_summary = {
                "job_id": job_id,
                "status": final_status,
                "total_products": total_products,
                "successful_products": successful_products,
                "results_count": len(results),
                "generation_metadata": {
                    "media_type": media_type,
                    "model_used": model,
                    "enhanced_pipeline": True,
                    "context_enriched": True
                }
            }
            logger.info(f"Media generation completed for job {job_id} (db_id: {db_job_id}): {successful_products}/{total_products} products succeeded - {result_summary}")

            full_result = {
                "job_id": job_id,  # This is already the external_id
                "status": final_status,
                "results": results,
                "total_products": total_products,
                "successful_products": successful_products,
                "generation_metadata": {
                    "media_type": media_type,
                    "model_used": resolved_provider,
                    "original_model": model,
                    "enhanced_pipeline": True,
                    "context_enriched": True
                }
            }

            return full_result

        except Exception as e:
            logger.error(f"Media generation failed for job {task_data.get('job_id')} (db_id: {db_job_id if 'db_job_id' in locals() else 'unknown'}): {e}", exc_info=True)

            # Update job status to failed
            try:
                if 'job' in locals() and job:
                    logger.info(f"DEBUG: Setting job {job.id} to FAILED due to exception: {e}")
                    job.status = MediaJobStatus.FAILED
                    job.error_message = str(e)
                    job.completed_at = datetime.now()
                    await db.commit()
                    logger.info(f"DEBUG: Committed job {job.id} as FAILED")
            except Exception as db_error:
                logger.error(f"Failed to update job status: {db_error}")

            raise


@celery_app.task(name='media.push_to_platform', bind=True, max_retries=3)
def push_to_platform(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Push generated media to platform store.

    This task handles pushing completed media variants to connected platform stores.
    It uses the media push processor for robust error handling and retry logic.

    Args:
        task_data: Dictionary containing task parameters including:
            - variant_id: Media variant ID to push
            - store_id: Store ID to push to
            - user_id: User ID (optional)
            - product_id: Product ID (optional)

    Returns:
        Push result with status and metadata
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    # In Celery forked workers, we need to ensure we create a fresh event loop
    return asyncio.run(_push_to_platform_async(task_data))


async def _push_to_platform_async(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Async implementation of push to platform to avoid event loop conflicts.
    """
    from modules.media.processors.media_push_processor import MediaPushProcessor
    from core.db.database import get_session_factory
    from sqlalchemy import select
    from modules.media.models import MediaVariant
    from modules.stores.models import Store

    session_factory = get_session_factory()
    async with session_factory() as db:
        try:
            # Extract parameters from task_data
            variant_id = task_data.get("variant_id")
            store_id = task_data.get("store_id")
            user_id = task_data.get("user_id")
            product_id = task_data.get("product_id")

            if not variant_id:
                raise ValueError("variant_id is required")
            if not store_id:
                raise ValueError("store_id is required")

            # Get variant and store details
            variant_result = await db.execute(select(MediaVariant).filter(MediaVariant.id == variant_id))
            variant = variant_result.scalar_one_or_none()

            store_result = await db.execute(select(Store).filter(Store.id == store_id))
            store = store_result.scalar_one_or_none()

            if not variant:
                raise ValueError(f"Media variant {variant_id} not found")

            if not store:
                raise ValueError(f"Store {store_id} not found")

            # Prepare job data for push processor
            job_data = {
                "tenant_id": store.tenant_id,
                "variant_id": variant_id,
                "product_id": str(variant.job.product_id) if variant.job else product_id,
                "shop_domain": store.shop_domain,
                "store_type": store.platform,
                "user_id": variant.job.user_id if variant.job else user_id
            }

            # Use the media push processor
            processor = MediaPushProcessor()
            result = processor.process(job_data)

            logger.info(f"Push task completed for variant {variant_id} to store {store_id}: {result}")

            return result

        except Exception as e:
            logger.error(f"Push task failed for variant {variant_id}: {e}", exc_info=True)
            raise


@celery_app.task(name='media.cleanup_old_media')
def cleanup_old_media(days_old: int = 90):
    """
    Clean up old generated media files to save storage space.

    Args:
        days_old: Number of days old media to keep
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    # In Celery forked workers, we need to ensure we create a fresh event loop
    return asyncio.run(_cleanup_old_media_async(days_old))


async def _cleanup_old_media_async(days_old: int = 90) -> Dict[str, Any]:
    """
    Async implementation of cleanup to avoid event loop conflicts.
    """
    from core.db.database import get_session_factory
    from datetime import datetime, timezone, timedelta
    import os

    session_factory = get_session_factory()
    async with session_factory() as db:
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

            # Query old media records
            from sqlalchemy import select, and_
            from modules.media.models import MediaJob, MediaVariant
            from modules.storage.storage_service import media_storage_service

            # Find old completed jobs
            old_jobs_result = await db.execute(
                select(MediaJob).filter(
                    and_(
                        MediaJob.completed_at < cutoff_date,
                        MediaJob.status == MediaJobStatus.COMPLETED
                    )
                )
            )
            old_jobs = old_jobs_result.scalars().all()

            cleaned_files = 0
            cleaned_jobs = 0

            for job in old_jobs:
                try:
                    # Get variants for this job
                    variants_result = await db.execute(
                        select(MediaVariant).filter(MediaVariant.job_id == job.id)
                    )
                    variants = variants_result.scalars().all()

                    # Delete media files from storage
                    for variant in variants:
                        try:
                            if variant.image_url and "storage" in variant.image_url:
                                # Extract storage path and delete
                                storage_path = variant.image_url.split("/storage/")[-1]
                                await media_storage_service.delete_media(storage_path)
                                cleaned_files += 1

                            if variant.video_url and "storage" in variant.video_url:
                                storage_path = variant.video_url.split("/storage/")[-1]
                                await media_storage_service.delete_media(storage_path)
                                cleaned_files += 1

                        except Exception as e:
                            logger.warning(f"Failed to delete media file for variant {variant.id}: {e}")

                    # Delete database records
                    for variant in variants:
                        await db.delete(variant)

                    await db.delete(job)
                    cleaned_jobs += 1

                except Exception as e:
                    logger.error(f"Failed to cleanup job {job.id}: {e}")

            await db.commit()

            logger.info(f"Cleaned up {cleaned_jobs} jobs and {cleaned_files} media files older than {cutoff_date}")

            return {
                'status': 'completed',
                'message': f'Cleaned up {cleaned_jobs} jobs and {cleaned_files} files',
                'cleaned_jobs': cleaned_jobs,
                'cleaned_files': cleaned_files
            }

        except Exception as e:
            logger.error(f"Error cleaning up old media: {e}", exc_info=True)
            raise


async def _check_cached_result(idempotency_key: str, user_id: int) -> Optional[Dict[str, Any]]:
    """Check for cached media generation result."""
    try:
        from core.db.database import get_session_factory
        from sqlalchemy import select, and_
        from modules.media.models import MediaJob, MediaVariant

        session_factory = get_session_factory()
        async with session_factory() as db:
            # Look for completed job with same idempotency key
            job_result = await db.execute(
                select(MediaJob).filter(
                    and_(
                        MediaJob.idempotency_key == idempotency_key,
                        MediaJob.user_id == user_id,
                        MediaJob.status == MediaJobStatus.COMPLETED
                    )
                )
            )
            job = job_result.scalar_one_or_none()

            if job:
                # Get variants for this job
                variants_result = await db.execute(
                    select(MediaVariant).filter(MediaVariant.job_id == job.id)
                )
                variants = variants_result.scalars().all()

                # Return cached result
                result = {
                    'success': True,
                    'job_id': job.id,
                    'cached_result': True,
                    'idempotency_key': idempotency_key,
                    'variants': [
                        {
                            'id': v.id,
                            'image_url': v.image_url,
                            'video_url': v.video_url,
                            'text_content': v.text_content,
                            'status': v.status.value,
                            'quality_score': v.quality_score
                        }
                        for v in variants
                    ]
                }

                return result

            return None

    except Exception as e:
        logger.warning(f"Failed to check cached result: {e}")
        return None


def _generate_product_version_hash(product_data: Dict[str, Any]) -> str:
    """Generate version hash for product data to detect changes."""
    import hashlib
    import json

    # Include only fields that affect media generation
    version_data = {
        "title": product_data.get("title"),
        "description": product_data.get("description"),
        "category": product_data.get("category"),
        "colors": product_data.get("colors"),
        "materials": product_data.get("materials"),
        "key_features": product_data.get("key_features"),
        "target_audience": product_data.get("target_audience")
    }

    version_string = json.dumps(version_data, sort_keys=True)
    return hashlib.sha256(version_string.encode()).hexdigest()[:12]


def _should_regenerate_media(
    existing_version: Optional[str],
    current_version: str,
    media_type: str,
    changed_fields: List[str]
) -> bool:
    """Determine if media should be regenerated based on product changes."""
    if not existing_version or existing_version != current_version:
        # Check if changes affect this media type
        if media_type == "text":
            # Text regeneration needed for description, features, or category changes
            text_affecting_fields = {"title", "description", "key_features", "category", "target_audience"}
            return bool(set(changed_fields) & text_affecting_fields)
        elif media_type == "image":
            # Image regeneration needed for visual changes
            image_affecting_fields = {"title", "colors", "materials", "category"}
            return bool(set(changed_fields) & image_affecting_fields)
        elif media_type == "video":
            # Video regeneration needed for most changes
            video_affecting_fields = {"title", "description", "colors", "materials", "category", "key_features"}
            return bool(set(changed_fields) & video_affecting_fields)

    return False


async def _handle_multi_variant_products(items: List[Dict[str, Any]], full_payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Handle multi-variant products by deciding whether to generate universal or variant-specific assets."""
    if len(items) <= 1:
        return items

    # Check if items are variants of the same product
    base_product_ids = set()
    for item in items:
        product_id = str(item.get("product_id", ""))
        # Extract base product ID (remove variant suffix like -red, -large, etc.)
        base_id = product_id.split("-")[0] if "-" in product_id else product_id
        base_product_ids.add(base_id)

    if len(base_product_ids) == 1:
        # Multi-variant product - create universal asset
        logger.info(f"Detected multi-variant product with {len(items)} variants")

        # Merge variant information into a single universal item
        universal_item = items[0].copy()

        # Combine variant-specific information
        colors = set()
        sizes = set()
        materials = set()

        for item in items:
            if item.get("color"):
                colors.add(item["color"])
            if item.get("size"):
                sizes.add(item["size"])
            if item.get("material"):
                materials.add(item["material"])

        # Update universal item with combined variant info
        if colors:
            universal_item["colors"] = list(colors)
        if sizes:
            universal_item["sizes"] = list(sizes)
        if materials:
            universal_item["materials"] = list(materials)

        # Add note about variants
        universal_item["variant_note"] = f"Available in {len(items)} variants"

        return [universal_item]
    else:
        # Different products - process separately
        return items


async def _generate_alt_text(media_url: str, product_context: 'ProductContext') -> str:
    """Generate alt text for images using AI."""
    try:
        # Basic alt text generation based on product context
        alt_parts = []

        if product_context.title:
            alt_parts.append(product_context.title)

        if product_context.category:
            alt_parts.append(product_context.category.value.replace("_", " "))

        if product_context.colors:
            alt_parts.append(f"in {', '.join(product_context.colors)}")

        if product_context.materials:
            alt_parts.append(f"made of {', '.join(product_context.materials)}")

        alt_text = " ".join(alt_parts)

        # Ensure it's not too long
        if len(alt_text) > 125:
            alt_text = alt_text[:122] + "..."

        return alt_text or "Product image"

    except Exception as e:
        logger.warning(f"Failed to generate alt text: {e}")
        return "Product image"


async def _generate_captions(media_url: str, product_context: 'ProductContext') -> str:
    """Generate captions/subtitles for videos."""
    try:
        # Basic caption generation for product videos
        captions = []

        if product_context.title:
            captions.append(f"Introducing {product_context.title}")

        if product_context.key_features:
            captions.extend([f"Features: {feature}" for feature in product_context.key_features[:3]])

        if product_context.target_audience:
            captions.append(f"Perfect for {product_context.target_audience.value.replace('_', ' ')}")

        captions.append("Shop now!")

        # Format as SRT-style captions
        caption_text = "\n".join([
            f"{i+1}\n00:00:0{i*2},000 --> 00:00:0{(i+1)*2},000\n{caption}\n"
            for i, caption in enumerate(captions[:5])
        ])

        return caption_text

    except Exception as e:
        logger.warning(f"Failed to generate captions: {e}")
        return ""


def _apply_safe_defaults(product_context: Dict[str, Any]) -> Dict[str, Any]:
    """Apply safe defaults for missing product metadata."""
    safe_context = product_context.copy()

    # Safe defaults for missing fields
    if not safe_context.get("title"):
        safe_context["title"] = "Product"

    if not safe_context.get("description"):
        safe_context["description"] = "High-quality product"

    if not safe_context.get("category"):
        safe_context["category"] = "general"

    if not safe_context.get("colors"):
        # Don't specify colors if unknown
        pass

    if not safe_context.get("materials"):
        # Don't specify materials if unknown
        pass

    if not safe_context.get("target_audience"):
        safe_context["target_audience"] = "general_consumers"

    if not safe_context.get("key_features"):
        safe_context["key_features"] = ["High quality", "Durable", "Stylish"]

    return safe_context


async def _apply_content_safety_checks(
    result: 'ProviderMediaResult',
    request: 'ProviderMediaRequest',
    product_context: Optional['ProductContext'] = None
) -> Dict[str, Any]:
    """Apply content safety and legal compliance checks to generated content."""
    try:
        from modules.compliance.content_safety import content_safety_service

        safety_results = {
            "safe": True,
            "flags": [],
            "details": {},
            "filtered_content": {}
        }

        # Check text content if available
        if hasattr(result, 'variants') and result.variants:
            for i, variant in enumerate(result.variants):
                variant_flags = []
                variant_details = {}

                # Check text content
                if variant.get("text_content"):
                    text_safe, text_flags, text_details = await content_safety_service.check_content_safety(
                        variant["text_content"],
                        content_type="text",
                        product_category=product_context.category.value if product_context and product_context.category else None
                    )

                    if not text_safe:
                        variant_flags.extend(text_flags)
                        variant_details.update(text_details)

                        # Filter the content
                        filtered_text = content_safety_service.filter_content(
                            variant["text_content"],
                            text_flags
                        )
                        safety_results["filtered_content"][f"variant_{i}_text"] = filtered_text

                # Check image content
                if variant.get("image_url"):
                    image_safe, image_flags, image_details = await content_safety_service.check_content_safety(
                        variant["image_url"],
                        content_type="image",
                        product_category=product_context.category.value if product_context and product_context.category else None
                    )

                    if not image_safe:
                        variant_flags.extend(image_flags)
                        variant_details.update(image_details)

                # Check video content
                if variant.get("video_url"):
                    # Validate rights for video content
                    rights_valid, rights_issues = await content_safety_service.validate_rights(
                        "video",
                        media_url=variant["video_url"],
                        music_used=variant.get("music_used"),
                        audio_used=variant.get("audio_used")
                    )

                    if not rights_valid:
                        variant_flags.append("rights_issues")
                        variant_details["rights_issues"] = rights_issues

                if variant_flags:
                    safety_results["safe"] = False
                    safety_results["flags"].extend(variant_flags)
                    safety_results["details"][f"variant_{i}"] = variant_details

        # Check the main prompt for safety
        if hasattr(request, 'custom_prompt') and request.custom_prompt:
            prompt_safe, prompt_flags, prompt_details = await content_safety_service.check_content_safety(
                request.custom_prompt,
                content_type="text",
                product_category=product_context.category.value if product_context and product_context.category else None
            )

            if not prompt_safe:
                safety_results["safe"] = False
                safety_results["flags"].extend(prompt_flags)
                safety_results["details"]["prompt"] = prompt_details

        return safety_results

    except Exception as e:
        logger.warning(f"Content safety check failed: {e}")
        return {
            "safe": True,  # Default to safe if check fails
            "flags": [],
            "details": {"error": str(e)},
            "filtered_content": {}
        }


# Helper functions for media generation

async def _create_enhanced_product_context(item: Dict[str, Any], full_payload: Dict[str, Any]) -> 'ProductContext':
    """Create comprehensive product context from item data."""
    from modules.media.schemas import ProductContext, ProductCategory, TargetAudience

    # Extract product information
    prompt = item.get("prompt") or ""
    title = item.get("title", prompt) or "Product"
    description = item.get("description", "")

    # Infer category from title/description
    category = _infer_product_category(title, description)

    # Extract attributes
    colors = _extract_colors_from_text(f"{title} {description}")
    materials = _extract_materials_from_text(f"{title} {description}")

    # Determine target audience
    target_audience = _infer_target_audience(title, description, full_payload)

    return ProductContext(
        title=title,
        description=description,
        category=category,
        colors=colors,
        materials=materials,
        target_audience=target_audience,
        key_features=_extract_key_features(description),
        shopify_product_id=str(item.get("productId", "")),
        shopify_tags=item.get("tags", []),
        existing_images=item.get("referenceImageUrls", [])
    )


async def _create_shop_branding_context(full_payload: Dict[str, Any]) -> Optional['ShopBranding']:
    """Create shop branding context from payload."""
    from modules.media.schemas import ShopBranding, ContentStyle

    shop_info = full_payload.get("shop_info", {})
    if not shop_info:
        return None

    return ShopBranding(
        shop_name=shop_info.get("name", "Store"),
        brand_voice=shop_info.get("brand_voice", "professional"),
        color_palette=shop_info.get("colors", []),
        visual_style=ContentStyle(shop_info.get("visual_style", "professional")),
        brand_values=shop_info.get("values", [])
    )





    


def _infer_product_category(title: str, description: str) -> 'ProductCategory':
    """Infer product category from title and description."""
    from modules.media.schemas import ProductCategory

    text = f"{title} {description}".lower()

    if any(keyword in text for keyword in ["shoe", "boot", "sneaker", "sandal"]):
        return ProductCategory.FOOTWEAR
    elif any(keyword in text for keyword in ["shirt", "dress", "pants", "jacket"]):
        return ProductCategory.FASHION_APPAREL
    elif any(keyword in text for keyword in ["bag", "wallet", "watch", "sunglasses"]):
        return ProductCategory.ACCESSORIES
    elif any(keyword in text for keyword in ["makeup", "skincare", "beauty"]):
        return ProductCategory.BEAUTY_COSMETICS
    elif any(keyword in text for keyword in ["necklace", "ring", "earring", "jewelry"]):
        return ProductCategory.JEWELRY
    else:
        return ProductCategory.FASHION_APPAREL


def _extract_colors_from_text(text: str) -> List[str]:
    """Extract color information from text."""
    colors = []
    color_keywords = [
        "black", "white", "red", "blue", "green", "yellow", "orange",
        "purple", "pink", "brown", "gray", "grey", "navy", "beige"
    ]

    text_lower = text.lower()
    for color in color_keywords:
        if color in text_lower:
            colors.append(color)

    return colors


def _extract_materials_from_text(text: str) -> List[str]:
    """Extract material information from text."""
    materials = []
    material_keywords = [
        "cotton", "silk", "wool", "leather", "denim", "polyester",
        "metal", "gold", "silver", "plastic", "wood"
    ]

    text_lower = text.lower()
    for material in material_keywords:
        if material in text_lower:
            materials.append(material)

    return materials


def _extract_key_features(description: str) -> List[str]:
    """Extract key features from product description."""
    features = []
    feature_keywords = [
        "waterproof", "breathable", "comfortable", "durable", "lightweight",
        "wireless", "rechargeable", "adjustable", "machine washable"
    ]

    description_lower = description.lower()
    for feature in feature_keywords:
        if feature in description_lower:
            features.append(feature)

    return features


def _infer_target_audience(title: str, description: str, payload: Dict[str, Any]) -> List['TargetAudience']:
    """Infer target audience from product information."""
    from modules.media.schemas import TargetAudience

    audiences = []
    text = f"{title} {description}".lower()

    if any(keyword in text for keyword in ["trendy", "young", "teen"]):
        audiences.append(TargetAudience.GEN_Z)
    elif any(keyword in text for keyword in ["professional", "business", "office"]):
        audiences.append(TargetAudience.PROFESSIONALS)
    elif any(keyword in text for keyword in ["luxury", "premium", "high-end"]):
        audiences.append(TargetAudience.LUXURY_BUYERS)
    else:
        audiences.append(TargetAudience.MILLENNIALS)

    return audiences






