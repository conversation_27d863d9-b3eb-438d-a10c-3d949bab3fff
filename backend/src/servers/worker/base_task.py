"""
Base task classes for Celery tasks.

This module provides base classes for Celery tasks with enhanced logging and error handling.
"""

import logging
from typing import Any, Dict
from celery import Task

logger = logging.getLogger(__name__)


class LoggedTask(Task):
    """
    Base task class that provides enhanced logging for Celery tasks.

    This class automatically logs task start, success, and failure events
    with structured logging for better observability.
    """

    def on_success(self, retval: Any, task_id: str, args: tuple, kwargs: Dict[str, Any]) -> None:
        """Called when task succeeds."""
        logger.info(
            f"Task {self.name} completed successfully",
            extra={
                'task_id': task_id,
                'task_name': self.name,
                'status': 'success',
                'task_args': str(args),
                'result': str(retval) if retval else None
            }
        )
        super().on_success(retval, task_id, args, kwargs)

    def on_failure(self, exc: Exception, task_id: str, args: tuple, kwargs: Dict[str, Any], einfo: Any) -> None:
        """Called when task fails."""
        logger.error(
            f"Task {self.name} failed",
            exc_info=exc,
            extra={
                'task_id': task_id,
                'task_name': self.name,
                'status': 'failure',
                'exception_type': type(exc).__name__,
                'exception_message': str(exc)
            }
        )
        super().on_failure(exc, task_id, args, kwargs, einfo)

    def on_retry(self, exc: Exception, task_id: str, args: tuple, kwargs: Dict[str, Any], einfo: Any) -> None:
        """Called when task is retried."""
        logger.warning(
            f"Task {self.name} retrying",
            exc_info=exc,
            extra={
                'task_id': task_id,
                'task_name': self.name,
                'status': 'retry',
                'exception_type': type(exc).__name__,
                'exception_message': str(exc),
                'retry_count': getattr(self, 'request', {}).get('retries', 0)
            }
        )
        super().on_retry(exc, task_id, args, kwargs, einfo)

    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """Called when task is executed."""
        logger.info(
            f"Task {self.name} started",
            extra={
                'task_name': self.name,
                'status': 'started',
                'args_count': len(args),
                'kwargs_keys': list(kwargs.keys()) if kwargs else []
            }
        )
        return super().__call__(*args, **kwargs)