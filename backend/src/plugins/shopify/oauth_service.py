"""
Shopify OAuth2 service for ProductVideo platform.
Handles OAuth2 flow with proper scopes for video generation and media management.
"""

import hashlib
import hmac
import logging
import secrets
import urllib.parse
from datetime import datetime, timedelta
from typing import Dict, Optional

import httpx
from fastapi import HTT<PERSON>Exception
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.stores.models import Store

logger = logging.getLogger(__name__)

# Essential scopes for ProductVideo platform
REQUIRED_SCOPES = [
    "read_products",
    "write_products", 
    "read_product_listings",
    "write_product_listings",
    "read_files",
    "write_files",
    "read_orders",
    "read_customers",
    "read_analytics",
    "read_reports"
]


class ShopifyOAuthService:
    """
    Handles Shopify OAuth2 flow for ProductVideo platform.
    Requests essential scopes for video generation and media management.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_version = "2025-07"
        self.client_id = self.settings.SHOPIFY_API_KEY
        self.client_secret = self.settings.SHOPIFY_API_SECRET
        self.redirect_uri = f"{self.settings.FRONTEND_URL}/auth/callback/shopify"
        
        # Essential scopes for ProductVideo platform
        self.scopes = REQUIRED_SCOPES
        
        if not self.client_id or not self.client_secret:
            raise ValueError("Shopify API key and secret must be configured")
            
        logger.info(f"Initialized Shopify OAuth service with scopes: {', '.join(self.scopes)}")

    def generate_install_url(self, shop_domain: str, state: Optional[str] = None) -> str:
        """
        Generate Shopify app installation URL with proper OAuth2 parameters.
        
        Args:
            shop_domain: The shop domain (e.g., 'myshop.myshopify.com')
            state: Optional state parameter for CSRF protection
            
        Returns:
            Complete OAuth2 authorization URL
        """
        if not shop_domain.endswith('.myshopify.com'):
            shop_domain = f"{shop_domain}.myshopify.com"
            
        if not state:
            state = secrets.token_urlsafe(32)
            
        params = {
            'client_id': self.client_id,
            'scope': ','.join(self.scopes),
            'redirect_uri': self.redirect_uri,
            'state': state,
            'grant_options[]': 'per-user'  # Request per-user tokens
        }
        
        query_string = urllib.parse.urlencode(params)
        install_url = f"https://{shop_domain}/admin/oauth/authorize?{query_string}"
        
        logger.info(f"Generated install URL for shop: {shop_domain}")
        return install_url

    def verify_webhook_signature(self, data: bytes, signature: str) -> bool:
        """
        Verify Shopify webhook signature using HMAC-SHA256.
        
        Args:
            data: Raw webhook payload
            signature: X-Shopify-Hmac-Sha256 header value
            
        Returns:
            True if signature is valid, False otherwise
        """
        if not self.client_secret:
            logger.error("Client secret not configured for webhook verification")
            return False
            
        try:
            expected_signature = hmac.new(
                self.client_secret.encode('utf-8'),
                data,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False

    async def exchange_code_for_token(self, shop_domain: str, code: str, state: str) -> Dict:
        """
        Exchange authorization code for access token.
        
        Args:
            shop_domain: The shop domain
            code: Authorization code from Shopify
            state: State parameter for CSRF protection
            
        Returns:
            Dictionary containing access token and shop information
        """
        if not shop_domain.endswith('.myshopify.com'):
            shop_domain = f"{shop_domain}.myshopify.com"
            
        token_url = f"https://{shop_domain}/admin/oauth/access_token"
        
        payload = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code': code
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(token_url, json=payload)
                response.raise_for_status()
                
                token_data = response.json()
                logger.info(f"Successfully exchanged code for token for shop: {shop_domain}")
                
                return {
                    'access_token': token_data['access_token'],
                    'scope': token_data['scope'],
                    'shop_domain': shop_domain
                }
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error during token exchange: {e}")
            raise HTTPException(status_code=400, detail="Failed to exchange authorization code")
        except Exception as e:
            logger.error(f"Unexpected error during token exchange: {e}")
            raise HTTPException(status_code=500, detail="Internal server error during OAuth")

    async def get_shop_info(self, shop_domain: str, access_token: str) -> Dict:
        """
        Get shop information using access token.
        
        Args:
            shop_domain: The shop domain
            access_token: Shopify access token
            
        Returns:
            Dictionary containing shop information
        """
        if not shop_domain.endswith('.myshopify.com'):
            shop_domain = f"{shop_domain}.myshopify.com"
            
        shop_url = f"https://{shop_domain}/admin/api/{self.api_version}/shop.json"
        
        headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(shop_url, headers=headers)
                response.raise_for_status()
                
                shop_data = response.json()
                logger.info(f"Successfully retrieved shop info for: {shop_domain}")
                
                return shop_data['shop']
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error retrieving shop info: {e}")
            raise HTTPException(status_code=400, detail="Failed to retrieve shop information")
        except Exception as e:
            logger.error(f"Unexpected error retrieving shop info: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    async def save_store_credentials(
        self, 
        db: AsyncSession, 
        shop_info: Dict, 
        access_token: str, 
        user_id: int
    ) -> Store:
        """
        Save store credentials to database.
        
        Args:
            db: Database session
            shop_info: Shop information from Shopify
            access_token: Shopify access token
            user_id: User ID who owns this store
            
        Returns:
            Created or updated Store instance
        """
        # Check if store already exists
        from sqlalchemy import select
        result = await db.execute(
            select(Store).where(Store.shop_domain == shop_info['domain'])
        )
        existing_store = result.scalar_one_or_none()
        
        if existing_store:
            # Update existing store
            existing_store.admin_access_token = access_token
            existing_store.shop_name = shop_info['name']
            existing_store.shop_id = str(shop_info['id'])
            existing_store.is_active = True
            store = existing_store
        else:
            # Create new store
            store = Store(
                platform="shopify",
                admin_access_token=access_token,
                shop_domain=shop_info['domain'],
                shop_id=str(shop_info['id']),
                shop_name=shop_info['name'],
                owner_id=user_id,
                is_active=True
            )
            db.add(store)
        
        await db.commit()
        # Note: No refresh needed - use external_id for same-session operations
        
        logger.info(f"Saved store credentials for: {shop_info['domain']}")
        return store


# Create service instance
shopify_oauth_service = ShopifyOAuthService()
