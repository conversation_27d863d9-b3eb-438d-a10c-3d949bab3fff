"""
Shopify API Router for OAuth, integration management, and webhooks
"""

import hashlib
import hmac
import json
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.db.database import get_db
from modules.auth.router import get_current_user
from plugins.shopify.oauth_service import shopify_oauth_service
from modules.stores.service import store_service
from modules.auth.models import User
from modules.sync.models import WebhookEvent
from modules.stores.models import Store
from modules.queue.queue_service import celery_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/install")
async def get_install_url(
    shop: str = Query(..., description="Shop domain (e.g., 'myshop' or 'myshop.myshopify.com')"),
    current_user: User = Depends(get_current_user),
):
    """
    Generate Shopify app installation URL.
    
    Args:
        shop: Shop domain
        
    Returns:
        Installation URL and state for OAuth flow
    """
    try:
        install_url = shopify_oauth_service.generate_install_url(shop)
        return {
            "install_url": install_url,
            "message": "Visit the install_url to authorize the app"
        }
    except Exception as e:
        logger.error(f"Error generating install URL: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/oauth/callback")
async def oauth_callback(
    code: str = Query(..., description="Authorization code from Shopify"),
    shop: str = Query(..., description="Shop domain"),
    state: Optional[str] = Query(None, description="State parameter for CSRF protection"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Handle Shopify OAuth callback.
    
    Args:
        code: Authorization code from Shopify
        shop: Shop domain
        state: State parameter for CSRF protection
        
    Returns:
        Success message and store information
    """
    try:
        # Exchange code for access token
        token_data = await shopify_oauth_service.exchange_code_for_token(shop, code, state)
        
        # Get shop information
        shop_info = await shopify_oauth_service.get_shop_info(shop, token_data['access_token'])
        
        # Save store credentials
        store = await shopify_oauth_service.save_store_credentials(
            db, shop_info, token_data['access_token'], current_user.id
        )
        
        return {
            "message": "Shopify store connected successfully",
            "store": {
                "id": store.id,
                "shop_domain": store.shop_domain,
                "shop_name": store.shop_name
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in OAuth callback: {e}")
        raise HTTPException(status_code=500, detail="Failed to complete OAuth flow")




@router.get("/scopes")
async def get_required_scopes():
    """
    Get the required Shopify scopes for the application.

    Returns:
        List of required scopes
    """
    return {
        "scopes": shopify_oauth_service.scopes,
        "description": "Required scopes for ProductVideo platform"
    }


@router.post("/webhooks")
async def shopify_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Shopify webhooks for real-time data synchronization.

    This endpoint receives webhooks from Shopify, validates them,
    stores the event data, and triggers processing via Celery.

    Expected headers:
    - X-Shopify-Hmac-Sha256: HMAC signature for verification
    - X-Shopify-Shop-Domain: Shop domain sending the webhook
    - X-Shopify-Topic: Webhook topic (e.g., products/update)
    - X-Shopify-Event-Id: Unique event ID for deduplication
    """
    try:
        # Get raw body for signature verification
        body = await request.body()
        if not body:
            logger.warning("Received empty webhook body")
            return Response(status_code=400)

        # Extract required headers
        hmac_signature = request.headers.get("X-Shopify-Hmac-Sha256")
        shop_domain = request.headers.get("X-Shopify-Shop-Domain")
        topic = request.headers.get("X-Shopify-Topic")
        event_id = request.headers.get("X-Shopify-Event-Id")

        if not all([hmac_signature, shop_domain, topic, event_id]):
            logger.warning(f"Missing required webhook headers: hmac={bool(hmac_signature)}, shop={bool(shop_domain)}, topic={bool(topic)}, event_id={bool(event_id)}")
            return Response(status_code=400)

        # Verify webhook signature
        if not shopify_oauth_service.verify_webhook_signature(body, hmac_signature):
            logger.warning(f"Invalid webhook signature for shop {shop_domain}")
            return Response(status_code=401)

        # Parse webhook payload
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse webhook payload: {e}")
            return Response(status_code=400)

        # Find store by domain
        result = await db.execute(
            select(Store).where(Store.shop_domain == shop_domain)
        )
        store = result.scalar_one_or_none()

        if not store:
            logger.warning(f"Received webhook for unknown shop: {shop_domain}")
            return Response(status_code=404)

        # Check for duplicate events
        existing_event = await db.execute(
            select(WebhookEvent).where(WebhookEvent.event_id == event_id)
        )
        if existing_event.scalar_one_or_none():
            logger.info(f"Duplicate webhook event ignored: {event_id}")
            return Response(status_code=200)  # Shopify expects 200 for duplicates

        # Create webhook event record
        webhook_event = WebhookEvent(
            event_id=event_id,
            topic=topic,
            shop_domain=shop_domain,
            store_id=store.id,
            payload=payload,
            headers=dict(request.headers),
            hmac_verified=True,
            status='pending'
        )

        db.add(webhook_event)
        await db.commit()
        # Note: No refresh needed - use external_id for same-session operations

        # Trigger webhook processing via queue service
        celery_service.enqueue_webhook_processing(webhook_event.id)

        logger.info(f"Webhook received and queued for processing: {topic} from {shop_domain} (event: {event_id})")

        # Return 200 OK to acknowledge receipt
        return Response(status_code=200)

    except Exception as e:
        logger.error(f"Error processing Shopify webhook: {e}", exc_info=True)
        # Don't return error to Shopify as it may retry
        return Response(status_code=200)






