"""
Generic media service that routes to appropriate plugin based on store type.
"""

import importlib
import os
from typing import Dict, Any, Optional

from .interfaces import MediaServiceInterface


class PluginMediaService:
    """Generic media service that routes to appropriate plugin based on store type."""

    def __init__(self):
        self._services: Dict[str, MediaServiceInterface] = {}
        self._load_services()

    def _load_services(self):
        """Load available media services from plugins."""
        plugins_dir = os.path.dirname(__file__)

        for item in os.listdir(plugins_dir):
            plugin_path = os.path.join(plugins_dir, item)
            if os.path.isdir(plugin_path) and not item.startswith('__'):
                # Check if plugin has media service
                media_service_path = os.path.join(plugin_path, 'media_service.py')
                if os.path.exists(media_service_path):
                    try:
                        # Import the media service
                        module = importlib.import_module(f'plugins.{item}.media_service')

                        # Look for generic 'media_service'
                        if hasattr(module, 'media_service'):
                            service = getattr(module, 'media_service')
                            self._services[item] = service

                    except ImportError as e:
                        print(f"Could not load media service for plugin {item}: {e}")

    def get_service_for_store(self, store_type: str) -> Optional[MediaServiceInterface]:
        """Get the appropriate media service for a store type."""
        # First try to get the specific service for the store type
        service = self._services.get(store_type.lower())

        # If no specific service found, use the example plugin as default
        if not service and 'example' in self._services:
            service = self._services['example']

        return service

    async def push_media_to_product(
        self,
        store_type: str,
        shop_domain: str,
        product_id: str,
        media_url: str,
        alt_text: Optional[str] = None
    ) -> Dict[str, Any]:
        """Push media to product using the appropriate plugin service."""
        service = self.get_service_for_store(store_type)
        if not service:
            raise ValueError(f"No media service available for store type: {store_type}")

        return await service.push_media_to_product(
            shop_domain=shop_domain,
            product_id=product_id,
            media_url=media_url,
            alt_text=alt_text
        )


# Create global instance
media_plugin_manager = PluginMediaService()