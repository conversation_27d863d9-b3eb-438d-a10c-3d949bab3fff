"""
Example media service that logs pushes to a file instead of actually pushing.
"""

import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional

from ..interfaces import MediaServiceInterface
from core.config import get_settings


class ExampleMediaService(MediaServiceInterface):
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    async def push_media_to_product(
        self,
        shop_domain: str,
        product_id: str,
        media_url: str,
        alt_text: Optional[str] = None
    ) -> Dict[str, Any]:
        # Log the push to a file
        settings = get_settings()
        log_dir = settings.LOG_DIR
        log_path = os.path.join(log_dir, 'example_push.log')
        os.makedirs(log_dir, exist_ok=True)
        log_entry = f"Pushed media: {media_url} to product {product_id} on {shop_domain} at {datetime.now()}\n"
        with open(log_path, 'a') as f:
            f.write(log_entry)

        self.logger.info(f"Logged push for {media_url} to {shop_domain}: {log_path}")

        return {"success": True, "media_id": "fake_id_123"}


media_service = ExampleMediaService()