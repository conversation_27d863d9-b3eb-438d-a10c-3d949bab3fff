{"providers": {"banana": {"type": "image", "api_key": "${BANANA_API_KEY}", "timeout": 300, "model": "gemini-2.5-flash-image-preview", "capabilities": {"supported_formats": ["image"], "supported_styles": ["product_photography", "lifestyle", "minimalist", "luxury", "social_media"], "supported_categories": ["fashion_apparel", "footwear", "accessories", "beauty_cosmetics", "jewelry", "electronics", "home_decor"]}, "limits": {"max_variants_per_request": 4, "requests_per_minute": 10, "requests_per_hour": 100}, "costs": {"cost_per_unit": 0.04, "currency": "USD"}, "quality": {"quality_score": 0.95, "average_generation_time": 30}, "features": ["Professional product photography", "Lifestyle scene generation", "Brand-consistent styling", "Multi-variant generation", "Context-aware prompting", "E-commerce optimization", "High-quality AI generation"], "image_config": {"generation_params": {"quality": "high", "style": "photorealistic", "negative_prompt": "blurry, low quality, distorted, oversaturated, poor lighting, amateur", "guidance_scale": 7.5, "num_inference_steps": 20}}, "metadata": {"provider_name": "Google Gemini - Image Generation", "description": "Professional e-commerce image generation using Gemini 2.5 Flash Image Preview"}}, "veo3": {"type": "video", "api_key": "${VEO3_API_KEY}", "timeout": 600, "model": "veo-3.0-fast-generate-001", "capabilities": {"supported_formats": ["video"], "supported_aspect_ratios": ["16:9", "16:10"], "max_duration_seconds": 8, "supported_person_generation": ["ALLOW_ALL", "ALLOW_ADULT"]}, "limits": {"max_variants_per_request": 4, "requests_per_minute": 5, "requests_per_hour": 50}, "costs": {"cost_per_unit": 1.0, "currency": "USD"}, "quality": {"quality_score": 0.95, "average_generation_time": 180}, "features": ["Professional product videos", "Lifestyle video generation", "Social media optimized videos", "Brand-consistent styling", "Multi-variant generation", "High-quality AI generation"], "video_config": {"aspect_resolutions": {"1:1": "1080x1080", "9:16": "1080x1920", "16:9": "1920x1080", "4:5": "1080x1350"}, "generation_params": {"resolution": "1080p", "fps": 30, "duration_seconds": 8, "codec": "H.264", "bitrate": "high"}}, "metadata": {"provider_name": "Google Veo 3 - Video Generation", "description": "Professional e-commerce video generation using Google's Veo 3 AI"}}, "gemini": {"type": "text", "api_key": "${GEMINI_API_KEY}", "timeout": 120, "model": "gemini-2.5-flash", "capabilities": {"supported_formats": ["text"], "content_types": ["product_description", "marketing_copy", "social_caption", "seo_snippet"], "supported_languages": ["en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh", "ar", "hi"]}, "limits": {"max_variants_per_request": 4, "requests_per_minute": 20, "requests_per_hour": 500, "token_limits": {"product_description": 300, "marketing_copy": 400, "social_caption": 100, "seo_snippet": 200, "default": 300}}, "costs": {"cost_per_unit": 0.001, "currency": "USD"}, "quality": {"quality_score": 0.95, "average_generation_time": 10}, "features": ["Product descriptions", "Marketing copy", "Social media captions", "SEO content", "Multi-language support", "Brand-consistent content"], "text_config": {"temperature_settings": {"social_caption": 0.7, "default": 0.3}, "generation_params": {"temperature": 0.7, "top_p": 0.9, "top_k": 50, "max_tokens": 1000, "presence_penalty": 0.0, "frequency_penalty": 0.0, "stop_sequences": ["\\n\\n", "###"]}, "estimated_completion_time_per_variant": 10}, "metadata": {"provider_name": "Google Gemini AI - Text Generation", "description": "Professional text generation and content creation using Google's Gemini AI"}}, "example_image": {"type": "image", "api_key": "", "timeout": 30, "model": "example_model", "capabilities": {"supported_formats": ["image"], "supported_styles": ["example_style", "test_style"], "supported_categories": ["test", "demo"]}, "limits": {"max_variants_per_request": 2, "requests_per_minute": 100, "requests_per_hour": 1000}, "costs": {"cost_per_unit": 0.0, "currency": "USD"}, "quality": {"quality_score": 0.9, "average_generation_time": 5}, "features": ["Example image generation", "Test data provider", "No API key required", "Fast response times"], "image_config": {"generation_params": {"quality": "high", "style": "example", "negative_prompt": "", "guidance_scale": 1.0, "num_inference_steps": 1}}, "metadata": {"provider_name": "Example Image Provider", "description": "Example provider for testing and demonstration purposes"}}, "example_text": {"type": "text", "api_key": "", "timeout": 30, "model": "example_model", "capabilities": {"supported_formats": ["text"], "content_types": ["product_description", "marketing_copy", "social_caption", "seo_snippet"], "supported_languages": ["en"]}, "limits": {"max_variants_per_request": 4, "requests_per_minute": 100, "requests_per_hour": 1000, "token_limits": {"product_description": 300, "marketing_copy": 400, "social_caption": 100, "seo_snippet": 200, "default": 300}}, "costs": {"cost_per_unit": 0.0, "currency": "USD"}, "quality": {"quality_score": 0.95, "average_generation_time": 2}, "features": ["Example text generation", "Test data provider", "No API key required", "Fast response times"], "text_config": {"temperature_settings": {"default": 0.7}, "generation_params": {"temperature": 0.7, "top_p": 0.9, "top_k": 50, "max_tokens": 1000, "presence_penalty": 0.0, "frequency_penalty": 0.0, "stop_sequences": []}, "estimated_completion_time_per_variant": 2}, "metadata": {"provider_name": "Example Text Provider", "description": "Example provider for testing and demonstration purposes"}}, "example_video": {"type": "video", "api_key": "", "timeout": 60, "model": "example_model", "capabilities": {"supported_formats": ["video"], "supported_aspect_ratios": ["16:9", "1:1", "9:16"], "max_duration_seconds": 10, "supported_person_generation": []}, "limits": {"max_variants_per_request": 1, "requests_per_minute": 50, "requests_per_hour": 500}, "costs": {"cost_per_unit": 0.0, "currency": "USD"}, "quality": {"quality_score": 0.88, "average_generation_time": 10}, "features": ["Example video generation", "Test data provider", "No API key required", "Fast response times"], "video_config": {"aspect_resolutions": {"1:1": "1024x1024", "9:16": "1080x1920", "16:9": "1920x1080"}, "generation_params": {"resolution": "1080p", "fps": 30, "duration_seconds": 8, "codec": "H.264", "bitrate": "high"}}, "metadata": {"provider_name": "Example Video Provider", "description": "Example provider for testing and demonstration purposes"}}}, "defaults": {"image": "banana", "video": "veo3", "text": "gemini"}, "fallbacks": {"image": ["banana", "example_image"], "video": ["veo3", "example_video"], "text": ["gemini", "example_text"]}}