import json
import logging
import sys
from pathlib import Path

import colorlog
from pythonjsonlogger import jsonlogger

from core.config import get_settings


class PrettyJ<PERSON>NFormatter(colorlog.ColoredFormatter):
    """Custom formatter that pretty-prints JSON messages in console with colors, including extra fields."""

    def format(self, record):
        # Create a dict with message and extra fields for structured logging
        try:
            json_data = json.loads(record.getMessage())

            # Add extra fields (exclude standard logging record attributes)
            standard_attrs = {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
                'module', 'exc_info', 'exc_text', 'stack_info', 'lineno', 'funcName',
                'created', 'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'message'
            }

            for key, value in record.__dict__.items():
                if key not in standard_attrs:
                    json_data[key] = value


            pretty_json = json.dumps(json_data, indent=2, sort_keys=True, default=str)
            # Replace the message with pretty JSON
            record.msg = '\n' + pretty_json
            record.args = ()  # Clear args since we replaced msg
        except json.JSONDecodeError:
            # If not JSON, just pass through
            pass

        # Now format with colors
        return super().format(record)


def setup_logging(log_filename: str = "app.log"):
    """
    Sets up dual logging: JSON for files and colorful pretty-printed for console.
    This overrides Celery's default logging configuration.
    """
    settings = get_settings()

    # Get the root logger and configure it
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))

    # Clear ALL existing handlers from root logger
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Also clear handlers from specific loggers that might conflict
    celery_logger = logging.getLogger('celery')
    for handler in celery_logger.handlers[:]:
        celery_logger.removeHandler(handler)

    worker_logger = logging.getLogger('celery.worker')
    for handler in worker_logger.handlers[:]:
        worker_logger.removeHandler(handler)

    # Create logs directory if it doesn't exist
    logs_dir = Path(settings.LOG_DIR)
    logs_dir.mkdir(exist_ok=True)

    # File handler - always JSON format with size-based rotation
    from logging.handlers import RotatingFileHandler

    file_handler = RotatingFileHandler(
        logs_dir / log_filename,
        maxBytes=settings.LOG_MAX_BYTES,      # Configurable max bytes per file
        backupCount=settings.LOG_BACKUP_COUNT  # Configurable number of backup files
    )
    file_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    json_formatter = jsonlogger.JsonFormatter(
        "%(asctime)s %(name)s %(levelname)s %(pathname)s %(lineno)d %(process)d %(processName)s %(message)s"
    )
    file_handler.setFormatter(json_formatter)
    root_logger.addHandler(file_handler)

    # Console handler - colorful with pretty JSON printing
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))

    # Colorful formatter with pretty JSON
    console_formatter = PrettyJSONFormatter(
        "%(asctime)s - %(log_color)s%(levelname)s%(reset)s - %(name)s - %(pathname)s:%(lineno)d - %(message)s",
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # Ensure Celery uses our logging configuration
    celery_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    celery_logger.propagate = True  # Let messages propagate to root logger

    worker_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    worker_logger.propagate = True

    # Set specific loggers to appropriate levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("celery.app.trace").setLevel(logging.WARNING)

    # Reduce verbosity of Celery task success logs to prevent duplication with LoggedTask
    logging.getLogger("celery.worker.strategy").setLevel(logging.WARNING)
    logging.getLogger("celery.worker.consumer").setLevel(logging.WARNING)

    # Log that structured logging has been set up
    root_logger.info("Dual logging initialized: JSON to file, colorful console output", extra={
        'log_level': settings.LOG_LEVEL,
        'file_handler': str(logs_dir / "app.log"),
        'console_format': 'colorful with pretty JSON',
        'rotation': 'size-based (10MB, 5 backups)',
        'startup_time': 'now'
    })

    # Add shutdown handler to log when application stops
    import atexit
    def log_shutdown():
        root_logger.info("Application shutting down", extra={
            'shutdown_time': 'now',
            'log_file': str(logs_dir / "app.log")
        })

    atexit.register(log_shutdown)
