"""
Simple and robust database connection management.
Follows SQLAlchemy best practices for production applications.
"""

from typing import AsyncGenerator, Optional
import logging
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from core.config import get_settings

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Global variables for lazy initialization
_async_engine: Optional[create_async_engine] = None
_async_session_factory: Optional[async_sessionmaker] = None
_async_session_factory_fresh: Optional[async_sessionmaker] = None

def get_async_engine():
    """
    Get or create the async engine lazily.
    This ensures each process creates its own engine instance.
    """
    global _async_engine
    if _async_engine is None:
        logger.info("Creating async engine for process")

        # Check if we're in a Celery worker process
        import os
        is_worker = (
            'celery' in ' '.join(os.sys.argv).lower() or
            'ForkPoolWorker' in str(os.environ.get('CELERY_WORKER_TYPE', '')) or
            os.environ.get('CELERY_WORKER_PID') is not None or
            'worker' in os.environ.get('CELERY_ROLE', '').lower()
        )

        if is_worker:
            # Use NullPool for worker processes to avoid connection sharing issues
            from sqlalchemy.pool import NullPool
            logger.info("Using NullPool for Celery worker process to prevent event loop conflicts")
            pool_class = NullPool
            # NullPool doesn't accept pool_size or max_overflow parameters
            engine_kwargs = {
                "poolclass": pool_class,
                "pool_pre_ping": True,  # Test connections before use
                "pool_recycle": 300,  # Recycle connections every 5 minutes
            }
        else:
            # Use NullPool for API as well to prevent event loop conflicts
            # This ensures each connection is independent and avoids sharing issues
            from sqlalchemy.pool import NullPool
            logger.info("Using NullPool for API process to prevent event loop conflicts")
            pool_class = NullPool
            engine_kwargs = {
                "poolclass": pool_class,
                "pool_pre_ping": True,  # Test connections before use
                "pool_recycle": 300,  # Recycle connections every 5 minutes
            }

        # Add connect_args for asyncpg
        if "asyncpg" in settings.DATABASE_URL:
            engine_kwargs["connect_args"] = {
                "server_settings": {
                    "application_name": "ecommerce_app",
                    "statement_timeout": "30000",  # 30 seconds
                    "idle_in_transaction_session_timeout": "30000",  # 30 seconds
                }
            }

        _async_engine = create_async_engine(
            settings.DATABASE_URL,
            echo=False,  # Set to True for development debugging
            **engine_kwargs
        )
    return _async_engine

def get_async_session_factory():
    """
    Get or create the async session factory lazily.
    """
    global _async_session_factory
    if _async_session_factory is None:
        logger.info("Creating async session factory for process")
        _async_session_factory = async_sessionmaker(
            bind=get_async_engine(),
            expire_on_commit=False,
            autoflush=False,
            autocommit=False,
            class_=AsyncSession,
        )
    return _async_session_factory

def get_async_session_factory_fresh():
    """
    Get or create the fresh async session factory lazily.
    """
    global _async_session_factory_fresh
    if _async_session_factory_fresh is None:
        logger.info("Creating fresh async session factory for process")
        _async_session_factory_fresh = async_sessionmaker(
            bind=get_async_engine(),
            expire_on_commit=True,  # Expire all objects after commit for fresh data
            autoflush=False,
            autocommit=False,
            class_=AsyncSession,
        )
    return _async_session_factory_fresh

# For backward compatibility, expose the functions as if they were the factories
async_session_factory = get_async_session_factory
async_session_factory_fresh = get_async_session_factory_fresh

# All Celery workers now use async database operations
# Sync database functions removed as they're no longer needed


# Main database dependency - always fresh data (no caching)
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency to get a fresh database session.
    Always expires cached objects to ensure real-time data.
    Automatically handles session lifecycle.
    """
    session_factory = get_async_session_factory()
    async with session_factory() as session:
        # Always expire cached objects for real-time data consistency
        # This prevents stale data issues in concurrent environments
        session.expire_all()
        try:
            yield session
        finally:
            pass  # Session is automatically closed by context manager


# Utility functions for services that need direct session access
def get_session_factory():
    """Get the async session factory for manual session creation."""
    return get_async_session_factory()


def get_fresh_session_factory():
    """Get the async session factory with automatic expiration for fresh data."""
    return get_async_session_factory_fresh()


# Fresh database dependency - maximum freshness (expires after each commit)
async def get_fresh_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency to get a database session with maximum freshness.
    All objects expire after each commit, ensuring no stale data.
    Use for critical real-time operations.
    """
    session_factory = get_async_session_factory_fresh()
    async with session_factory() as session:
        try:
            yield session
        finally:
            pass  # Session is automatically closed by context manager


# Import Base after engine creation to avoid circular imports
from core.db.base_declarative import Base


# Query utilities for manual freshness control
# DEPRECATED: These refresh utilities are no longer needed with external_id pattern
# Use external_id for same-session operations instead of refreshing objects

async def refresh_object(session: AsyncSession, obj):
    """
    DEPRECATED: Manually refresh a specific object with fresh data from database.
    Use external_id for same-session operations instead.
    """
    await session.refresh(obj)
    return obj


async def refresh_objects(session: AsyncSession, objects):
    """
    DEPRECATED: Refresh multiple objects with fresh data from database.
    Use external_id for same-session operations instead.
    """
    for obj in objects:
        await session.refresh(obj)
    return objects