"""
Products API Router
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.products.models import Product, ProductVariant, ProductImage
from modules.products.schemas import (
    ProductCreate,
    ProductListResponse,
    ProductResponse,
    ProductUpdate,
    PaginatedProductResponse,
    ProductVariantResponse,
    ProductImageResponse
)
from modules.products.service import product_service
from modules.assets.service import asset_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=PaginatedProductResponse)
async def get_products(
    page: int = 1,
    limit: int = 50,
    search: str = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all products for the current user's stores."""
    try:
        logger.info(f"Products API called with page={page}, limit={limit}" + (f", search='{search}'" if search else ""))

        # Get user's stores
        from modules.stores.models import Store
        from sqlalchemy import select

        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == current_user.id)
        )
        user_stores = stores_result.scalars().all()

        logger.info(f"User {current_user.id} has {len(user_stores)} stores")
        for store in user_stores:
            logger.info(f"Store: {store.id} - {store.shop_name or 'Unnamed Store'}")

        if not user_stores:
            logger.info("No stores found for user")
            return PaginatedProductResponse(
                items=[],
                total=0,
                page=page,
                limit=limit,
                total_pages=0
            )

        store_ids = [store.id for store in user_stores]

        # Get ALL products for all user's stores with their relationships loaded
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select, or_, and_

        all_products = []
        for store_id in store_ids:
            # Build the base query
            query = select(Product).options(
                selectinload(Product.variants),
                selectinload(Product.images)
            ).filter(Product.store_id == store_id)

            # Add search filter if search query is provided
            if search:
                search_filter = f"%{search.lower()}%"
                query = query.filter(
                    or_(
                        Product.title.ilike(search_filter),
                        Product.description.ilike(search_filter),
                        Product.vendor.ilike(search_filter),
                        Product.product_type.ilike(search_filter),
                        Product.tags.ilike(search_filter)
                    )
                )
                logger.debug(f"Applying search filter: {search}")

            query = query.order_by(Product.updated_at.desc())

            # Execute the query
            products_result = await db.execute(query)
            store_products = products_result.scalars().all()

            logger.debug(f"Store {store_id} has {len(store_products)} products" + (f" matching '{search}'" if search else ""))

            # Convert to dict and add computed fields
            for product in store_products:
                # Convert variants to schema instances
                variants_list = []
                if product.variants:
                    for variant in product.variants:
                        variant_response = ProductVariantResponse(
                            id=variant.id,
                            external_id=variant.external_id,
                            product_id=variant.product_id,
                            title=variant.title,
                            sku=variant.sku,
                            barcode=variant.barcode,
                            price=variant.price,
                            compare_at_price=variant.compare_at_price,
                            cost=variant.cost,
                            weight=variant.weight,
                            weight_unit=variant.weight_unit,
                            quantity=variant.quantity,
                            inventory_policy=variant.inventory_policy,
                            inventory_item_id=variant.inventory_item_id,
                            option1=variant.option1,
                            option2=variant.option2,
                            option3=variant.option3,
                            taxable=variant.taxable,
                            requires_shipping=variant.requires_shipping,
                            fulfillment_service=variant.fulfillment_service,
                            available_for_sale=variant.available_for_sale,
                            full_json=variant.full_json,
                            metafields=variant.metafields,
                            created_at=variant.created_at,
                            updated_at=variant.updated_at,
                            source_updated_at=variant.source_updated_at
                        )
                        variants_list.append(variant_response)
        
                # Convert images to schema instances
                images_list = []
                if product.images:
                    for image in product.images:
                        image_response = ProductImageResponse(
                            id=image.id,
                            external_id=image.external_id,
                            product_id=image.product_id,
                            variant_id=image.variant_id,
                            src=image.src,
                            alt=image.alt,
                            width=image.width,
                            height=image.height,
                            position=image.position,
                            full_json=image.full_json,
                            metafields=image.metafields,
                            created_at=image.created_at,
                            updated_at=image.updated_at,
                            source_updated_at=image.source_updated_at
                        )
                        images_list.append(image_response)

                # Create ProductListResponse instance
                product_response = ProductListResponse(
                    id=product.id,
                    external_id=product.external_id,
                    title=product.title,
                    handle=product.handle,
                    vendor=product.vendor,
                    product_type=product.product_type,
                    status=product.status,
                    published=product.published,
                    description=product.description,
                    tags=product.tags,
                    options=product.options,
                    seo=product.seo,
                    metafields=product.metafields,
                    collections=product.collections,
                    full_json=product.full_json,
                    featured_media=product.featured_media,
                    store_id=product.store_id,
                    created_at=product.created_at,
                    updated_at=product.updated_at,
                    published_at=product.published_at,
                    source_updated_at=product.source_updated_at,
                    variant_count=len(variants_list),
                    image_count=len(images_list),
                    variants=variants_list,
                    images=images_list,
                    assets=await asset_service.get_all_media_for_product(product)
                )

                # Add to products list
                all_products.append(product_response)

                # Log if no images found (for debugging)
                if product_response.image_count == 0:
                    logger.debug(f"Product {product.title}: no images found")
                if not product_response.assets:
                    logger.debug(f"Product {product.title}: no assets found")

        # Calculate total count
        total_products = len(all_products)
        logger.info(f"Total products found: {total_products}")

        # Apply pagination to the combined results
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_products = all_products[start_idx:end_idx]

        # Calculate total pages
        total_pages = (total_products + limit - 1) // limit  # Ceiling division

        logger.info(f"Returning page {page} with {len(paginated_products)} products out of {total_products} total")

        response = PaginatedProductResponse(
            items=paginated_products,
            total=total_products,
            page=page,
            limit=limit,
            total_pages=total_pages
        )

        logger.info(f"Response total field: {response.total}")
        return response

    except Exception as e:
        logger.error(f"Error getting products: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ProductResponse)
async def create_product(
    product: ProductCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new product."""
    try:
        # Validate store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        db_product = await product_service.create_product_with_variants(db, product)

        # Load relationships for the response
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select

        result = await db.execute(
            select(Product)
            .options(
                selectinload(Product.variants),
                selectinload(Product.images)
            )
            .filter(Product.id == db_product.id)
        )
        db_product = result.scalar_one()

        # Convert variants to schema instances
        variants_list = []
        if db_product.variants:
            for variant in db_product.variants:
                variant_response = ProductVariantResponse(
                    id=variant.id,
                    external_id=variant.external_id,
                    product_id=variant.product_id,
                    title=variant.title,
                    sku=variant.sku,
                    barcode=variant.barcode,
                    price=variant.price,
                    compare_at_price=variant.compare_at_price,
                    cost=variant.cost,
                    weight=variant.weight,
                    weight_unit=variant.weight_unit,
                    quantity=variant.quantity,
                    inventory_policy=variant.inventory_policy,
                    inventory_item_id=variant.inventory_item_id,
                    option1=variant.option1,
                    option2=variant.option2,
                    option3=variant.option3,
                    taxable=variant.taxable,
                    requires_shipping=variant.requires_shipping,
                    fulfillment_service=variant.fulfillment_service,
                    available_for_sale=variant.available_for_sale,
                    full_json=variant.full_json,
                    metafields=variant.metafields,
                    created_at=variant.created_at,
                    updated_at=variant.updated_at,
                    source_updated_at=variant.source_updated_at
                )
                variants_list.append(variant_response)

        # Convert images to schema instances
        images_list = []
        if db_product.images:
            for image in db_product.images:
                image_response = ProductImageResponse(
                    id=image.id,
                    external_id=image.external_id,
                    product_id=image.product_id,
                    variant_id=image.variant_id,
                    src=image.src,
                    alt=image.alt,
                    width=image.width,
                    height=image.height,
                    position=image.position,
                    full_json=image.full_json,
                    metafields=image.metafields,
                    created_at=image.created_at,
                    updated_at=image.updated_at,
                    source_updated_at=image.source_updated_at
                )
                images_list.append(image_response)

        # Create ProductResponse instance
        product_response = ProductResponse(
            id=db_product.id,
            external_id=db_product.external_id,
            title=db_product.title,
            handle=db_product.handle,
            vendor=db_product.vendor,
            product_type=db_product.product_type,
            status=db_product.status,
            published=db_product.published,
            description=db_product.description,
            tags=db_product.tags,
            options=db_product.options,
            seo=db_product.seo,
            metafields=db_product.metafields,
            collections=db_product.collections,
            full_json=db_product.full_json,
            featured_media=db_product.featured_media,
            store_id=db_product.store_id,
            created_at=db_product.created_at,
            updated_at=db_product.updated_at,
            published_at=db_product.published_at,
            source_updated_at=db_product.source_updated_at,
            variants=variants_list,
            images=images_list,
            assets=await asset_service.get_all_media_for_product(db_product)
        )

        return product_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific product."""
    try:
        logger.info(f"Getting product {product_id} for user {current_user.id}")

        product = await product_service.get_product_with_full_details_by_external_id(db, product_id)

        if not product:
            logger.warning(f"Product {product_id} not found")
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            logger.warning(f"User {current_user.id} does not have access to product {product_id} (store {product.store_id})")
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        # Debug metafields
        logger.info(f"Product {product_id} metafields: {product.metafields}")
        logger.info(f"Product {product_id} metafields type: {type(product.metafields)}")

        # Convert variants to schema instances
        variants_list = []
        if product.variants:
            for variant in product.variants:
                variant_response = ProductVariantResponse(
                    id=variant.id,
                    external_id=variant.external_id,
                    product_id=variant.product_id,
                    title=variant.title,
                    sku=variant.sku,
                    barcode=variant.barcode,
                    price=variant.price,
                    compare_at_price=variant.compare_at_price,
                    cost=variant.cost,
                    weight=variant.weight,
                    weight_unit=variant.weight_unit,
                    quantity=variant.quantity,
                    inventory_policy=variant.inventory_policy,
                    inventory_item_id=variant.inventory_item_id,
                    option1=variant.option1,
                    option2=variant.option2,
                    option3=variant.option3,
                    taxable=variant.taxable,
                    requires_shipping=variant.requires_shipping,
                    fulfillment_service=variant.fulfillment_service,
                    available_for_sale=variant.available_for_sale,
                    full_json=variant.full_json,
                    metafields=variant.metafields,
                    created_at=variant.created_at,
                    updated_at=variant.updated_at,
                    source_updated_at=variant.source_updated_at
                )
                variants_list.append(variant_response)

        # Convert images to schema instances
        images_list = []
        if product.images:
            for image in product.images:
                image_response = ProductImageResponse(
                    id=image.id,
                    external_id=image.external_id,
                    product_id=image.product_id,
                    variant_id=image.variant_id,
                    src=image.src,
                    alt=image.alt,
                    width=image.width,
                    height=image.height,
                    position=image.position,
                    full_json=image.full_json,
                    metafields=image.metafields,
                    created_at=image.created_at,
                    updated_at=image.updated_at,
                    source_updated_at=image.source_updated_at
                )
                images_list.append(image_response)

        # Create ProductResponse instance
        product_response = ProductResponse(
            id=product.id,
            external_id=product.external_id,
            title=product.title,
            handle=product.handle,
            vendor=product.vendor,
            product_type=product.product_type,
            status=product.status,
            published=product.published,
            description=product.description,
            tags=product.tags,
            options=product.options,
            seo=product.seo,
            metafields=product.metafields,
            collections=product.collections,
            full_json=product.full_json,
            featured_media=product.featured_media,
            store_id=product.store_id,
            created_at=product.created_at,
            updated_at=product.updated_at,
            published_at=product.published_at,
            source_updated_at=product.source_updated_at,
            variants=variants_list,
            images=images_list,
            assets=await asset_service.get_all_media_for_product(product)
        )

        return product_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: str,
    product_update: ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a product."""
    try:
        # Get existing product
        product = await product_service.get_by_external_id(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        updated_product = await product_service.update(db, db_obj=product, obj_in=product_update)

        # Load relationships for the response
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select

        result = await db.execute(
            select(Product)
            .options(
                selectinload(Product.variants),
                selectinload(Product.images)
            )
            .filter(Product.id == updated_product.id)
        )
        updated_product = result.scalar_one()

        # Convert variants to schema instances
        variants_list = []
        if updated_product.variants:
            for variant in updated_product.variants:
                variant_response = ProductVariantResponse(
                    id=variant.id,
                    external_id=variant.external_id,
                    product_id=variant.product_id,
                    title=variant.title,
                    sku=variant.sku,
                    barcode=variant.barcode,
                    price=variant.price,
                    compare_at_price=variant.compare_at_price,
                    cost=variant.cost,
                    weight=variant.weight,
                    weight_unit=variant.weight_unit,
                    quantity=variant.quantity,
                    inventory_policy=variant.inventory_policy,
                    inventory_item_id=variant.inventory_item_id,
                    option1=variant.option1,
                    option2=variant.option2,
                    option3=variant.option3,
                    taxable=variant.taxable,
                    requires_shipping=variant.requires_shipping,
                    fulfillment_service=variant.fulfillment_service,
                    available_for_sale=variant.available_for_sale,
                    full_json=variant.full_json,
                    metafields=variant.metafields,
                    created_at=variant.created_at,
                    updated_at=variant.updated_at,
                    source_updated_at=variant.source_updated_at
                )
                variants_list.append(variant_response)

        # Convert images to schema instances
        images_list = []
        if updated_product.images:
            for image in updated_product.images:
                image_response = ProductImageResponse(
                    id=image.id,
                    external_id=image.external_id,
                    product_id=image.product_id,
                    variant_id=image.variant_id,
                    src=image.src,
                    alt=image.alt,
                    width=image.width,
                    height=image.height,
                    position=image.position,
                    full_json=image.full_json,
                    metafields=image.metafields,
                    created_at=image.created_at,
                    updated_at=image.updated_at,
                    source_updated_at=image.source_updated_at
                )
                images_list.append(image_response)

        # Create ProductResponse instance
        product_response = ProductResponse(
            id=updated_product.id,
            external_id=updated_product.external_id,
            title=updated_product.title,
            handle=updated_product.handle,
            vendor=updated_product.vendor,
            product_type=updated_product.product_type,
            status=updated_product.status,
            published=updated_product.published,
            description=updated_product.description,
            tags=updated_product.tags,
            options=updated_product.options,
            seo=updated_product.seo,
            metafields=updated_product.metafields,
            collections=updated_product.collections,
            full_json=updated_product.full_json,
            featured_media=updated_product.featured_media,
            store_id=updated_product.store_id,
            created_at=updated_product.created_at,
            updated_at=updated_product.updated_at,
            published_at=updated_product.published_at,
            source_updated_at=updated_product.source_updated_at,
            variants=variants_list,
            images=images_list,
            assets=await asset_service.get_all_media_for_product(updated_product)
        )

        return product_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}")
async def delete_product(
    product_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a product."""
    try:
        # Get existing product
        product = await product_service.get_by_external_id(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        await product_service.remove(db, id=product_id)
        return {"message": "Product deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))

