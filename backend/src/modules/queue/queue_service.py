"""
Celery integration for ProductVideo platform.
Replaces BullMQ with Celery for distributed task processing.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

from celery import Celery
from celery.result import AsyncResult

from core.config import get_settings
from core.metrics import (
    celery_active_tasks, celery_scheduled_tasks, celery_worker_status,
    celery_queue_length, celery_worker_pool_size, celery_task_rate
)

logger = logging.getLogger(__name__)
settings = get_settings()

# Create Celery app instance
celery_app = Celery(
    'productvideo_worker',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['servers.worker.tasks']
)


class TaskPriority(Enum):
    """Task priority levels for Celery."""
    LOW = 1
    NORMAL = 5
    HIGH = 10
    URGENT = 15


class CeleryService:
    """
    Celery service for managing media generation tasks.
    Provides a clean interface over Celery for our specific use cases.
    """

    def __init__(self):
        self.redis_url = settings.REDIS_URL
        logger.info("Initialized Celery service")
    
    def enqueue_media_generation(
        self,
        user_id: int,
        job_id: str,  # Changed from int to str to support external_id
        product_ids: List[str],
        media_type: str,
        template_id: Optional[str] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        delay_seconds: int = 0,
        **kwargs
    ) -> str:
        """
        Enqueue a media generation task.

        Args:
            user_id: User ID
            job_id: Database job ID
            product_ids: List of product IDs to generate media for
            media_type: Type of media to generate ('video', 'image')
            template_id: Template to use (for video/image)
            priority: Task priority
            delay_seconds: Delay before processing (seconds)
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks.media_tasks import generate_media

        # Get full payload from kwargs if provided
        full_payload = kwargs.get("full_payload")

        task_data = {
            "user_id": user_id,
            "job_id": job_id,
            "product_ids": product_ids,
            "media_type": media_type,
            "template_id": template_id,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }

        # Include full payload if provided
        if full_payload:
            task_data["full_payload"] = full_payload

        # Convert priority to Celery priority (1-10, higher is more important)
        celery_priority = priority.value

        result = generate_media.apply_async(
            args=[task_data],
            priority=celery_priority,
            countdown=delay_seconds if delay_seconds > 0 else None,
            queue='media-generation'
        )

        # Record task rate metric
        celery_task_rate.labels(
            queue="media-generation",
            task_name="generate_media",
            status="enqueued"
        ).inc()

        logger.info(f"Enqueued {media_type} generation task {result.id} for user {user_id}")
        return result.id
    
    def enqueue_media_push(
        self,
        user_id: int,
        variant_external_id: str,
        product_id: str,
        store_id: int,
        priority: TaskPriority = TaskPriority.HIGH,
        **kwargs
    ) -> str:
        """
        Enqueue a media push to Store task.

        Args:
            user_id: User ID
            variant_external_id: Media variant external ID (UUID string)
            product_id: Store product ID
            store_id: Store ID
            priority: Task priority
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks.media_tasks import push_to_platform

        celery_priority = priority.value

        # Prepare task data as dictionary for better parameter clarity
        task_data = {
            "variant_external_id": variant_external_id,  # Use external_id instead of database ID
            "store_id": store_id,
            "user_id": user_id,
            "product_id": product_id,
            **kwargs
        }

        result = push_to_platform.apply_async(
            args=[task_data],
            priority=celery_priority,
            queue='media-push'
        )

        # Record task rate metric
        celery_task_rate.labels(
            queue="media-push",
            task_name="push_to_platform",
            status="enqueued"
        ).inc()

        logger.info(f"Enqueued media push task {result.id} for variant {variant_external_id}")
        return result.id
    
    def enqueue_analytics_processing(
        self,
        user_id: int,
        event_data: Dict[str, Any],
        priority: TaskPriority = TaskPriority.LOW,
        **kwargs
    ) -> str:
        """
        Enqueue analytics event processing.

        Args:
            user_id: User ID
            event_data: Analytics event data
            priority: Task priority
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks import process_analytics

        task_data = {
            "user_id": user_id,
            "event_data": event_data,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }

        celery_priority = priority.value

        result = process_analytics.apply_async(
            args=[task_data],
            priority=celery_priority
        )

        # Record task rate metric
        celery_task_rate.labels(
            queue="analytics",
            task_name="process_analytics",
            status="enqueued"
        ).inc()

        logger.info(f"Enqueued analytics processing task {result.id}")
        return result.id

    def enqueue_webhook_processing(
        self,
        webhook_event_id: int,
        priority: TaskPriority = TaskPriority.HIGH,
        **kwargs
    ) -> str:
        """
        Enqueue a webhook processing task using Celery's send_task to avoid direct imports.

        Args:
            webhook_event_id: Webhook event ID from database
            priority: Task priority
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from celery import Celery

        task_data = {
            "webhook_event_id": webhook_event_id,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }

        celery_priority = priority.value

        # Use send_task to avoid direct imports of worker tasks
        result = celery_app.send_task(
            name='webhook.process_webhook_event',
            args=[webhook_event_id],
            priority=celery_priority,
            queue='webhook-processing'
        )

        # Record task rate metric
        celery_task_rate.labels(
            queue="webhook-processing",
            task_name="process_webhook_event",
            status="enqueued"
        ).inc()

        logger.info(f"Enqueued webhook processing task {result.id} for event {webhook_event_id}")
        return result.id

    def enqueue_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
        max_attempts: int = 3,
        timeout_seconds: int = 300,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Enqueue a generic job with specified type and payload.

        Args:
            job_type: Type of job to enqueue
            payload: Job payload data
            priority: Task priority
            max_attempts: Maximum number of retry attempts
            timeout_seconds: Job timeout in seconds
            metadata: Additional metadata for the job

        Returns:
            Celery task ID
        """
        celery_priority = priority.value

        if job_type == "media_push":
            # Use the existing push_media_to_platform task
            from servers.worker.tasks.media_tasks import push_media_to_platform

            # Extract required parameters from payload
            store_id = payload.get("store_id")
            variant_external_id = payload.get("variant_external_id") or payload.get("variant_id")  # Support both for backward compatibility

            if not store_id or not variant_external_id:
                raise ValueError("store_id and variant_external_id are required for media_push jobs")

            result = push_media_to_platform.apply_async(
                args=[variant_external_id, store_id],
                priority=celery_priority,
                queue='media-push'
            )

            task_name = "push_media_to_platform"
        else:
            # For other job types, create a generic task structure
            from servers.worker.tasks.media_tasks import generate_media

            task_data = {
                "job_type": job_type,
                "payload": payload,
                "created_at": datetime.utcnow().isoformat(),
                **(metadata or {})
            }

            result = generate_media.apply_async(
                args=[task_data],
                priority=celery_priority,
                countdown=0
            )

            task_name = job_type

        # Record task rate metric
        celery_task_rate.labels(
            queue="generic",
            task_name=task_name,
            status="enqueued"
        ).inc()

        logger.info(f"Enqueued {job_type} job {result.id}")
        return result.id


    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task status and details.

        Args:
            task_id: Celery task ID

        Returns:
            Task status information
        """
        result = AsyncResult(task_id, app=celery_app)

        if not result:
            return None

        return {
            "id": result.id,
            "state": result.state,
            "info": result.info,
            "result": result.result if result.state == "SUCCESS" else None,
            "traceback": result.traceback if result.state == "FAILURE" else None,
        }

    def get_queue_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive queue statistics similar to Flower monitoring.

        Returns detailed information about:
        - Active tasks per queue
        - Scheduled tasks
        - Task processing times
        - Worker status
        - Queue lengths
        """
        try:
            from celery.control import inspect
            from datetime import datetime, timezone
            import time

            # Get Celery inspector
            insp = inspect()

            # Get active tasks
            active_tasks = insp.active() or {}
            scheduled_tasks = insp.scheduled() or {}
            registered_tasks = insp.registered() or {}
            stats = insp.stats() or {}

            queue_stats = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "workers": {},
                "queues": {},
                "tasks": {
                    "active": {},
                    "scheduled": {},
                    "registered": {}
                },
                "summary": {
                    "total_active_tasks": 0,
                    "total_scheduled_tasks": 0,
                    "total_workers": len(stats),
                    "total_registered_tasks": 0
                }
            }

            # Process active tasks
            for worker, tasks in active_tasks.items():
                worker_name = worker.split('@')[0] if '@' in worker else worker
                queue_stats["workers"][worker_name] = {
                    "name": worker,
                    "active_tasks": len(tasks),
                    "status": "active"
                }

                # Update worker status metric
                celery_worker_status.labels(worker=worker_name, status="active").set(1)

                for task in tasks:
                    queue_name = task.get('delivery_info', {}).get('routing_key', 'unknown')
                    if queue_name not in queue_stats["queues"]:
                        queue_stats["queues"][queue_name] = {
                            "active_tasks": 0,
                            "scheduled_tasks": 0,
                            "total_tasks": 0
                        }

                    queue_stats["queues"][queue_name]["active_tasks"] += 1
                    queue_stats["queues"][queue_name]["total_tasks"] += 1

                    # Update active task metrics
                    celery_active_tasks.labels(
                        queue=queue_name,
                        worker=worker_name
                    ).set(1)

                    # Track task details
                    task_name = task.get('name', 'unknown')
                    if task_name not in queue_stats["tasks"]["active"]:
                        queue_stats["tasks"]["active"][task_name] = []

                    task_info = {
                        "id": task.get('id'),
                        "worker": worker_name,
                        "queue": queue_name,
                        "args": str(task.get('args', []))[:100] + "..." if len(str(task.get('args', []))) > 100 else str(task.get('args', [])),
                        "kwargs": str(task.get('kwargs', {}))[:100] + "..." if len(str(task.get('kwargs', {}))) > 100 else str(task.get('kwargs', {})),
                        "started_at": task.get('time_start'),
                        "routing_key": task.get('delivery_info', {}).get('routing_key')
                    }

                    # Calculate processing time if started_at is available
                    if task.get('time_start'):
                        task_info["processing_time_seconds"] = time.time() - task['time_start']
                    else:
                        task_info["processing_time_seconds"] = None

                    queue_stats["tasks"]["active"][task_name].append(task_info)

            # Process scheduled tasks
            for worker, tasks in scheduled_tasks.items():
                worker_name = worker.split('@')[0] if '@' in worker else worker

                for task in tasks:
                    queue_name = task.get('request', {}).get('delivery_info', {}).get('routing_key', 'unknown')
                    if queue_name not in queue_stats["queues"]:
                        queue_stats["queues"][queue_name] = {
                            "active_tasks": 0,
                            "scheduled_tasks": 0,
                            "total_tasks": 0
                        }

                    queue_stats["queues"][queue_name]["scheduled_tasks"] += 1
                    queue_stats["queues"][queue_name]["total_tasks"] += 1

                    # Update scheduled task metrics
                    celery_scheduled_tasks.labels(queue=queue_name).inc()

                    # Track scheduled task details
                    task_name = task.get('request', {}).get('name', 'unknown')
                    if task_name not in queue_stats["tasks"]["scheduled"]:
                        queue_stats["tasks"]["scheduled"][task_name] = []

                    eta = task.get('eta')
                    if eta:
                        try:
                            eta_time = datetime.fromisoformat(eta.replace('Z', '+00:00'))
                            delay_seconds = (eta_time - datetime.now(timezone.utc)).total_seconds()
                        except:
                            delay_seconds = None
                    else:
                        delay_seconds = None

                    task_info = {
                        "id": task.get('request', {}).get('id'),
                        "worker": worker_name,
                        "queue": queue_name,
                        "eta": eta,
                        "delay_seconds": delay_seconds,
                        "priority": task.get('request', {}).get('priority'),
                        "args": str(task.get('request', {}).get('args', []))[:100] + "..." if len(str(task.get('request', {}).get('args', []))) > 100 else str(task.get('request', {}).get('args', [])),
                        "kwargs": str(task.get('request', {}).get('kwargs', {}))[:100] + "..." if len(str(task.get('request', {}).get('kwargs', {}))) > 100 else str(task.get('request', {}).get('kwargs', {}))
                    }

                    queue_stats["tasks"]["scheduled"][task_name].append(task_info)

            # Process registered tasks
            for worker, tasks in registered_tasks.items():
                worker_name = worker.split('@')[0] if '@' in worker else worker

                for task_name in tasks:
                    if task_name not in queue_stats["tasks"]["registered"]:
                        queue_stats["tasks"]["registered"][task_name] = []

                    queue_stats["tasks"]["registered"][task_name].append({
                        "worker": worker_name,
                        "task_name": task_name
                    })

            # Calculate summary statistics
            queue_stats["summary"]["total_active_tasks"] = sum(
                len(tasks) for tasks in queue_stats["tasks"]["active"].values()
            )
            queue_stats["summary"]["total_scheduled_tasks"] = sum(
                len(tasks) for tasks in queue_stats["tasks"]["scheduled"].values()
            )
            queue_stats["summary"]["total_registered_tasks"] = sum(
                len(tasks) for tasks in queue_stats["tasks"]["registered"].values()
            )

            # Update queue length metrics
            for queue_name, queue_info in queue_stats["queues"].items():
                celery_queue_length.labels(queue=queue_name).set(queue_info["total_tasks"])

            # Add worker statistics and update metrics
            active_worker_names = set()
            for worker, worker_stats in stats.items():
                worker_name = worker.split('@')[0] if '@' in worker else worker
                active_worker_names.add(worker_name)

                if worker_name in queue_stats["workers"]:
                    queue_stats["workers"][worker_name].update({
                        "pool": worker_stats.get('pool', {}),
                        "total": worker_stats.get('total', {}),
                        "pid": worker_stats.get('pid'),
                        "clock": worker_stats.get('clock')
                    })

                    # Update worker pool size metrics
                    pool_stats = worker_stats.get('pool', {})
                    celery_worker_pool_size.labels(
                        worker=worker_name,
                        pool_type="processes"
                    ).set(pool_stats.get("processes", 0))

            # Mark inactive workers
            all_worker_names = set(queue_stats["workers"].keys())
            inactive_workers = all_worker_names - active_worker_names
            for worker_name in inactive_workers:
                celery_worker_status.labels(worker=worker_name, status="inactive").set(0)

            logger.info(f"Retrieved queue stats: {queue_stats['summary']}")
            return queue_stats

        except Exception as e:
            logger.error(f"Error getting queue stats: {e}", exc_info=True)
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": "Failed to retrieve queue statistics"
            }

    def retry_failed_task(self, task_id: str) -> bool:
        """
        Retry a failed task.

        Args:
            task_id: Celery task ID

        Returns:
            True if retry was initiated
        """
        result = AsyncResult(task_id, app=celery_app)

        if result.state == "FAILURE":
            # In Celery, failed tasks need to be re-queued manually
            # This is more complex than BullMQ's retry mechanism
            logger.info(f"Manual retry for task {task_id} would need to be implemented")
            return False

        return False

    def record_task_completion(self, task_name: str, queue: str, duration_seconds: float, status: str = "success"):
        """Record task completion metrics."""
        # Record task processing duration
        celery_task_processing_duration.labels(
            queue=queue,
            task_name=task_name
        ).observe(duration_seconds)

        # Record task rate
        celery_task_rate.labels(
            queue=queue,
            task_name=task_name,
            status=status
        ).inc()

        if status == "failure":
            celery_task_failures.labels(
                queue=queue,
                task_name=task_name,
                failure_reason="unknown"
            ).inc()

    def record_task_failure(self, task_name: str, queue: str, failure_reason: str = "unknown"):
        """Record task failure metrics."""
        celery_task_failures.labels(
            queue=queue,
            task_name=task_name,
            failure_reason=failure_reason
        ).inc()

        celery_task_rate.labels(
            queue=queue,
            task_name=task_name,
            status="failure"
        ).inc()


# Create service instance
celery_service = CeleryService()
