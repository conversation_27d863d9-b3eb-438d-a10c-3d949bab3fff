"""
Analytics API Router - Media performance tracking
"""

import logging
from datetime import datetime, timedelta
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.analytics.service import analytics_service
from modules.analytics.event_service import analytics_event_service
from modules.analytics.event_schemas import (
    EventIngestionRequest, BatchEventIngestionRequest,
    EventIngestionResponse, BatchEventIngestionResponse,
    MediaAnalyticsRequest, MediaAnalyticsResponse,
    ConversionFunnelRequest, ConversionFunnelResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/events")
async def track_event(
    event_data: dict,
    db: AsyncSession = Depends(get_db),
):
    """
    Track analytics events (views, plays, conversions).
    
    Body: {variantId, eventType, userSessionId, viewport, device, ...}
    """
    try:
        await analytics_service.track_event(db, event_data)
        return {"status": "tracked"}
        
    except Exception as e:
        logger.error(f"Error tracking event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dashboard")
async def get_dashboard_metrics(
    days: int = Query(30, description="Number of days to look back"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get dashboard overview metrics."""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        metrics = await analytics_service.get_dashboard_metrics(
            db=db,
            user_id=current_user.id,
            start_date=start_date,
            end_date=end_date
        )
        
        return metrics

    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# New analytics event endpoints
@router.post("/events/ingest", response_model=EventIngestionResponse)
async def ingest_analytics_event(
    event_request: EventIngestionRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Ingest a single analytics event with deduplication."""
    try:
        # Get tenant ID from user (assuming user has tenant relationship)
        tenant_id = current_user.tenant_id

        # Get client IP
        ip_address = request.client.host if request.client else None

        response = await analytics_event_service.ingest_event(
            db, tenant_id, event_request, ip_address
        )
        return response

    except Exception as e:
        logger.error(f"Error ingesting event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/events/batch", response_model=BatchEventIngestionResponse)
async def ingest_batch_analytics_events(
    batch_request: BatchEventIngestionRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Ingest multiple analytics events in batch."""
    try:
        # Get client IP
        ip_address = request.client.host if request.client else None

        response = await analytics_event_service.ingest_batch_events(
            db, batch_request, ip_address
        )
        return response

    except Exception as e:
        logger.error(f"Error ingesting batch events: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/media-analytics", response_model=MediaAnalyticsResponse)
async def get_detailed_media_analytics(
    analytics_request: MediaAnalyticsRequest = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get detailed media analytics for a product or variant."""
    try:
        # Get tenant ID from user
        tenant_id = current_user.tenant_id

        response = await analytics_event_service.get_media_analytics(
            db, tenant_id, analytics_request
        )
        return response

    except Exception as e:
        logger.error(f"Error getting media analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversion-funnel", response_model=ConversionFunnelResponse)
async def get_detailed_conversion_funnel(
    funnel_request: ConversionFunnelRequest = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get detailed conversion funnel analysis."""
    try:
        # Get tenant ID from user
        tenant_id = current_user.tenant_id

        response = await analytics_event_service.get_conversion_funnel(
            db, tenant_id, funnel_request
        )
        return response

    except Exception as e:
        logger.error(f"Error getting conversion funnel: {e}")
        raise HTTPException(status_code=500, detail=str(e))
