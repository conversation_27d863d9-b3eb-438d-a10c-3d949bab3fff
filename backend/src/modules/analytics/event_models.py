"""
Analytics event models for ProductMedia platform.
Tracks media interactions, conversions, and user behavior.
"""

from sqlalchemy import <PERSON>umn, BigInteger, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum as PyEnum
import uuid

from core.db.database import Base


class EventType(PyEnum):
    """Analytics event types."""
    MEDIA_VIEW = "media_view"
    MEDIA_PLAY = "media_play"
    MEDIA_PAUSE = "media_pause"
    MEDIA_COMPLETE = "media_complete"
    MEDIA_SEEK = "media_seek"
    CTA_CLICK = "cta_click"
    ADD_TO_CART = "add_to_cart"
    PURCHASE = "purchase"
    PAGE_VIEW = "page_view"

class GroupBy(PyEnum):
    """Group by options for analytics."""
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    PRODUCT_VIEW = "product_view"


class AnalyticsEvent(Base):
    """
    Analytics event tracking for video interactions and conversions.
    Supports deduplication and conversion attribution.
    """
    __tablename__ = 'analytics_events'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)

    # Event identification
    event_id = Column(String(255), unique=True, index=True, nullable=False)  # UUID for deduplication
    event_type = Column(String(50), nullable=False)  # EventType enum value
    
    # Context
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    session_id = Column(String(255), index=True, nullable=True)
    user_id = Column(String(255), index=True, nullable=True)  # Anonymous or authenticated
    
    # Media context
    media_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    product_id = Column(String(255), index=True, nullable=True)
    
    # Event data
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    duration = Column(Float, nullable=True)  # Video duration or time spent
    position = Column(Float, nullable=True)  # Video position or scroll position
    
    # User agent and device info
    user_agent = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    country = Column(String(2), nullable=True)  # ISO country code
    device_type = Column(String(50), nullable=True)  # mobile, desktop, tablet
    
    # Attribution
    referrer = Column(Text, nullable=True)
    utm_source = Column(String(255), nullable=True)
    utm_medium = Column(String(255), nullable=True)
    utm_campaign = Column(String(255), nullable=True)
    utm_content = Column(String(255), nullable=True)
    utm_term = Column(String(255), nullable=True)
    
    # E-commerce data (for conversion events)
    order_id = Column(String(255), index=True, nullable=True)
    order_value = Column(Float, nullable=True)
    currency = Column(String(3), nullable=True)
    
    # Custom properties
    properties = Column(JSON, nullable=True, default=dict)
    
    # Processing flags
    is_processed = Column(Boolean, default=False, nullable=False)
    is_conversion = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    tenant = relationship("Tenant")
    media_variant = relationship("MediaVariant")

    def __repr__(self):
        return f"<AnalyticsEvent(id={self.id}, type='{self.event_type}', product_id='{self.product_id}')>"


# Create indexes for performance
Index('idx_analytics_events_tenant_timestamp', AnalyticsEvent.tenant_id, AnalyticsEvent.timestamp)
Index('idx_analytics_events_product_timestamp', AnalyticsEvent.product_id, AnalyticsEvent.timestamp)
Index('idx_analytics_events_session_timestamp', AnalyticsEvent.session_id, AnalyticsEvent.timestamp)
Index('idx_analytics_events_conversion', AnalyticsEvent.is_conversion, AnalyticsEvent.timestamp)


class ConversionFunnel(Base):
    """
    Conversion funnel tracking for video-to-purchase attribution.
    """
    __tablename__ = 'conversion_funnels'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)

    # Identification
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    session_id = Column(String(255), index=True, nullable=False)
    user_id = Column(String(255), index=True, nullable=True)
    
    # Funnel stages
    media_view_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    media_play_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    media_complete_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    cta_click_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    add_to_cart_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    purchase_event_id = Column(String(255), ForeignKey('analytics_events.event_id'), nullable=True)
    
    # Attribution
    first_media_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    converting_media_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    product_id = Column(String(255), index=True, nullable=True)
    
    # Conversion metrics
    time_to_conversion = Column(Integer, nullable=True)  # Seconds from first video view to purchase
    conversion_value = Column(Float, nullable=True)
    currency = Column(String(3), nullable=True)
    
    # Timestamps
    funnel_start = Column(DateTime(timezone=True), nullable=False)
    funnel_end = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    first_media_variant = relationship("MediaVariant", foreign_keys=[first_media_variant_id])
    converting_media_variant = relationship("MediaVariant", foreign_keys=[converting_media_variant_id])

    def __repr__(self):
        return f"<ConversionFunnel(id={self.id}, session_id='{self.session_id}', converted={self.purchase_event_id is not None})>"


class MediaAnalytics(Base):
    """
    Aggregated media analytics for performance tracking.
    """
    __tablename__ = 'media_analytics'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)

    # Identification
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    media_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=False)
    product_id = Column(String(255), index=True, nullable=False)
    
    # Time period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # View metrics
    views = Column(Integer, default=0, nullable=False)
    unique_views = Column(Integer, default=0, nullable=False)
    plays = Column(Integer, default=0, nullable=False)
    completions = Column(Integer, default=0, nullable=False)
    
    # Engagement metrics
    total_watch_time = Column(Float, default=0.0, nullable=False)  # Total seconds watched
    average_watch_time = Column(Float, default=0.0, nullable=False)
    completion_rate = Column(Float, default=0.0, nullable=False)  # Percentage
    
    # Interaction metrics
    cta_clicks = Column(Integer, default=0, nullable=False)
    cta_click_rate = Column(Float, default=0.0, nullable=False)  # Percentage
    
    # Conversion metrics
    add_to_carts = Column(Integer, default=0, nullable=False)
    purchases = Column(Integer, default=0, nullable=False)
    conversion_rate = Column(Float, default=0.0, nullable=False)  # Percentage
    total_revenue = Column(Float, default=0.0, nullable=False)
    
    # Device breakdown
    mobile_views = Column(Integer, default=0, nullable=False)
    desktop_views = Column(Integer, default=0, nullable=False)
    tablet_views = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    media_variant = relationship("MediaVariant")

    def __repr__(self):
        return f"<MediaAnalytics(id={self.id}, variant_id={self.media_variant_id}, views={self.views})>"


# Create indexes for analytics queries
Index('idx_media_analytics_tenant_date', MediaAnalytics.tenant_id, MediaAnalytics.date)
Index('idx_media_analytics_product_date', MediaAnalytics.product_id, MediaAnalytics.date)
Index('idx_media_analytics_variant_date', MediaAnalytics.media_variant_id, MediaAnalytics.date)


class ABTestExperiment(Base):
    """
    A/B testing experiments for media variants.
    """
    __tablename__ = 'ab_test_experiments'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)

    # Experiment details
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Configuration
    product_ids = Column(JSON, nullable=False)  # List of product IDs
    control_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=False)
    test_variant_ids = Column(JSON, nullable=False)  # List of variant IDs
    
    # Traffic allocation
    traffic_allocation = Column(Float, default=1.0, nullable=False)  # 0.0 to 1.0
    control_split = Column(Float, default=0.5, nullable=False)  # 0.0 to 1.0
    
    # Status
    is_active = Column(Boolean, default=False, nullable=False)
    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    
    # Results
    statistical_significance = Column(Float, nullable=True)  # p-value
    confidence_level = Column(Float, default=0.95, nullable=False)
    winner_variant_id = Column(Integer, ForeignKey('media_variants.id'), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    control_variant = relationship("MediaVariant", foreign_keys=[control_variant_id])
    winner_variant = relationship("MediaVariant", foreign_keys=[winner_variant_id])

    def __repr__(self):
        return f"<ABTestExperiment(id={self.id}, name='{self.name}', active={self.is_active})>"
