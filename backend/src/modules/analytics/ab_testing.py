"""
A/B Testing Service for Media Generation
Handles feature flags, experiment assignment, and metrics collection.
"""

import logging
import hashlib
from typing import Dict, Any, Optional, List
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)


class ExperimentStatus(str, Enum):
    """Experiment status."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"


class ABTestingService:
    """Service for A/B testing and feature flags."""
    
    def __init__(self):
        self.experiments = self._load_experiments()
        self.feature_flags = self._load_feature_flags()
    
    def _load_experiments(self) -> Dict[str, Dict[str, Any]]:
        """Load A/B test configurations."""
        return {
            "prompt_enhancement": {
                "name": "Enhanced Prompt Generation",
                "status": ExperimentStatus.ACTIVE,
                "traffic_split": 50,  # 50% get enhanced prompts
                "variants": {
                    "control": {
                        "name": "Standard Prompts",
                        "weight": 50,
                        "config": {"use_enhanced_prompts": False}
                    },
                    "treatment": {
                        "name": "Enhanced Prompts",
                        "weight": 50,
                        "config": {"use_enhanced_prompts": True}
                    }
                },
                "metrics": ["quality_score", "user_rating", "generation_time"],
                "start_date": "2024-01-01",
                "end_date": "2024-02-01"
            },
            "provider_fallback": {
                "name": "Multi-Provider Fallback",
                "status": ExperimentStatus.ACTIVE,
                "traffic_split": 30,  # 30% get multi-provider
                "variants": {
                    "control": {
                        "name": "Single Provider",
                        "weight": 70,
                        "config": {"use_fallback": False}
                    },
                    "treatment": {
                        "name": "Multi-Provider Fallback",
                        "weight": 30,
                        "config": {"use_fallback": True}
                    }
                },
                "metrics": ["success_rate", "cost_per_generation", "quality_score"],
                "start_date": "2024-01-01",
                "end_date": "2024-02-15"
            },
            "quality_threshold": {
                "name": "Quality Threshold Testing",
                "status": ExperimentStatus.DRAFT,
                "traffic_split": 25,
                "variants": {
                    "control": {
                        "name": "Standard Threshold (70)",
                        "weight": 50,
                        "config": {"quality_threshold": 70}
                    },
                    "treatment": {
                        "name": "Higher Threshold (80)",
                        "weight": 50,
                        "config": {"quality_threshold": 80}
                    }
                },
                "metrics": ["manual_review_rate", "user_satisfaction", "generation_cost"],
                "start_date": "2024-02-01",
                "end_date": "2024-03-01"
            }
        }
    
    def _load_feature_flags(self) -> Dict[str, Dict[str, Any]]:
        """Load feature flag configurations."""
        return {
            "text_generation_enabled": {
                "enabled": True,
                "rollout_percentage": 100,
                "user_segments": ["all"]
            },
            "image_generation_enabled": {
                "enabled": True,
                "rollout_percentage": 75,  # Gradual rollout
                "user_segments": ["paid", "beta"]
            },
            "video_generation_enabled": {
                "enabled": False,
                "rollout_percentage": 10,  # Beta testing
                "user_segments": ["beta", "enterprise"]
            },
            "enhanced_safety_checks": {
                "enabled": True,
                "rollout_percentage": 100,
                "user_segments": ["all"]
            },
            "quota_enforcement": {
                "enabled": True,
                "rollout_percentage": 100,
                "user_segments": ["all"]
            },
            "manual_review_queue": {
                "enabled": True,
                "rollout_percentage": 100,
                "user_segments": ["all"]
            },
            "accessibility_features": {
                "enabled": True,
                "rollout_percentage": 90,
                "user_segments": ["all"]
            }
        }
    
    def is_feature_enabled(
        self, 
        feature_name: str, 
        user_id: int, 
        user_segment: str = "default"
    ) -> bool:
        """Check if a feature is enabled for a user."""
        try:
            flag = self.feature_flags.get(feature_name)
            if not flag:
                return False
            
            # Check if feature is globally enabled
            if not flag.get("enabled", False):
                return False
            
            # Check user segment
            allowed_segments = flag.get("user_segments", ["all"])
            if "all" not in allowed_segments and user_segment not in allowed_segments:
                return False
            
            # Check rollout percentage
            rollout_percentage = flag.get("rollout_percentage", 0)
            if rollout_percentage >= 100:
                return True
            
            # Use consistent hash-based assignment
            user_hash = self._get_user_hash(user_id, feature_name)
            return user_hash < rollout_percentage
            
        except Exception as e:
            logger.error(f"Feature flag check failed for {feature_name}: {e}")
            return False
    
    def get_experiment_variant(
        self, 
        experiment_name: str, 
        user_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get experiment variant for a user."""
        try:
            experiment = self.experiments.get(experiment_name)
            if not experiment:
                return None
            
            # Check if experiment is active
            if experiment.get("status") != ExperimentStatus.ACTIVE:
                return None
            
            # Check if user is in experiment
            traffic_split = experiment.get("traffic_split", 0)
            user_hash = self._get_user_hash(user_id, experiment_name)
            
            if user_hash >= traffic_split:
                return None  # User not in experiment
            
            # Assign variant based on weights
            variants = experiment.get("variants", {})
            total_weight = sum(v.get("weight", 0) for v in variants.values())
            
            if total_weight == 0:
                return None
            
            # Use hash to consistently assign variant
            variant_hash = self._get_user_hash(user_id, f"{experiment_name}_variant") % total_weight
            
            current_weight = 0
            for variant_name, variant_config in variants.items():
                current_weight += variant_config.get("weight", 0)
                if variant_hash < current_weight:
                    return {
                        "experiment": experiment_name,
                        "variant": variant_name,
                        "config": variant_config.get("config", {}),
                        "name": variant_config.get("name", variant_name)
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Experiment variant assignment failed for {experiment_name}: {e}")
            return None
    
    def _get_user_hash(self, user_id: int, context: str) -> int:
        """Get consistent hash for user in given context."""
        hash_input = f"{user_id}:{context}"
        hash_value = hashlib.md5(hash_input.encode()).hexdigest()
        return int(hash_value[:8], 16) % 100
    
    async def track_experiment_event(
        self,
        experiment_name: str,
        variant: str,
        user_id: int,
        event_type: str,
        event_data: Dict[str, Any]
    ):
        """Track experiment event for analysis."""
        try:
            from core.db.database import SessionLocal
            from modules.analytics.models import AnalyticsEvent
            
            db = SessionLocal()
            
            # Create analytics event with experiment context
            event = AnalyticsEvent(
                user_id=user_id,
                event_type=event_type,
                timestamp=datetime.utcnow(),
                metadata={
                    "experiment": experiment_name,
                    "variant": variant,
                    "event_data": event_data
                }
            )
            
            db.add(event)
            db.commit()
            db.close()
            
            logger.info(f"Tracked experiment event: {experiment_name}/{variant} - {event_type}")
            
        except Exception as e:
            logger.error(f"Failed to track experiment event: {e}")
    
    def get_user_experiments(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all active experiments for a user."""
        user_experiments = []
        
        for experiment_name, experiment in self.experiments.items():
            variant = self.get_experiment_variant(experiment_name, user_id)
            if variant:
                user_experiments.append(variant)
        
        return user_experiments
    
    def get_experiment_config(
        self, 
        experiment_name: str, 
        user_id: int, 
        default_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Get experiment configuration for a user."""
        variant = self.get_experiment_variant(experiment_name, user_id)
        
        if variant:
            # Merge experiment config with defaults
            config = default_config.copy() if default_config else {}
            config.update(variant.get("config", {}))
            return config
        
        return default_config or {}
    
    async def get_experiment_metrics(self, experiment_name: str) -> Dict[str, Any]:
        """Get metrics for an experiment."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select, func
            from modules.analytics.models import AnalyticsEvent
            
            db = SessionLocal()
            
            # Get events for this experiment
            events = db.execute(
                select(AnalyticsEvent).filter(
                    AnalyticsEvent.metadata.op('->>')('experiment') == experiment_name
                )
            ).scalars().all()
            
            # Calculate metrics by variant
            metrics = {}
            for event in events:
                variant = event.metadata.get("variant", "unknown")
                if variant not in metrics:
                    metrics[variant] = {
                        "total_events": 0,
                        "unique_users": set(),
                        "event_types": {}
                    }
                
                metrics[variant]["total_events"] += 1
                metrics[variant]["unique_users"].add(event.user_id)
                
                event_type = event.event_type
                if event_type not in metrics[variant]["event_types"]:
                    metrics[variant]["event_types"][event_type] = 0
                metrics[variant]["event_types"][event_type] += 1
            
            # Convert sets to counts
            for variant in metrics:
                metrics[variant]["unique_users"] = len(metrics[variant]["unique_users"])
            
            db.close()
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get experiment metrics: {e}")
            return {}


# Global instance
ab_testing_service = ABTestingService()
