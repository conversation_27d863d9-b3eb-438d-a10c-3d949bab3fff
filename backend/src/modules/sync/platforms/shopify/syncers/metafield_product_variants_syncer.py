"""
Shopify Metafield Variant Syncer - Handles synchronization of Shopify product variant metafields.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text as sql_text
import json

from ....base.syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class MetafieldProductVariantsSyncer(BaseSyncer):
    """
    Syncer for Shopify product variant metafields.

    Aggregates metafields from metafield_product_variants table and updates product_variants.metafields column.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'metafield_product_variants'
        logger.info(f"DEBUG: MetafieldProductVariantsSyncer initialized with entity_type '{self.entity_type}'")
        logger.info(f"DEBUG: This syncer reads from Airbyte table '{self.entity_type}' but writes to local 'product_variants.metafields' column")

    def get_destination_table(self) -> str:
        """Override to specify the correct destination table for statistics tracking.

        Metafield syncers write to product_variants table, not metafield_product_variants.
        """
        return 'product_variants'

    def sync(self, db: Session, store_id: int) -> Dict[str, Any]:
        """
        Sync product variant metafields for a specific store with comprehensive statistics tracking.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = self.get_last_sync_time(db, store_id)

        store = self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        # Start comprehensive statistics tracking
        self.start_sync_tracking(db, store_id)

        logger.info(f"🚀 Starting variant metafields sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM metafield_product_variants"))
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} metafield records in Airbyte metafield_product_variants table")

                # Process data in batches until no more data
                offset = 0
                iteration = 1
                errors_in_sync = 0

                while True:
                    batch_start_time = time.time()
                    logger.info(f"Iteration {iteration}: Fetching variant metafield data from Airbyte")

                    try:
                        select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                        airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                            'last_sync_time': last_sync_time,
                            'batch_size': batch_size,
                            'offset': offset,
                            'store_id': store_id
                        })
                        rows = airbyte_result.fetchall()

                        if not rows:
                            logger.info(f"🎉 Iteration {iteration}: No more variant metafield records to sync!")
                            break

                        logger.info(f"Iteration {iteration}: Fetched {len(rows)} variant metafield records from Airbyte")

                        # Aggregate metafields by owner_id and update variants
                        metafields_by_owner = self.aggregate_metafields(rows)
                        batch_errors = 0

                        if metafields_by_owner:
                            try:
                                logger.info(f"DEBUG: About to update {len(metafields_by_owner)} variants with metafields data")
                                update_count = self.update_variants_metafields(db, metafields_by_owner, store_id)
                                total_processed += update_count
                                logger.info(f"Iteration {iteration}: Successfully updated {update_count} variants with metafields (total: {total_processed})")
                                logger.info(f"DEBUG: These updates go to the 'product_variants' table, not '{self.entity_type}' table")

                                # Track successful batch
                                batch_time = time.time() - batch_start_time
                                self.track_batch_processing(iteration, len(rows), batch_time, update_count, batch_errors)

                            except Exception as e:
                                db.rollback()
                                self.track_error('metafield_update_error', str(e))
                                batch_errors += len(metafields_by_owner)
                                errors_in_sync += batch_errors
                                logger.error(f"Error updating metafields for batch {iteration}: {e}")

                        # Move to next batch
                        offset += batch_size
                        iteration += 1

                    except Exception as e:
                        self.track_error('batch_fetch_error', str(e))
                        errors_in_sync += 1
                        logger.error(f"Error fetching batch {iteration}: {e}")
                        break

                logger.info(f"🏁 VARIANT METAFIELDS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total variants updated: {total_processed}")
                logger.info(f"   Total errors: {errors_in_sync}")

                # Finalize comprehensive statistics
                self.finalize_sync_statistics(db, store_id)

                # Update sync checkpoint with comprehensive statistics
                self.update_sync_checkpoint_comprehensive(db, store_id, total_processed)

                return {
                    'entity_type': 'metafield_product_variants',
                    'processed_count': total_processed,
                    'error_count': errors_in_sync,
                    'status': 'completed',
                    'iterations_completed': iteration - 1,
                    'statistics': self.sync_stats.copy()
                }

        except Exception as e:
            db.rollback()
            self.track_error('sync_failed', str(e))
            self.finalize_sync_statistics(db, store_id)
            logger.error(f"❌ Error in variant metafields sync: {e}", exc_info=True)
            return {
                'entity_type': 'metafield_product_variants',
                'processed_count': total_processed,
                'error_count': errors_in_sync + 1,
                'status': 'failed',
                'error': str(e),
                'statistics': self.sync_stats.copy()
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching variant metafields from Airbyte."""
        return """
        SELECT
            owner_id,
            key,
            value,
            namespace,
            type,
            COALESCE(updated_at, _airbyte_extracted_at) as updated_at
        FROM metafield_product_variants
        WHERE _airbyte_extracted_at > :last_sync_time
          AND owner_id IS NOT NULL
          AND key IS NOT NULL
        ORDER BY owner_id, namespace, key
        LIMIT :batch_size OFFSET :offset
        """

    def aggregate_metafields(self, rows: List[Tuple]) -> Dict[str, Dict[str, Any]]:
        """
        Aggregate metafields by owner_id.

        Returns:
            Dict[owner_id, Dict[namespace, Dict[key, value]]]
        """
        metafields_by_owner = {}

        for row in rows:
            owner_id, key, value, namespace, field_type, updated_at = row

            if owner_id not in metafields_by_owner:
                metafields_by_owner[owner_id] = {}

            if namespace not in metafields_by_owner[owner_id]:
                metafields_by_owner[owner_id][namespace] = {}

            # Store the metafield with its metadata
            metafields_by_owner[owner_id][namespace][key] = {
                'value': value,
                'type': field_type,
                'updated_at': updated_at.isoformat() if updated_at else None
            }

        return metafields_by_owner

    def update_variants_metafields(self, db: Session, metafields_by_owner: Dict[str, Dict[str, Any]], store_id: int) -> int:
        """
        Update product_variants table with aggregated metafields.
        """
        update_count = 0

        for owner_id, metafields in metafields_by_owner.items():
            try:
                # Convert to JSON for storage
                metafields_json = json.dumps(metafields)

                # Update the variant record
                update_sql = """
                UPDATE product_variants
                SET metafields = :metafields,
                    updated_at = NOW()
                WHERE external_id = :external_id
                  AND product_id IN (SELECT id FROM products WHERE store_id = :store_id)
                """

                result = db.execute(sql_text(update_sql), {
                    'metafields': metafields_json,
                    'external_id': str(owner_id),
                    'store_id': store_id
                })

                if result.rowcount > 0:
                    update_count += 1

            except Exception as e:
                logger.error(f"Error updating metafields for variant {owner_id}: {e}")
                # Rollback immediately to clear the aborted transaction state
                db.rollback()
                continue

        # Commit all successful changes
        try:
            db.commit()
        except Exception as e:
            logger.error(f"Error committing variant metafield updates: {e}")
            db.rollback()
            return 0

        return update_count

    def update_sync_checkpoint(self, db: Session, store_id: int, processed_count: int):
        """Update sync checkpoint after successful sync, using airbyte connection for counting."""
        from sqlalchemy import text as sql_text
        from datetime import datetime, timezone

        checkpoint = db.query(SyncCheckpoint).filter(
            SyncCheckpoint.store_id == store_id,
            SyncCheckpoint.entity_type == self.entity_type
        ).first()

        now = datetime.now(timezone.utc)

        # Use airbyte engine to get the actual count from the source table
        airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
        with airbyte_engine.connect() as airbyte_conn:
            total_count_query = sql_text(f"SELECT COUNT(*) FROM {self.entity_type}")
            total_count_result = airbyte_conn.execute(total_count_query)
            actual_total_count = total_count_result.scalar()

        if checkpoint:
            checkpoint.last_successful_sync_at = now
            checkpoint.last_sync_status = 'completed'
            checkpoint.updated_at = now
            checkpoint.total_records = actual_total_count
        else:
            checkpoint = SyncCheckpoint(
                store_id=store_id,
                entity_type=self.entity_type,
                last_successful_sync_at=now,
                total_records=actual_total_count,
                last_sync_status='completed',
                created_at=now,
                updated_at=now
            )
            db.add(checkpoint)

        # Record sync checkpoint update metric
        from core.metrics import sync_checkpoint_updates
        sync_checkpoint_updates.labels(
            store_domain="",  # We don't have store_domain here, could be enhanced
            entity_type=self.entity_type,
            stage="completed"
        ).inc()

        db.commit()
        logger.debug(f"Updated checkpoint for {self.entity_type}: {processed_count} processed, {checkpoint.total_records} total in airbyte table")