"""
Shopify Variant Syncer - Handles synchronization of Shopify product variants.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text as sql_text

from ....base.syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class ProductVariantsSyncer(BaseSyncer):
    """
    Syncer for Shopify product variants.

    Handles the synchronization of product variant data from Airbyte to the production database.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'product_variants'

    def sync(self, db: Session, store_id: int) -> Dict[str, Any]:
        """
        Sync product variants for a specific store.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = self.get_last_sync_time(db, store_id)

        store = self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        logger.info(f"🚀 Starting variants sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM product_variants WHERE shop_url = :shop_url"), {'shop_url': shop_url})
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} records in Airbyte product_variants table for shop_url: {shop_url}")

                # Process data in batches until no more data
                offset = 0
                iteration = 1

                while True:
                    logger.info(f"Iteration {iteration}: Fetching data from Airbyte product_variants table")

                    select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                    airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                        'last_sync_time': last_sync_time,
                        'batch_size': batch_size,
                        'offset': offset,
                        'shop_url': shop_url
                    })
                    rows = airbyte_result.fetchall()

                    if not rows:
                        logger.info(f"🎉 Iteration {iteration}: No more records to sync!")
                        break

                    logger.info(f"Iteration {iteration}: Fetched {len(rows)} records from Airbyte")

                    # Get product ID mappings from production database
                    product_mappings = {}
                    product_external_ids = [row[1] for row in rows]  # product_external_id is at index 1

                    if product_external_ids:
                        placeholders = ','.join([':param' + str(i) for i in range(len(product_external_ids))])
                        mapping_query = f"""
                        SELECT external_id, id FROM products
                        WHERE external_id IN ({placeholders}) AND store_id = :store_id
                        """
                        params = {f'param{i}': pid for i, pid in enumerate(product_external_ids)}
                        params['store_id'] = store_id
                        mapping_result = db.execute(sql_text(mapping_query), params)
                        product_mappings = {row[0]: row[1] for row in mapping_result.fetchall()}

                    # Transform and insert data
                    insert_data = []
                    for row in rows:
                        transformed_row = self.transform_row(row, store_id, product_mappings)
                        if transformed_row:
                            insert_data.append(transformed_row)

                    if insert_data:
                        insert_sql = self.get_insert_query()
                        from psycopg2.extras import execute_values
                        with db.connection().connection.cursor() as cursor:
                            execute_values(cursor, insert_sql, insert_data)
                            batch_processed = cursor.rowcount

                        db.commit()
                        total_processed += batch_processed
                        logger.info(f"Iteration {iteration}: Successfully inserted {batch_processed} product_variants (total: {total_processed})")

                    # Move to next batch
                    offset += batch_size
                    iteration += 1

                logger.info(f"🏁 VARIANTS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total records processed: {total_processed}")

                # Update sync checkpoint
                self.update_sync_checkpoint(db, store_id, total_processed)

                return {
                    'entity_type': 'product_variants',
                    'processed_count': total_processed,
                    'error_count': 0,
                    'status': 'completed',
                    'iterations_completed': iteration - 1
                }

        except Exception as e:
            db.rollback()
            logger.error(f"❌ Error in variants sync: {e}", exc_info=True)
            return {
                'entity_type': 'product_variants',
                'processed_count': total_processed,
                'error_count': 1,
                'status': 'failed',
                'error': str(e)
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching variants from Airbyte."""
        return """
        SELECT
            id::text as external_id,
            product_id::text as product_external_id,
            title as title,
            sku as sku,
            barcode as barcode,
            price::numeric as price,
            CASE
                WHEN compare_at_price ~ '^[0-9]+(\\.[0-9]+)?$'
                THEN compare_at_price::numeric
                ELSE NULL
            END as compare_at_price,
            NULL as cost,
            weight::numeric as weight,
            COALESCE(weight_unit, 'kg') as weight_unit,
            COALESCE(inventory_quantity, 0) as quantity,
            inventory_policy as inventory_policy,
            inventory_item_id::text as inventory_item_id,
            option1 as option1,
            option2 as option2,
            option3 as option3,
            COALESCE(taxable, true) as taxable,
            COALESCE(requires_shipping, true) as requires_shipping,
            NULL as fulfillment_service,
            COALESCE(available_for_sale, true) as available_for_sale,
            row_to_json(product_variants.*)::text as full_json,
            COALESCE(updated_at, _airbyte_extracted_at) as source_updated_at
        FROM product_variants
        WHERE _airbyte_extracted_at > :last_sync_time
          AND id IS NOT NULL
          AND product_id IS NOT NULL
          AND shop_url = :shop_url
        ORDER BY id::text
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting variants."""
        return """
        INSERT INTO product_variants (
            external_id, product_id, title, sku, barcode, price, compare_at_price,
            cost, weight, weight_unit, quantity, inventory_policy, inventory_item_id,
            option1, option2, option3, taxable, requires_shipping, fulfillment_service,
            available_for_sale, full_json, source_updated_at
        ) VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET
            title = EXCLUDED.title,
            sku = EXCLUDED.sku,
            barcode = EXCLUDED.barcode,
            price = EXCLUDED.price,
            compare_at_price = EXCLUDED.compare_at_price,
            cost = EXCLUDED.cost,
            weight = EXCLUDED.weight,
            weight_unit = EXCLUDED.weight_unit,
            quantity = EXCLUDED.quantity,
            inventory_policy = EXCLUDED.inventory_policy,
            inventory_item_id = EXCLUDED.inventory_item_id,
            option1 = EXCLUDED.option1,
            option2 = EXCLUDED.option2,
            option3 = EXCLUDED.option3,
            taxable = EXCLUDED.taxable,
            requires_shipping = EXCLUDED.requires_shipping,
            fulfillment_service = EXCLUDED.fulfillment_service,
            available_for_sale = EXCLUDED.available_for_sale,
            full_json = EXCLUDED.full_json,
            updated_at = NOW(),
            source_updated_at = EXCLUDED.source_updated_at
        WHERE product_variants.source_updated_at IS NULL
           OR EXCLUDED.source_updated_at > product_variants.source_updated_at
        """

    def transform_row(self, row: tuple, store_id: int, product_mappings: Dict[str, int]) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.

        Row structure from SELECT:
        0: external_id, 1: product_external_id, 2: title, 3: sku, 4: barcode, 5: price,
        6: compare_at_price, 7: cost, 8: weight, 9: weight_unit, 10: quantity,
        11: inventory_policy, 12: inventory_item_id, 13: option1, 14: option2, 15: option3,
        16: taxable, 17: requires_shipping, 18: fulfillment_service, 19: available_for_sale,
        20: full_json, 21: source_updated_at
        """
        external_id, product_external_id, *rest = row
        product_id = product_mappings.get(product_external_id)

        if product_id:
            # Insert product_id at the beginning and return the transformed row
            return (external_id, product_id) + tuple(rest)
        else:
            # Skip variants without valid product IDs
            logger.warning(f"Skipping variant {external_id} - no valid product_id found for product_external_id {product_external_id}")
            return None
