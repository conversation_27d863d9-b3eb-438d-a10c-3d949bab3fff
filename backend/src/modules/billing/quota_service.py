"""
Quota and Cost Control Service
Manages per-merchant quotas, generation budgets, and cost tracking.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession
from core.db.database import get_db

logger = logging.getLogger(__name__)


class QuotaType(str, Enum):
    """Types of quotas."""
    DAILY = "daily"
    MONTHLY = "monthly"
    TOTAL = "total"


class MediaType(str, Enum):
    """Media types for quota tracking."""
    IMAGE = "image"
    VIDEO = "video"
    TEXT = "text"


class QuotaService:
    """Service for managing quotas and cost controls."""

    def __init__(self):
        self.default_quotas = self._get_default_quotas()
        self.cost_per_generation = self._get_cost_structure()

    async def get_db_session(self):
        """Get a database session using the simplified database setup."""
        # get_db() is a FastAPI dependency that returns an async generator
        # We need to call it to get the actual session
        async with get_db() as session:
            return session
    
    def _get_default_quotas(self) -> Dict[str, Dict[str, int]]:
        """Get default quotas by plan tier."""
        return {
            "free": {
                "daily_images": 10,
                "daily_videos": 2,
                "daily_text": 50,
                "monthly_images": 100,
                "monthly_videos": 20,
                "monthly_text": 500,
                "total_storage_mb": 100
            },
            "starter": {
                "daily_images": 50,
                "daily_videos": 10,
                "daily_text": 200,
                "monthly_images": 1000,
                "monthly_videos": 100,
                "monthly_text": 5000,
                "total_storage_mb": 1000
            },
            "growth": {
                "daily_images": 200,
                "daily_videos": 50,
                "daily_text": 1000,
                "monthly_images": 5000,
                "monthly_videos": 500,
                "monthly_text": 20000,
                "total_storage_mb": 10000
            },
            "pro": {
                "daily_images": 1000,
                "daily_videos": 200,
                "daily_text": 5000,
                "monthly_images": 25000,
                "monthly_videos": 2000,
                "monthly_text": 100000,
                "total_storage_mb": 50000
            },
            "enterprise": {
                "daily_images": -1,  # Unlimited
                "daily_videos": -1,
                "daily_text": -1,
                "monthly_images": -1,
                "monthly_videos": -1,
                "monthly_text": -1,
                "total_storage_mb": -1
            }
        }
    
    def _get_cost_structure(self) -> Dict[str, float]:
        """Get cost per generation by media type."""
        return {
            "image": 0.02,  # $0.02 per image
            "video": 0.50,  # $0.50 per video
            "text": 0.001,  # $0.001 per text generation
        }
    
    async def check_quota(
        self,
        db: AsyncSession,
        user_id: int,
        media_type: str,
        quantity: int = 1
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if user has quota available for generation.
        Prioritizes credits over plan limits.

        Returns:
            Tuple of (has_quota, quota_info)
        """
        try:
            from sqlalchemy import select, and_, func
            from modules.auth.models import User, Tenant
            from modules.media.models import MediaJob
            from sqlalchemy.orm import selectinload

            logger.debug(f"Starting quota check for user {user_id}, media_type: {media_type}, quantity: {quantity}")
            logger.debug(f"Session transaction state: in_transaction={db.in_transaction()}, connection={db.connection()}")

            # Check if session is already in a transaction
            if db.in_transaction():
                logger.debug("Session already in transaction, proceeding without new transaction")
                # Session is already in transaction, proceed without starting a new one
                transaction_context = None
            else:
                logger.debug("Starting new transaction for quota check")
                transaction_context = db.begin()
                await transaction_context.__aenter__()

            try:
                # Get user and tenant
                user = await db.execute(select(User).options(selectinload(User.tenants)).filter(User.id == user_id))
                user = user.scalar_one_or_none()
                if not user:
                    return False, {"error": "User not found"}

                # Get tenant (assuming user has one tenant for now)
                tenant = user.tenants[0] if user.tenants else None
                if not tenant:
                    return False, {"error": "Tenant not found"}

                plan_tier = tenant.plan_tier
                quotas = self.default_quotas.get(plan_tier, self.default_quotas['free'])

                # First, check if user has sufficient credits
                credit_balance = tenant.credits or 0.0
                cost_per_item = self.cost_per_generation.get(media_type, 0.02)
                total_cost = cost_per_item * quantity

                if credit_balance >= total_cost:
                    # User has enough credits - allow the request
                    return True, {
                        "quota_type": "credits_available",
                        "credit_balance": credit_balance,
                        "total_cost": total_cost,
                        "remaining_credits": credit_balance - total_cost,
                        "plan_tier": plan_tier
                    }

                # If not enough credits, check plan limits
                # Check daily quota
                today = datetime.now().date()
                daily_key = f"daily_{media_type}s"
                daily_limit = quotas.get(daily_key, 0)

                if daily_limit > 0:  # -1 means unlimited
                    from sqlalchemy import extract
                    daily_usage_result = await db.execute(
                        select(func.count(MediaJob.id)).filter(
                            and_(
                                MediaJob.user_id == user_id,
                                MediaJob.media_type == media_type,
                                extract('year', MediaJob.created_at) == today.year,
                                extract('month', MediaJob.created_at) == today.month,
                                extract('day', MediaJob.created_at) == today.day
                            )
                        )
                    )
                    daily_usage = daily_usage_result.scalar() or 0

                    if daily_usage + quantity > daily_limit:
                        return False, {
                            "quota_type": "daily_limit_exceeded",
                            "credit_balance": credit_balance,
                            "insufficient_credits": total_cost - credit_balance,
                            "daily_limit": daily_limit,
                            "daily_used": daily_usage,
                            "requested": quantity,
                            "available": max(0, daily_limit - daily_usage)
                        }

                # Check monthly quota
                month_start = datetime.now().replace(day=1).date()
                monthly_key = f"monthly_{media_type}s"
                monthly_limit = quotas.get(monthly_key, 0)

                if monthly_limit > 0:  # -1 means unlimited
                    monthly_usage_result = await db.execute(
                        select(func.count(MediaJob.id)).filter(
                            and_(
                                MediaJob.user_id == user_id,
                                MediaJob.media_type == media_type,
                                extract('year', MediaJob.created_at) == month_start.year,
                                extract('month', MediaJob.created_at) == month_start.month,
                                extract('day', MediaJob.created_at) >= month_start.day
                            )
                        )
                    )
                    monthly_usage = monthly_usage_result.scalar() or 0

                    if monthly_usage + quantity > monthly_limit:
                        return False, {
                            "quota_type": "monthly_limit_exceeded",
                            "credit_balance": credit_balance,
                            "insufficient_credits": total_cost - credit_balance,
                            "monthly_limit": monthly_limit,
                            "monthly_used": monthly_usage,
                            "requested": quantity,
                            "available": max(0, monthly_limit - monthly_usage)
                        }

                return True, {
                    "quota_type": "plan_limits_available",
                    "credit_balance": credit_balance,
                    "total_cost": total_cost,
                    "insufficient_credits": max(0, total_cost - credit_balance),
                    "daily_limit": daily_limit,
                    "monthly_limit": monthly_limit,
                    "plan_tier": plan_tier
                }
            finally:
                # Clean up transaction if we started one
                if transaction_context:
                    await transaction_context.__aexit__(None, None, None)

        except Exception as e:
            logger.error(f"Quota check failed: {e}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            # For event loop errors, return a more user-friendly message
            if "different loop" in str(e) or "Event loop is closed" in str(e):
                return False, {
                    "error": "Service temporarily unavailable. Please try again.",
                    "quota_type": "service_error"
                }

            return False, {"error": str(e), "exception_type": str(type(e))}
    
    async def check_budget(
        self,
        db: AsyncSession,
        user_id: int,
        media_type: str,
        quantity: int = 1
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if user has budget available for generation.

        Returns:
            Tuple of (has_budget, budget_info)
        """
        try:
            cost_per_item = self.cost_per_generation.get(media_type, 0.02)
            total_cost = cost_per_item * quantity

            # Get user's tenant (credits are stored at tenant level)
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload
            from modules.auth.models import User

            # Check if session is already in a transaction
            if db.in_transaction():
                logger.debug("Session already in transaction for budget check, proceeding without new transaction")
                transaction_context = None
            else:
                logger.debug("Starting new transaction for budget check")
                transaction_context = db.begin()
                await transaction_context.__aenter__()

            try:
                user_result = await db.execute(select(User).options(selectinload(User.tenants)).filter(User.id == user_id))
                user = user_result.scalar_one_or_none()

                if not user or not user.tenants:
                    return False, {"error": "User or tenant not found"}

                # Get tenant credits directly from the loaded tenant object
                tenant = user.tenants[0]  # Assuming user has one primary tenant
                available_credits = tenant.credits or 0.0

                if available_credits < total_cost:
                    return False, {
                        "budget_type": "insufficient_credits",
                        "required": total_cost,
                        "available": available_credits,
                        "deficit": total_cost - available_credits
                    }

                return True, {
                    "budget_type": "sufficient",
                    "cost": total_cost,
                    "available_credits": available_credits,
                    "remaining_after": available_credits - total_cost
                }
            finally:
                if transaction_context:
                    await transaction_context.__aexit__(None, None, None)

        except Exception as e:
            logger.error(f"Budget check failed: {e}")
            return False, {"error": str(e)}
    
    async def deduct_quota_and_budget(
        self,
        db: AsyncSession,
        user_id: int,
        media_type: str,
        quantity: int = 1,
        resource_id: str = None,
        resource_type: str = None
    ) -> bool:
        """
        Deduct quota and budget after successful generation.

        Returns:
            True if deduction successful
        """
        try:
            cost_per_item = self.cost_per_generation.get(media_type, 0.02)
            total_cost = cost_per_item * quantity

            # Get user's tenant
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload
            from modules.auth.models import User

            # Check if session is already in a transaction
            if db.in_transaction():
                logger.debug("Session already in transaction for deduction, proceeding without new transaction")
                transaction_context = None
            else:
                logger.debug("Starting new transaction for deduction")
                transaction_context = db.begin()
                await transaction_context.__aenter__()

            try:
                user_result = await db.execute(select(User).options(selectinload(User.tenants)).filter(User.id == user_id))
                user = user_result.scalar_one_or_none()

                if not user or not user.tenants:
                    return False

                # Deduct credits directly from the tenant object
                tenant = user.tenants[0]  # Assuming user has one primary tenant
                current_balance = tenant.credits or 0.0

                if current_balance >= total_cost:
                    # Deduct credits
                    tenant.credits = current_balance - total_cost

                    # Create transaction record for audit trail
                    from .models import CreditTransaction, CreditTransactionType
                    from decimal import Decimal

                    description = f"Usage: {quantity} {media_type} generations"
                    transaction = CreditTransaction(
                        tenant_id=tenant.id,
                        transaction_type=CreditTransactionType.USAGE,
                        amount=Decimal(str(-total_cost)),  # Negative for deduction
                        balance_after=Decimal(str(tenant.credits)),
                        description=description,
                        resource_id=resource_id,
                        resource_type=resource_type,
                        transaction_metadata={"deducted_amount": total_cost}
                    )

                    db.add(transaction)
                    success = True
                    logger.info(f"Deducted {total_cost} credits from tenant {tenant.id}, new balance: {tenant.credits}")
                else:
                    success = False
                    logger.error(f"Insufficient credits for tenant {tenant.id}: required {total_cost}, available {current_balance}")

                if success:
                    logger.info(f"Deducted {total_cost} credits for {quantity} {media_type} generations for user {user_id}")
                else:
                    logger.error(f"Failed to deduct credits for user {user_id}")

                return success
            finally:
                if transaction_context:
                    await transaction_context.__aexit__(None, None, None)

        except Exception as e:
            logger.error(f"Quota/budget deduction failed: {e}")
            return False
    
    async def get_usage_stats(self, db: AsyncSession, user_id: int) -> Dict[str, Any]:
        """Get usage statistics for a user."""
        try:
            from sqlalchemy import select, and_, func
            from modules.auth.models import User
            from modules.media.models import MediaJob
            from sqlalchemy.orm import selectinload

            # Check if session is already in a transaction
            if db.in_transaction():
                logger.debug("Session already in transaction for usage stats, proceeding without new transaction")
                transaction_context = None
            else:
                logger.debug("Starting new transaction for usage stats")
                transaction_context = db.begin()
                await transaction_context.__aenter__()

            try:
                # Get user and plan
                user_result = await db.execute(select(User).options(selectinload(User.tenants)).filter(User.id == user_id))
                user = user_result.scalar_one_or_none()
                if not user or not user.tenants:
                    return {"error": "User or tenant not found"}

                tenant = user.tenants[0]
                plan_tier = tenant.plan_tier
                quotas = self.default_quotas.get(plan_tier, self.default_quotas['free'])

                # Calculate usage for different periods
                today = datetime.now().date()
                month_start = datetime.now().replace(day=1).date()

                stats = {
                    "plan_tier": plan_tier,
                    "quotas": quotas,
                    "usage": {
                        "daily": {},
                        "monthly": {},
                        "total": {}
                    }
                }

                for media_type in ["image", "video", "text"]:
                    # Daily usage
                    daily_result = await db.execute(
                        select(func.count(MediaJob.id)).filter(
                            and_(
                                MediaJob.user_id == user_id,
                                MediaJob.media_type == media_type,
                                extract('year', MediaJob.created_at) == today.year,
                                extract('month', MediaJob.created_at) == today.month,
                                extract('day', MediaJob.created_at) == today.day
                            )
                        )
                    )
                    daily_usage = daily_result.scalar() or 0

                    # Monthly usage
                    monthly_result = await db.execute(
                        select(func.count(MediaJob.id)).filter(
                            and_(
                                MediaJob.user_id == user_id,
                                MediaJob.media_type == media_type,
                                extract('year', MediaJob.created_at) == month_start.year,
                                extract('month', MediaJob.created_at) == month_start.month,
                                extract('day', MediaJob.created_at) >= month_start.day
                            )
                        )
                    )
                    monthly_usage = monthly_result.scalar() or 0

                    # Total usage
                    total_result = await db.execute(
                        select(func.count(MediaJob.id)).filter(
                            and_(
                                MediaJob.user_id == user_id,
                                MediaJob.media_type == media_type
                            )
                        )
                    )
                    total_usage = total_result.scalar() or 0

                    stats["usage"]["daily"][media_type] = daily_usage
                    stats["usage"]["monthly"][media_type] = monthly_usage
                    stats["usage"]["total"][media_type] = total_usage

                return stats
            finally:
                if transaction_context:
                    await transaction_context.__aexit__(None, None, None)

        except Exception as e:
            logger.error(f"Failed to get usage stats: {e}")
            return {"error": str(e)}


    # Synchronous versions for use in sync contexts (like Celery tasks)
    def check_quota_sync(self, user_id: int, media_type: str, quantity: int = 1) -> Tuple[bool, Dict[str, Any]]:
        """Synchronous version of check_quota for use in sync contexts."""
        import asyncio
        try:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self.check_quota(user_id, media_type, quantity))
                        return future.result()
                else:
                    return loop.run_until_complete(self.check_quota(user_id, media_type, quantity))
            except RuntimeError:
                return asyncio.run(self.check_quota(user_id, media_type, quantity))
        except Exception as e:
            logger.error(f"Error in sync check_quota: {e}")
            return False, {"error": str(e)}

    def check_budget_sync(self, user_id: int, media_type: str, quantity: int = 1) -> Tuple[bool, Dict[str, Any]]:
        """Synchronous version of check_budget for use in sync contexts."""
        import asyncio
        try:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self.check_budget(user_id, media_type, quantity))
                        return future.result()
                else:
                    return loop.run_until_complete(self.check_budget(user_id, media_type, quantity))
            except RuntimeError:
                return asyncio.run(self.check_budget(user_id, media_type, quantity))
        except Exception as e:
            logger.error(f"Error in sync check_budget: {e}")
            return False, {"error": str(e)}

    def deduct_quota_and_budget_sync(self, user_id: int, media_type: str, quantity: int = 1, resource_id: str = None, resource_type: str = None) -> bool:
        """Synchronous version of deduct_quota_and_budget for use in sync contexts."""
        import asyncio
        try:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self.deduct_quota_and_budget(user_id, media_type, quantity, resource_id, resource_type))
                        return future.result()
                else:
                    return loop.run_until_complete(self.deduct_quota_and_budget(user_id, media_type, quantity, resource_id, resource_type))
            except RuntimeError:
                return asyncio.run(self.deduct_quota_and_budget(user_id, media_type, quantity, resource_id, resource_type))
        except Exception as e:
            logger.error(f"Error in sync deduct_quota_and_budget: {e}")
            return False


# Global instance
quota_service = QuotaService()
