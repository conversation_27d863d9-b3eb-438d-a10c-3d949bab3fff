"""
Billing API router for ProductVideo platform.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Header
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from .service import billing_service
from .stripe_service import stripe_service
from .credit_service import credit_service
from .schemas import (
    Tenant, TenantCreate, TenantUpdate,
    Subscription, CreateSubscriptionRequest, CreateSubscriptionResponse,
    BillingUsage, RecordUsageRequest, RecordUsageResponse,
    UsageSummary, BillingDashboardData,
    Invoice, PaymentMethod
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/tenants", response_model=Tenant)
async def create_tenant(
    tenant_data: TenantCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new tenant."""
    try:
        tenant = await billing_service.create_tenant(db, tenant_data)
        return tenant
    except Exception as e:
        logger.error(f"Error creating tenant: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}", response_model=Tenant)
async def get_tenant(
    tenant_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get tenant by ID."""
    tenant = await billing_service.get_by_uuid(db, tenant_id)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant


@router.put("/tenants/{tenant_id}", response_model=Tenant)
async def update_tenant(
    tenant_id: str,
    tenant_data: TenantUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update tenant."""
    tenant = await billing_service.update_by_uuid(db, tenant_id, tenant_data)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant


@router.get("/tenants", response_model=List[Tenant])
async def list_tenants(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """List tenants."""
    tenants = await billing_service.get_multi(db, skip=skip, limit=limit)
    return tenants


@router.post("/subscriptions", response_model=CreateSubscriptionResponse)
async def create_subscription(
    request: CreateSubscriptionRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a subscription for a tenant."""
    try:
        response = await stripe_service.create_subscription(db, request)
        return response
    except Exception as e:
        logger.error(f"Error creating subscription: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/usage", response_model=RecordUsageResponse)
async def record_usage(
    request: RecordUsageRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Record usage for a tenant."""
    try:
        response = await stripe_service.record_usage(db, request)
        return response
    except Exception as e:
        logger.error(f"Error recording usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/usage", response_model=UsageSummary)
async def get_usage_summary(
    tenant_id: str,  # Now expects UUID string
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get usage summary for a tenant."""
    try:
        # Get tenant by UUID to get database ID
        tenant = await billing_service.get_by_uuid(db, tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        summary = await billing_service.get_usage_summary(db, tenant.id)
        return summary
    except Exception as e:
        logger.error(f"Error getting usage summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/dashboard", response_model=BillingDashboardData)
async def get_billing_dashboard(
    tenant_id: str,  # Now expects UUID string
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get billing dashboard data for a tenant."""
    try:
        # Get tenant by UUID to get database ID
        tenant = await billing_service.get_by_uuid(db, tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        dashboard_data = await billing_service.get_billing_dashboard_data(db, tenant.id)
        return dashboard_data
    except Exception as e:
        logger.error(f"Error getting billing dashboard: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhooks/stripe")
async def stripe_webhook(
    request: Request,
    stripe_signature: str = Header(..., alias="stripe-signature"),
    db: AsyncSession = Depends(get_db),
):
    """Handle Stripe webhooks."""
    try:
        # Get raw body
        body = await request.body()
        
        # Process webhook
        result = await stripe_service.process_webhook(db, body, stripe_signature)
        
        return {"status": "success", "result": result}
        
    except ValueError as e:
        logger.error(f"Invalid Stripe webhook: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error processing Stripe webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/usage/check")
async def check_usage_limits(
    tenant_id: str,  # Now expects UUID string
    usage_type: str,
    quantity: float,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Check if tenant can use the requested quantity."""
    try:
        from .models import UsageType
        
        # Convert string to enum
        try:
            usage_type_enum = UsageType(usage_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid usage type: {usage_type}")
        
        result = await billing_service.check_usage_limits(
            db, tenant_id, usage_type_enum, quantity
        )
        return result
        
    except Exception as e:
        logger.error(f"Error checking usage limits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/usage/video-generation")
async def record_video_generation_usage(
    tenant_id: str,  # Now expects UUID string
    video_job_id: str,
    variant_count: int = 4,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Record video generation usage for a tenant."""
    try:
        usage_record = await billing_service.record_video_generation_usage(
            db, tenant_id, video_job_id, variant_count
        )
        return {"status": "success", "usage_record_id": usage_record.id}
        
    except Exception as e:
        logger.error(f"Error recording video generation usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/usage/storage")
async def record_storage_usage(
    tenant_id: str,  # Now expects UUID string
    storage_gb: float,
    resource_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Record storage usage for a tenant."""
    try:
        usage_record = await billing_service.record_storage_usage(
            db, tenant_id, storage_gb, resource_id
        )
        return {"status": "success", "usage_record_id": usage_record.id}
        
    except Exception as e:
        logger.error(f"Error recording storage usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Credit Management Endpoints

@router.get("/tenants/{tenant_id}/credits/balance")
async def get_credit_balance(
    tenant_id: str,  # Now expects UUID string
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get credit balance for a tenant."""
    try:
        balance = await credit_service.get_balance(tenant_id)
        return {"balance": balance}
    except Exception as e:
        logger.error(f"Error getting credit balance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tenants/{tenant_id}/credits/transactions")
async def get_credit_transactions(
    tenant_id: str,  # Now expects UUID string
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get credit transaction history for a tenant."""
    try:
        transactions = await credit_service.get_transaction_history(tenant_id, limit)
        return {"transactions": transactions}
    except Exception as e:
        logger.error(f"Error getting credit transactions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/credits/purchase")
async def purchase_credits(
    tenant_id: int,
    credit_amount: int,
    stripe_payment_id: str,
    description: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Purchase credits for a tenant."""
    try:
        success = await credit_service.purchase_credits(
            tenant_id=tenant_id,
            credit_amount=credit_amount,
            stripe_payment_id=stripe_payment_id,
            description=description
        )

        if not success:
            raise HTTPException(status_code=400, detail="Failed to purchase credits")

        return {"message": f"Successfully purchased {credit_amount} credits"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error purchasing credits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/credits/grant")
async def grant_subscription_credits(
    tenant_id: int,
    plan_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Grant subscription credits to a tenant (admin only)."""
    try:
        success = await credit_service.grant_subscription_credits(tenant_id, plan_type)

        if not success:
            raise HTTPException(status_code=400, detail="Failed to grant credits")

        return {"message": f"Successfully granted credits for {plan_type} plan"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error granting subscription credits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/credits/purchase-session")
async def create_credit_purchase_session(
    tenant_id: int,
    package_id: str,
    success_url: str,
    cancel_url: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Create a Stripe checkout session for credit purchase.

    Args:
        tenant_id: Tenant ID
        package_id: Credit package identifier (e.g., "credits_1000")
        success_url: URL to redirect on successful payment
        cancel_url: URL to redirect on cancelled payment
        db: Database session
        current_user: Current authenticated user

    Returns:
        Checkout session data
    """
    try:
        session_data = await stripe_service.create_credit_purchase_session(
            db=db,
            tenant_id=tenant_id,
            package_id=package_id,
            success_url=success_url,
            cancel_url=cancel_url
        )

        return session_data
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating credit purchase session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/credit-packages")
async def get_credit_packages():
    """
    Get available credit packages for purchase.

    Returns:
        List of available credit packages
    """
    try:
        packages = []
        for package_id, package_info in stripe_service.credit_packages.items():
            packages.append({
                "id": package_id,
                "credits": package_info["credits"],
                "amount": package_info["amount"],
                "price_id": package_info["price_id"],
            })

        return {"packages": packages}
    except Exception as e:
        logger.error(f"Error getting credit packages: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tenants/{tenant_id}/credits/renew")
async def renew_tenant_credits(
    tenant_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Manually renew subscription credits for a tenant (admin only).

    Args:
        tenant_id: Tenant ID
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    try:
        success = await credit_service.renew_subscription_credits(tenant_id)

        if not success:
            raise HTTPException(status_code=400, detail="Failed to renew credits")

        return {"message": f"Successfully renewed credits for tenant {tenant_id}"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error renewing credits for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/admin/credits/process-renewals")
async def process_monthly_renewals(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Process monthly credit renewals for all tenants (admin only).

    Args:
        db: Database session
        current_user: Current authenticated user

    Returns:
        Renewal results
    """
    try:
        renewed_count = await credit_service.process_monthly_renewals()

        return {
            "message": f"Processed monthly renewals for {renewed_count} tenants",
            "tenants_renewed": renewed_count
        }
    except Exception as e:
        logger.error(f"Error processing monthly renewals: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/admin/credits/clean-expired")
async def clean_expired_credits(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Clean up expired credits for all tenants (admin only).

    Args:
        db: Database session
        current_user: Current authenticated user

    Returns:
        Cleanup results
    """
    try:
        # This is handled automatically in get_balance, but we can trigger it manually
        # For now, just return success since cleanup happens on balance checks
        return {
            "message": "Expired credits are automatically cleaned up when balances are checked",
            "note": "Credit expiration is handled automatically in the get_balance method"
        }
    except Exception as e:
        logger.error(f"Error cleaning expired credits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


