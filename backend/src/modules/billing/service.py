"""
Billing service for ProductVideo platform.
High-level billing operations and business logic.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func

from core.services.base_service import BaseService
from modules.auth.models import Tenant
from .models import Subscription, BillingUsage, Invoice, PaymentMethod, UsageType, BillingPlan
from .schemas import (
    TenantCreate, TenantUpdate, Tenant as TenantSchema,
    BillingUsageCreate, UsageSummary, BillingDashboardData, CreditBalance,
    RecordUsageRequest
)
from .stripe_service import stripe_service

logger = logging.getLogger(__name__)


class BillingService(BaseService[Tenant, TenantCreate, TenantUpdate]):
    """Service for billing operations."""

    def __init__(self):
        super().__init__(Tenant)

    async def get_by_uuid(self, db: AsyncSession, uuid: str) -> Optional[Tenant]:
        """Get tenant by UUID."""
        result = await db.execute(select(Tenant).where(Tenant.uuid == uuid))
        return result.scalar_one_or_none()

    async def update_by_uuid(self, db: AsyncSession, uuid: str, tenant_data: TenantUpdate) -> Optional[Tenant]:
        """Update tenant by UUID."""
        tenant = await self.get_by_uuid(db, uuid)
        if not tenant:
            return None

        for key, value in tenant_data.model_dump(exclude_unset=True).items():
            setattr(tenant, key, value)

        await db.commit()
        # Note: No refresh needed - use external_id for same-session operations
        return tenant

    async def create_tenant(
        self, 
        db: AsyncSession, 
        tenant_data: TenantCreate,
        create_stripe_customer: bool = True
    ) -> Tenant:
        """
        Create a new tenant with optional Stripe customer.
        
        Args:
            db: Database session
            tenant_data: Tenant creation data
            create_stripe_customer: Whether to create Stripe customer
            
        Returns:
            Created tenant
        """
        # Create tenant
        tenant = Tenant(**tenant_data.model_dump())
        db.add(tenant)
        await db.commit()
        # Note: No refresh needed - use external_id for same-session operations
        
        # Create Stripe customer if requested
        if create_stripe_customer and tenant.billing_email:
            try:
                customer_id = await stripe_service.create_customer(tenant)
                tenant.stripe_customer_id = customer_id
                await db.commit()
                # Note: No refresh needed - use external_id for same-session operations
                logger.info(f"Created Stripe customer for tenant {tenant.id}")
            except Exception as e:
                logger.warning(f"Failed to create Stripe customer for tenant {tenant.id}: {e}")
        
        return tenant

    async def get_tenant_by_shop_domain(
        self,
        db: AsyncSession,
        shop_domain: str
    ) -> Optional[Tenant]:
        """Get tenant by shop domain."""
        result = await db.execute(
            select(Tenant).where(Tenant.shop_domain == shop_domain)
        )
        return result.scalar_one_or_none()

    async def get_tenant_by_stripe_customer(
        self, 
        db: AsyncSession, 
        customer_id: str
    ) -> Optional[Tenant]:
        """Get tenant by Stripe customer ID."""
        result = await db.execute(
            select(Tenant).where(Tenant.stripe_customer_id == customer_id)
        )
        return result.scalar_one_or_none()

    async def record_video_generation_usage(
        self,
        db: AsyncSession,
        tenant_id: int,
        video_job_id: str,
        variant_count: int = 4,
        metadata: Optional[Dict[str, Any]] = None
    ) -> BillingUsage:
        """
        Record video generation usage.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            video_job_id: Video job ID
            variant_count: Number of variants generated
            metadata: Additional metadata
            
        Returns:
            Created usage record
        """
        request = RecordUsageRequest(
            tenant_id=tenant_id,
            usage_type=UsageType.VIDEO_GENERATION,
            quantity=float(variant_count),
            resource_id=video_job_id,
            resource_type="video_job",
            metadata=metadata or {}
        )
        
        response = await stripe_service.record_usage(db, request)
        return response.usage_record

    async def record_storage_usage(
        self,
        db: AsyncSession,
        tenant_id: int,
        storage_gb: float,
        resource_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> BillingUsage:
        """
        Record storage usage.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            storage_gb: Storage used in GB
            resource_id: Resource ID (video ID, etc.)
            metadata: Additional metadata
            
        Returns:
            Created usage record
        """
        request = RecordUsageRequest(
            tenant_id=tenant_id,
            usage_type=UsageType.STORAGE_GB,
            quantity=storage_gb,
            resource_id=resource_id,
            resource_type="storage",
            metadata=metadata or {}
        )
        
        response = await stripe_service.record_usage(db, request)
        return response.usage_record

    async def get_usage_summary(
        self,
        db: AsyncSession,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> UsageSummary:
        """
        Get usage summary for a tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            start_date: Start date (defaults to current month start)
            end_date: End date (defaults to current month end)
            
        Returns:
            Usage summary
        """
        # Default to current month
        if not start_date:
            now = datetime.utcnow()
            start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        if not end_date:
            if start_date.month == 12:
                end_date = start_date.replace(year=start_date.year + 1, month=1) - timedelta(seconds=1)
            else:
                end_date = start_date.replace(month=start_date.month + 1) - timedelta(seconds=1)
        
        # Query usage records
        result = await db.execute(
            select(BillingUsage)
            .where(
                and_(
                    BillingUsage.tenant_id == tenant_id,
                    BillingUsage.billing_period_start >= start_date,
                    BillingUsage.billing_period_end <= end_date
                )
            )
        )
        usage_records = result.scalars().all()
        
        # Aggregate by usage type
        usage_by_type = {}
        total_cost = Decimal('0.00')
        
        for record in usage_records:
            usage_type = record.usage_type.value
            if usage_type not in usage_by_type:
                usage_by_type[usage_type] = 0.0
            
            usage_by_type[usage_type] += record.quantity
            
            if record.total_cost:
                total_cost += record.total_cost
        
        return UsageSummary(
            tenant_id=tenant_id,
            billing_period_start=start_date,
            billing_period_end=end_date,
            usage_by_type=usage_by_type,
            total_cost=total_cost,
            currency="usd"
        )

    async def get_billing_dashboard_data(
        self,
        db: AsyncSession,
        tenant_id: int
    ) -> BillingDashboardData:
        """
        Get comprehensive billing dashboard data.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            
        Returns:
            Billing dashboard data
        """
        # Get tenant
        tenant = await self.get(db, tenant_id)
        if not tenant:
            raise ValueError(f"Tenant {tenant_id} not found")
        
        # Get current subscription
        subscription_result = await db.execute(
            select(Subscription)
            .where(Subscription.tenant_id == tenant_id)
            .order_by(Subscription.created_at.desc())
        )
        current_subscription = subscription_result.scalar_one_or_none()
        
        # Get current month usage
        now = datetime.utcnow()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        usage_result = await db.execute(
            select(BillingUsage)
            .where(
                and_(
                    BillingUsage.tenant_id == tenant_id,
                    BillingUsage.billing_period_start >= month_start
                )
            )
            .order_by(BillingUsage.created_at.desc())
        )
        current_usage = usage_result.scalars().all()
        
        # Get recent invoices
        invoice_result = await db.execute(
            select(Invoice)
            .where(Invoice.tenant_id == tenant_id)
            .order_by(Invoice.invoice_date.desc())
            .limit(10)
        )
        recent_invoices = invoice_result.scalars().all()
        
        # Get payment methods
        payment_method_result = await db.execute(
            select(PaymentMethod)
            .where(
                and_(
                    PaymentMethod.tenant_id == tenant_id,
                    PaymentMethod.is_active == True
                )
            )
            .order_by(PaymentMethod.is_default.desc(), PaymentMethod.created_at.desc())
        )
        payment_methods = payment_method_result.scalars().all()
        
        # Get usage summary
        usage_summary = await self.get_usage_summary(db, tenant_id)

        # Get credit balance and recent transactions
        from .credit_service import credit_service
        credit_balance_value = await credit_service.get_balance(tenant_id)
        recent_credit_transactions = await credit_service.get_transaction_history(tenant_id, limit=10)

        # Create credit balance object
        credit_balance = None
        if credit_balance_value > 0:
            plan_tier = tenant.plan_tier
            monthly_grant = credit_service.default_credit_grants.get(plan_tier, 0)
            credit_balance = CreditBalance(
                current_balance=credit_balance_value,
                expires_at=tenant.credit_expires_at,
                plan_tier=plan_tier,
                monthly_grant=monthly_grant
            )

        return BillingDashboardData(
            tenant=tenant,
            current_subscription=current_subscription,
            current_usage=list(current_usage),
            recent_invoices=list(recent_invoices),
            payment_methods=list(payment_methods),
            usage_summary=usage_summary,
            credit_balance=credit_balance,
            recent_credit_transactions=recent_credit_transactions
        )

    async def check_usage_limits(
        self,
        db: AsyncSession,
        tenant_id: int,
        usage_type: UsageType,
        requested_quantity: float
    ) -> Dict[str, Any]:
        """
        Check if tenant can use the requested quantity.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            usage_type: Type of usage
            requested_quantity: Requested quantity
            
        Returns:
            Usage check result
        """
        # Get tenant and plan limits
        tenant = await self.get(db, tenant_id)
        if not tenant:
            raise ValueError(f"Tenant {tenant_id} not found")
        
        # Define plan limits
        plan_limits = {
            BillingPlan.STARTER: {
                UsageType.VIDEO_GENERATION: 10,  # 10 videos per month
                UsageType.STORAGE_GB: 5,  # 5 GB storage
            },
            BillingPlan.PROFESSIONAL: {
                UsageType.VIDEO_GENERATION: 100,  # 100 videos per month
                UsageType.STORAGE_GB: 50,  # 50 GB storage
            },
            BillingPlan.ENTERPRISE: {
                UsageType.VIDEO_GENERATION: float('inf'),  # Unlimited
                UsageType.STORAGE_GB: float('inf'),  # Unlimited
            }
        }
        
        limit = plan_limits.get(tenant.billing_plan, {}).get(usage_type, 0)
        
        # Get current usage
        usage_summary = await self.get_usage_summary(db, tenant_id)
        current_usage = usage_summary.usage_by_type.get(usage_type.value, 0.0)
        
        # Check if request would exceed limit
        total_after_request = current_usage + requested_quantity
        can_proceed = total_after_request <= limit
        
        return {
            "can_proceed": can_proceed,
            "current_usage": current_usage,
            "requested_quantity": requested_quantity,
            "limit": limit,
            "remaining": max(0, limit - current_usage),
            "would_exceed_by": max(0, total_after_request - limit) if not can_proceed else 0
        }


# Create service instance
billing_service = BillingService()
