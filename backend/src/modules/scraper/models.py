"""
Web scraper models for e-commerce data extraction.
"""

from sqlalchemy import <PERSON>umn, BigInteger, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum as PyEnum
import uuid

from core.db.database import Base


class ScrapingStatus(PyEnum):
    """Scraping status enumeration."""
    PENDING = "pending"
    SCRAPING = "scraping"
    COMPLETED = "completed"
    FAILED = "failed"


class JobStatus(PyEnum):
    """Job status enumeration."""
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScrapedDocument(Base):
    """
    Scraped document tracking.
    Each document represents a scraping session for a specific URL/domain.
    """
    __tablename__ = 'scraped_documents'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    workspace_id = Column(BigInteger, ForeignKey('tenants.id'), nullable=False)
    url = Column(String, nullable=False)
    domain = Column(String, nullable=False, index=True)
    title = Column(String, nullable=True)
    status = Column(Enum(ScrapingStatus), default=ScrapingStatus.PENDING, nullable=False)
    progress = Column(Float, default=0.0)
    product_count = Column(Integer, default=0)
    collection_count = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    
    # Scraping metadata
    scraping_metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    workspace = relationship("Tenant", back_populates="scraped_documents")
    jobs = relationship("ScrapingJob", back_populates="document")
    products = relationship("ScrapedProduct", back_populates="document")
    collections = relationship("ScrapedCollection", back_populates="document")

    def __repr__(self):
        return f"<ScrapedDocument(id='{self.id}', domain='{self.domain}', status='{self.status.value}')>"


class ScrapingJob(Base):
    """
    Individual scraping jobs within a document.
    """
    __tablename__ = 'scraping_jobs'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    document_id = Column(BigInteger, ForeignKey('scraped_documents.id'), nullable=False)
    url = Column(String, nullable=False)
    status = Column(Enum(JobStatus), default=JobStatus.QUEUED, nullable=False)
    progress = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    
    # Job metadata
    job_metadata = Column(JSON, nullable=True)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    document = relationship("ScrapedDocument", back_populates="jobs")

    def __repr__(self):
        return f"<ScrapingJob(id='{self.id}', url='{self.url}', status='{self.status.value}')>"


class ScrapedProduct(Base):
    """
    Products scraped from e-commerce sites.
    """
    __tablename__ = 'scraped_products'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    document_id = Column(BigInteger, ForeignKey('scraped_documents.id'), nullable=False)
    product_id = Column(String, nullable=False)  # External product ID
    title = Column(String, nullable=False)
    handle = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    vendor = Column(String, nullable=True)
    product_type = Column(String, nullable=True)
    tags = Column(JSON, nullable=True)  # Array of tags
    
    # Product data
    images = Column(JSON, nullable=True)  # Array of image objects
    variants = Column(JSON, nullable=True)  # Array of variant objects
    collections = Column(JSON, nullable=True)  # Array of collection objects
    
    # Source information
    url = Column(String, nullable=False)
    domain = Column(String, nullable=False, index=True)
    
    # Timestamps
    scraped_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    document = relationship("ScrapedDocument", back_populates="products")

    def __repr__(self):
        return f"<ScrapedProduct(id='{self.id}', title='{self.title}', domain='{self.domain}')>"


class ScrapedCollection(Base):
    """
    Collections scraped from e-commerce sites.
    """
    __tablename__ = 'scraped_collections'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    document_id = Column(BigInteger, ForeignKey('scraped_documents.id'), nullable=False)
    slug = Column(String, nullable=False)
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String, nullable=True)
    product_count = Column(Integer, default=0)
    
    # Source information
    domain = Column(String, nullable=False, index=True)
    
    # Timestamps
    scraped_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    document = relationship("ScrapedDocument", back_populates="collections")

    def __repr__(self):
        return f"<ScrapedCollection(id='{self.id}', title='{self.title}', domain='{self.domain}')>"


class ScrapingPlatform(Base):
    """
    Supported scraping platforms and their configurations.
    """
    __tablename__ = 'scraping_platforms'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False, unique=True)
    domains = Column(JSON, nullable=False)  # Array of supported domains
    features = Column(JSON, nullable=False)  # Array of supported features
    limitations = Column(JSON, nullable=True)  # Array of limitations
    is_active = Column(Boolean, default=True)
    
    # Configuration
    config = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<ScrapingPlatform(id={self.id}, name='{self.name}')>"
