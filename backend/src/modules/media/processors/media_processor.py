import asyncio
import logging
import mimetypes
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from sqlalchemy import select

from core.config import get_settings
from core.db.database import get_session_factory
from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.media.service import media_service
from modules.media.schemas import ProviderMediaRequest, ProviderMediaResult
from modules.storage.storage_service import media_storage_service
from modules.processing.transcoding_service import video_transcoding_service
from modules.billing.service import billing_service
from core.metrics import media_generation_duration, media_generation_failures

logger = logging.getLogger(__name__)
settings = get_settings()

class MediaProcessor:
    """Enhanced media processor with improved error handling and monitoring."""

    def __init__(self):
        pass

    @asynccontextmanager
    async def get_db_session(self):
        """Context manager for database sessions with proper event loop handling."""
        session_factory = get_session_factory()
        db = session_factory()
        try:
            yield db
        finally:
            await db.close()

    async def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process media generation job with enhanced error handling.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result
        """
        logger.info(f"DEBUG: MediaProcessor.process called with job_data: {job_data}")
        result = await self._process_async(job_data)
        logger.info(f"DEBUG: MediaProcessor.process returning result: {result}")
        return result

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation with comprehensive error handling and monitoring.
        """
        from modules.media.service import media_service
        from uuid import UUID

        tenant_id = job_data["tenant_id"]
        job_external_id = job_data["job_id"]  # This should be external_id now
        product_ids = job_data.get("product_ids", [])
        template_id = job_data.get("template_id")
        media_type = job_data.get("media_type", "video")

        start_time = datetime.now(timezone.utc)
        logger.info(f"Processing {media_type} generation job {job_external_id} for tenant {tenant_id}")

        try:
            async with self.get_db_session() as db:
                # Get and validate media job by external_id
                media_job = await media_service.get_by_external_id(db, job_external_id)
                if not media_job:
                    raise ValueError(f"Media job {job_external_id} not found")

                # Update job status to processing
                media_job.status = MediaJobStatus.PROCESSING
                media_job.started_at = start_time
                db_job_id = media_job.id
                await db.commit()

                # Process products with error tracking
                successful_products = 0
                failed_products = 0
                total_variants = 0

                for product_id in product_ids:
                    try:
                        variants_count = await self._generate_media_for_product(
                            db, media_job, product_id, job_data
                        )
                        successful_products += 1
                        total_variants += variants_count
                        logger.info(f"Successfully processed product {product_id}: {variants_count} variants")
                    except Exception as e:
                        failed_products += 1
                        logger.error(f"Failed to process product {product_id}: {e}")
                        # Continue with other products

                # Calculate billing based on actual variants generated
                if total_variants > 0:
                    try:
                        await billing_service.record_video_generation_usage(
                            db, tenant_id, str(db_job_id), total_variants
                        )
                    except Exception as e:
                        logger.warning(f"Failed to record billing: {e}")

                # Update final job status
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()

                if failed_products == 0:
                    media_job.status = MediaJobStatus.COMPLETED
                    media_job.completed_at = end_time
                    logger.info(f"Media generation job {db_job_id} completed successfully")
                elif successful_products > 0:
                    media_job.status = MediaJobStatus.COMPLETED  # Use COMPLETED for partial success
                    media_job.completed_at = end_time
                    media_job.error_message = f"Completed {successful_products}/{len(product_ids)} products"
                    logger.warning(f"Media generation job {db_job_id} partially completed")
                else:
                    media_job.status = MediaJobStatus.FAILED
                    media_job.error_message = "All products failed to process"
                    logger.error(f"Media generation job {db_job_id} failed completely")

                await db.commit()

                # Record metrics
                media_generation_duration.labels(
                    media_type=media_type,
                    status=media_job.status.value
                ).observe(duration)

                return {
                    "success": media_job.status == MediaJobStatus.COMPLETED,
                    "job_id": db_job_id,
                    "products_processed": successful_products,
                    "products_failed": failed_products,
                    "variants_generated": total_variants,
                    "duration_seconds": duration,
                    "status": media_job.status.value
                }

        except Exception as e:
            logger.error(f"Critical error in media generation job {job_external_id}: {e}")

            # Update job status on critical failure
            try:
                async with self.get_db_session() as db:
                    media_job = await media_service.get_by_external_id(db, job_external_id)
                    if media_job:
                        media_job.status = MediaJobStatus.FAILED
                        media_job.error_message = f"Critical error: {str(e)}"
                        media_job.completed_at = datetime.now(timezone.utc)
                        await db.commit()

                        # Record failure metrics
                        media_generation_failures.labels(
                            media_type=media_type,
                            failure_reason="critical_error"
                        ).inc()
            except Exception as db_error:
                logger.error(f"Failed to update job status: {db_error}")

            raise e
    
    async def _generate_media_for_product(
        self,
        db,
        media_job: MediaJob,
        product_id: str,
        job_data: Dict[str, Any]
    ) -> int:
        """
        Generate media variants for a single product with enhanced error handling.

        Args:
            db: Database session
            media_job: Media job instance
            product_id: Product ID to generate for
            job_data: Complete job configuration

        Returns:
            Number of variants successfully generated
        """
        media_type = job_data.get("media_type", "video")
        template_id = job_data.get("template_id")

        logger.info(f"DEBUG: Generating {media_type} for product {product_id}, job_id: {media_job.id}")

        # Build generation request with flexible configuration
        custom_config = job_data.get("custom_config", {})
        variants_count = custom_config.get("variants_count", 4)

        request = ProviderMediaRequest(
            product_title=str(product_id),  # Convert to string
            media_type=media_type,
            template_id=template_id,
            custom_config=custom_config,
            num_images=variants_count if media_type == "image" else 4,
            variants_count=variants_count,
            aspect_ratio=custom_config.get("aspect_ratio", "16:9"),
            style="professional"
        )

        logger.info(f"DEBUG: Created ProviderMediaRequest: media_type={request.media_type}, model={request.model}")

        # Generate media using unified media service with retry logic
        max_retries = 3
        result = None
        for attempt in range(max_retries):
            try:
                # Provider override checking is now handled in the manager
                resolved_provider_name = request.model
                logger.info(f"DEBUG: Attempt {attempt + 1}: Using provider {resolved_provider_name}")

                # Use the unified media service to generate media
                result = await media_service.generate_media_with_provider(
                    provider_name=resolved_provider_name,
                    request=request
                )
                logger.info(f"DEBUG: Provider result: success={result.success}, error={result.error_message}")
                break
            except Exception as e:
                logger.error(f"DEBUG: Generation attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise Exception(f"Generation failed after {max_retries} attempts: {e}")
                logger.warning(f"Generation attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

        if not result or not result.success:
            error_msg = result.error_message if result else "No result returned"
            logger.error(f"DEBUG: Media generation failed for product {product_id}: {error_msg}")
            raise Exception(f"{media_type} generation failed: {error_msg}")

        # Process variants
        successful_variants = 0
        variants_data = result.variants or result.images or []
        logger.info(f"DEBUG: Processing {len(variants_data)} variants for product {product_id}")

        # Get existing variants for this job
        existing_variants = (await db.execute(
            select(MediaVariant).filter(MediaVariant.job_id == media_job.id)
        )).scalars().all()
        logger.info(f"DEBUG: Found {len(existing_variants)} existing variants for job {media_job.id}")

        for i, variant_data in enumerate(variants_data):
            logger.info(f"DEBUG: Processing variant {i+1}/{len(variants_data)}: {variant_data.get('variant_name', f'variant_{i+1}')}")
            if i < len(existing_variants):
                # Update existing variant
                variant = existing_variants[i]
                variant.variant_name = variant_data.get("variant_name", variant.variant_name)
                variant.status = MediaVariantStatus.PROCESSING
                variant.provider_media_id = variant_data.get("id")
                variant.provider_metadata = variant_data
                variant.provider = resolved_provider_name
                variant.duration_seconds = variant_data.get("duration_seconds")  # Extract duration_seconds directly
                logger.info(f"DEBUG: Updated existing variant {variant.id}")
            else:
                # Create new variant if we have more variants than expected
                variant = MediaVariant(
                    job_id=media_job.id,
                    user_id=media_job.user_id,
                    variant_name=variant_data.get("variant_name", f"Variant {i+1}"),
                    status=MediaVariantStatus.PROCESSING,
                    provider_media_id=variant_data.get("id"),
                    provider_metadata=variant_data,
                    provider=resolved_provider_name,
                    duration_seconds=variant_data.get("duration_seconds")
                )
                db.add(variant)
                logger.info(f"DEBUG: Created new variant for job {media_job.id}")

            try:
                # Use user_id as tenant_id since MediaJob doesn't have tenant_id
                await self._process_variant_media(
                    variant, variant_data, media_job.user_id, media_type, custom_config
                )
                variant.status = MediaVariantStatus.COMPLETED
                successful_variants += 1
                logger.info(f"DEBUG: Successfully processed variant {variant.id}")

            except Exception as e:
                logger.error(f"DEBUG: Failed to process variant {variant.id}: {e}")
                variant.status = MediaVariantStatus.FAILED
                variant.error_message = str(e)

        await db.commit()
        logger.info(f"DEBUG: Generated and processed {successful_variants}/{len(variants_data)} variants for product {product_id}")

        return successful_variants

    async def _process_variant_media(
        self,
        variant: MediaVariant,
        variant_data: Dict[str, Any],
        tenant_id: int,
        media_type: str,
        custom_config: Dict[str, Any]
    ):
        """Process and store media for a variant."""
        if media_type == "text":
            # Text generation doesn't have URLs, just store the text content
            await self._process_text_variant(variant, variant_data, tenant_id)
        else:
            # Video and image generation have URLs
            # Extract media URL directly from variant_data (provider result)
            # This is simpler and more reliable than accessing provider_metadata
            media_url = variant_data.get("video_url") or variant_data.get("image_url")

            if not media_url:
                logger.error(f"No media URL found in variant_data: {variant_data}")
                raise ValueError(f"No media URL provided in variant data for {media_type}")

            if media_type == "video":
                await self._process_video_variant(variant, media_url, tenant_id, custom_config)
            elif media_type == "image":
                await self._process_image_variant(variant, media_url, tenant_id)
            else:
                raise ValueError(f"Unsupported media type for processing: {media_type}")

    async def _process_video_variant(
        self,
        variant: MediaVariant,
        video_url: str,
        tenant_id: int,
        custom_config: Dict[str, Any]
    ):
        """Process video variant - providers already handle storage."""
        try:
            # Extract thumbnail_url and duration_seconds from provider metadata if available
            provider_data = variant.provider_metadata or {}
            thumbnail_url = provider_data.get('thumbnail_url')
            duration_seconds = provider_data.get('duration_seconds')

            # Store extracted data
            if thumbnail_url:
                variant.thumbnail_url = thumbnail_url
            if duration_seconds:
                variant.duration_seconds = duration_seconds

            # For test providers, just use the URLs directly without complex processing
            # Providers already uploaded to storage and returned storage URLs
            variant.video_url = video_url
            logger.info(f"Video variant {variant.id} processed with storage URL: {video_url}")

        except Exception as e:
            logger.error(f"Failed to process video variant {variant.id}: {e}")
            raise

    async def _process_image_variant(
        self,
        variant: MediaVariant,
        image_url: str,
        tenant_id: int
    ):
        """Process image variant - providers already handle storage."""
        try:
            # Extract thumbnail_url and other metadata from provider data if available
            provider_data = variant.provider_metadata or {}
            thumbnail_url = provider_data.get('thumbnail_url')
            duration_seconds = provider_data.get('duration_seconds')
            resolution = provider_data.get('resolution')
            alt_text = provider_data.get('alt_text')

            # Store extracted data
            if thumbnail_url:
                variant.thumbnail_url = thumbnail_url
            if duration_seconds:
                variant.duration_seconds = duration_seconds
            if resolution:
                variant.resolution = resolution
            if alt_text:
                variant.alt_text = alt_text

            # Providers already uploaded to storage and returned storage URLs
            # Just store the URL directly without redundant download/upload
            variant.image_url = image_url
            logger.info(f"Image variant {variant.id} processed with storage URL: {image_url}")

        except Exception as e:
            logger.error(f"Failed to process image variant {variant.id}: {e}")
            raise

    async def _process_text_variant(
        self,
        variant: MediaVariant,
        variant_data: Dict[str, Any],
        tenant_id: int
    ):
        """Process text variant - store the generated text content."""
        try:
            # Extract text content from variant_data
            text_content = variant_data.get("text", "")

            if not text_content:
                logger.error(f"No text content found in variant_data: {variant_data}")
                raise ValueError("No text content provided in variant data")

            # Store the text content in the variant
            variant.text_content = text_content

            # Store additional metadata if available
            if "content_type" in variant_data:
                # You could store content_type in provider_metadata or a separate field
                # For now, we'll just log it
                logger.info(f"Text variant {variant.id} processed with content type: {variant_data['content_type']}")

            if "word_count" in variant_data:
                logger.info(f"Text variant {variant.id} has {variant_data['word_count']} words")

            logger.info(f"Text variant {variant.id} processed with {len(text_content)} characters")

        except Exception as e:
            logger.error(f"Failed to process text variant {variant.id}: {e}")
            raise


    async def _upload_processed_media(
        self,
        tenant_id: int,
        variant_id: int,
        processing_result: dict,
        media_type: str
    ) -> dict:
        """Upload processed media files to storage."""
        storage_results = {}
        
        try:
            if media_type == "video":
                # Upload MP4 video
                if 'mp4' in processing_result['outputs']:
                    mp4_path = processing_result['outputs']['mp4']
                    mp4_key = f"tenants/{tenant_id}/videos/{variant_id}/video.mp4"
                    
                    # Read content from file
                    with open(mp4_path, 'rb') as f:
                        mp4_content = f.read()

                    media_file = await media_storage_service.upload_media(
                        tenant_id=tenant_id,
                        media_content=mp4_content,
                        filename=os.path.basename(mp4_key),
                        content_type="video/mp4"
                    )
                    mp4_url = media_file.public_url
                    storage_results['mp4_url'] = mp4_url
                
                # Upload HLS playlist and segments
                if 'hls' in processing_result['outputs']:
                    hls_path = processing_result['outputs']['hls']
                    hls_dir = os.path.dirname(hls_path)
                    
                    # Upload all HLS files
                    hls_urls = []
                    for file_name in os.listdir(hls_dir):
                        file_path = os.path.join(hls_dir, file_name)
                        hls_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/{file_name}"
                        
                        # Read content from file
                        with open(file_path, 'rb') as f:
                            hls_content = f.read()

                        media_file = await media_storage_service.upload_media(
                            tenant_id=tenant_id,
                            media_content=hls_content,
                            filename=os.path.basename(hls_key),
                            content_type=mimetypes.guess_type(file_name)[0] or "application/octet-stream" # Guess content type
                        )
                        hls_urls.append(media_file.public_url)
                    
                    # The main playlist URL
                    playlist_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/playlist.m3u8"
                    storage_results['hls_url'] = f"{media_storage_service.base_url}/{playlist_key}"
                
                # Upload thumbnails
                thumbnail_urls = []
                for i, thumb_path in enumerate(processing_result['thumbnails']):
                    thumb_key = f"tenants/{tenant_id}/videos/{variant_id}/thumbnails/thumb_{i+1}.jpg"
                    
                    # Read content from file
                    with open(thumb_path, 'rb') as f:
                        thumb_content = f.read()

                    media_file = await media_storage_service.upload_media(
                        tenant_id=tenant_id,
                        media_content=thumb_content,
                        filename=os.path.basename(thumb_key),
                        content_type="image/jpeg"
                    )
                    thumbnail_urls.append(media_file.public_url)
                storage_results['thumbnail_urls'] = thumbnail_urls
                
                # Upload subtitles
                if processing_result['subtitles']:
                    subtitle_path = processing_result['subtitles']
                    subtitle_key = f"tenants/{tenant_id}/videos/{variant_id}/subtitles.srt"
                    
                    # Read content from file
                    with open(subtitle_path, 'rb') as f:
                        subtitle_content = f.read()

                    media_file = await media_storage_service.upload_media(
                        tenant_id=tenant_id,
                        media_content=subtitle_content,
                        filename=os.path.basename(subtitle_key),
                        content_type="application/x-subrip" # SRT content type
                    )
                    subtitle_url = media_file.public_url
                    storage_results['subtitle_url'] = subtitle_url
            elif media_type == "image":
                # Image processing results would be different, handle accordingly
                pass
            
            return storage_results
            
        except Exception as e:
            logger.error(f"Failed to upload processed media files: {e}")
            raise
