"""
Media Generation API Router
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.db.database import get_db, get_fresh_db

from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.queue.queue_service import celery_service as job_queue_service, TaskPriority
from .models import (
    MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
)
from .schemas import (
    MediaGenerateRequest, MediaGenerateResponse, MediaJobStatusResponse,
    MediaPushRequest, MediaPushResponse, MediaJobListResponse,
    MediaVariantInfo, MediaJobInfo
)
from .service import media_service
from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/generate", response_model=MediaGenerateResponse)
async def generate_videos(
    request: MediaGenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_fresh_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate 4 video variants for selected products.

    Body: {shopId, productIds[], templateId?, aspectRatio?, locale?}
    Returns: job IDs per product-variant
    """
    try:
        # Validate request has required fields
        if not request.items or len(request.items) == 0:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="At least one product item is required for media generation"
            )

        # Prevent creation of too many jobs in a single request (safeguard against infinite loops)
        max_items = 50  # Limit to prevent excessive job creation
        if len(request.items) > max_items:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Too many items in request. Maximum allowed: {max_items}, received: {len(request.items)}"
            )

        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Validate shop ownership
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if hasattr(request, 'shop_id') and request.shop_id:
            if request.shop_id not in store_ids:
                raise HTTPException(
                    status_code=403,
                    detail="Access denied: shop does not belong to user"
                )

        # Create video generation jobs with full payload
        jobs = await media_service.create_generation_jobs(
            db=db,
            user_id=current_user.id,  # Use user ID directly
            request=request
        )

        # Extract all needed attributes before commit to avoid lazy loading issues
        job_data_list = []
        for job in jobs:
            job_data_list.append({
                "external_id": str(job.external_id),
                "product_id": job.product_id,
                "media_type": job.media_type,
                "template_id": job.template_id,
                "full_payload": job.full_payload,
                "status": job.status
            })

        # Commit the jobs to ensure they are persisted
        await db.commit()

        # Check quota and budget before queuing tasks
        from modules.billing.quota_service import quota_service
        media_type = request.media_type or request.mode or "image"
        total_items = len(request.items or [])

        # Check quota and budget before queuing tasks
        from modules.billing.quota_service import quota_service
        media_type = request.media_type or request.mode or "image"
        total_items = len(request.items or [])

        try:
            has_quota, quota_info = await quota_service.check_quota(db, current_user.id, media_type, total_items)
            if not has_quota:
                quota_type = quota_info.get('quota_type', 'unknown')

                # Handle service errors - do not proceed when quota service is unavailable
                if quota_type == 'service_error':
                    logger.error(f"Quota service temporarily unavailable for user {current_user.id}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="Quota service is temporarily unavailable. Please try again later."
                    )
                else:
                    logger.error(f"Quota check failed for user {current_user.id}: has_quota={has_quota}, quota_info={quota_info}")
                    raise HTTPException(
                        status_code=status.HTTP_402_PAYMENT_REQUIRED,
                        detail=f"Quota exceeded: {quota_type} limit reached"
                    )
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Unexpected error during quota check for user {current_user.id}: {e}")
            # For unexpected errors, allow the request to proceed but log the issue
            logger.warning(f"Proceeding with media generation due to quota service error: {e}")
            has_quota = True  # Allow request to proceed

        try:
            has_budget, budget_info = await quota_service.check_budget(db, current_user.id, media_type, total_items)
            if not has_budget:
                budget_type = budget_info.get('budget_type', 'unknown')

                # Handle service errors gracefully for budget check
                if budget_type == 'unknown' and 'error' in budget_info:
                    logger.warning(f"Budget service temporarily unavailable for user {current_user.id}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="Budget service is temporarily unavailable. Please try again later."
                    )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_402_PAYMENT_REQUIRED,
                        detail=f"Budget exceeded: {budget_type}"
                    )
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Unexpected error during budget check for user {current_user.id}: {e}")
            # For unexpected errors, allow the request to proceed but log the issue
            logger.warning(f"Proceeding with media generation due to budget service error: {e}")

        # Deduct quota and budget immediately since we've validated availability
        # This ensures billing happens even if the async worker has issues
        deduction_success = await quota_service.deduct_quota_and_budget(db, current_user.id, media_type, total_items)
        if not deduction_success:
            logger.error(f"Failed to deduct quota/budget for user {current_user.id} after validation")
            # Continue anyway since we already validated availability

        # Queue generation tasks using Celery
        from modules.queue.queue_service import celery_service

        job_responses = []
        for i, job_data in enumerate(job_data_list):
            # Enqueue the task using Celery
            celery_task_id = celery_service.enqueue_media_generation(
                user_id=current_user.id,
                job_id=job_data["external_id"],  # Use extracted external_id
                product_ids=[job_data["product_id"]],
                media_type=job_data["media_type"],
                template_id=job_data["template_id"],
                full_payload=job_data["full_payload"]  # Pass the complete payload
            )

            job_responses.append({
                "product_id": job_data["product_id"],
                "job_id": job_data["external_id"], # Use extracted external_id for API responses
                "celery_task_id": celery_task_id,
                "status": job_data["status"].value
            })

        return MediaGenerateResponse(jobs=job_responses)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.exception(f"Error generating videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}", response_model=MediaJobStatusResponse)
async def get_job_status(
    job_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status, progress, and variant IDs.
    """
    try:
        logger.info(f"DEBUG: API endpoint /jobs/{job_id} called by user {current_user.id}")

        job = await media_service.get_job_with_variants_by_external_id(db, job_id)
        if not job:
            logger.warning(f"DEBUG: Job {job_id} not found in API call")
            raise HTTPException(status_code=404, detail="Job not found")

        # Add ownership validation
        # Check if job belongs to current user
        if hasattr(job, 'user_id') and job.user_id != current_user.id:
            logger.warning(f"DEBUG: Access denied for job {job_id} - user {current_user.id} vs job owner {job.user_id}")
            raise HTTPException(
                status_code=403,
                detail="Access denied: job does not belong to user"
            )

        logger.info(f"DEBUG: API get_job_status for job {job_id}: status={job.status.value}, progress={job.progress_percentage}")
        if hasattr(job, 'variants'):
            for variant in job.variants:
                logger.info(f"DEBUG: Variant {variant.id}: name={variant.variant_name}, status={variant.status.value}")

        # Create response object
        variants_data = []
        if hasattr(job, 'variants'):
            for variant in job.variants:
                print(f"ROUTER_DEBUG: variant {variant.id} duration_seconds: {variant.duration_seconds}")
                variant_dict = {
                    "variant_id": str(variant.external_id),
                    "variant_name": variant.variant_name,
                    "status": variant.status.value,
                    "video_url": variant.video_url,
                    "image_url": variant.image_url,
                    "thumbnail_url": variant.thumbnail_url,
                    "duration_seconds": variant.duration_seconds
                }
                variants_data.append(variant_dict)
                logger.info(f"DEBUG: Added variant to response: {variant_dict}")

        response = MediaJobStatusResponse(
            job_id=str(job.external_id),
            status=job.status.value,
            progress=job.progress_percentage,
            variants=variants_data
        )

        logger.info(f"DEBUG: API response object created: job_id={response.job_id}, status={response.status}, progress={response.progress}")
        logger.info(f"DEBUG: API response variants: {len(response.variants)}")
        for i, v in enumerate(response.variants):
            logger.info(f"DEBUG: Response variant {i}: {v}")

        # Convert to dict to see final output
        response_dict = response.dict()
        logger.info(f"DEBUG: Final response dict: {response_dict}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}/status", response_model=MediaJobStatusResponse)
async def get_job_status_detailed(
    job_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get detailed job status with progress and variants.
    """
    try:
        logger.info(f"DEBUG: API endpoint /jobs/{job_id}/status called by user {current_user.id}")

        logger.info(f"DEBUG: Calling media_service.get_job_status_by_external_id for job {job_id}")
        status_data = await media_service.get_job_status_by_external_id(db, job_id, current_user.id)
        if not status_data:
            logger.warning(f"DEBUG: Job {job_id} status not found for user {current_user.id}")
            raise HTTPException(status_code=404, detail="Job not found")

        logger.info(f"DEBUG: Service returned status_data: {status_data}")
        logger.info(f"DEBUG: API returning detailed status for job {job_id}: status={status_data['status']}, progress={status_data['progress']}, variants={len(status_data['variants'])}")

        response = MediaJobStatusResponse(**status_data)
        logger.info(f"DEBUG: Created response object: status={response.status}, variants={len(response.variants)}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}/fresh", response_model=MediaJobStatusResponse)
async def get_job_status_fresh(
    job_id: str,
    db: AsyncSession = Depends(get_fresh_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status with maximum freshness (expires all data after each operation).
    Use this endpoint for critical real-time operations.
    """
    try:
        logger.info(f"DEBUG: API endpoint /jobs/{job_id}/fresh called by user {current_user.id} - MAXIMUM FRESHNESS MODE")

        job = await media_service.get_job_with_variants_by_external_id(db, job_id)
        if not job:
            logger.warning(f"DEBUG: Job {job_id} not found in fresh mode")
            raise HTTPException(status_code=404, detail="Job not found")

        # Add ownership validation
        if hasattr(job, 'user_id') and job.user_id != current_user.id:
            logger.warning(f"DEBUG: Access denied for job {job_id} in fresh mode - user {current_user.id} vs job owner {job.user_id}")
            raise HTTPException(
                status_code=403,
                detail="Access denied: job does not belong to user"
            )

        logger.info(f"DEBUG: Fresh mode - Retrieved job {job_id}: status={job.status.value}, progress={job.progress_percentage}")

        # Create response object
        variants_data = []
        if hasattr(job, 'variants'):
            for variant in job.variants:
                variant_dict = {
                    "variant_id": str(variant.external_id),
                    "variant_name": variant.variant_name,
                    "status": variant.status.value,
                    "video_url": variant.video_url,
                    "image_url": variant.image_url,
                    "thumbnail_url": variant.thumbnail_url,
                    "duration_seconds": variant.duration_seconds
                }
                variants_data.append(variant_dict)
                logger.info(f"DEBUG: Fresh mode - Variant {variant.id}: status={variant.status.value}")

        response = MediaJobStatusResponse(
            job_id=str(job.external_id),
            status=job.status.value,
            progress=job.progress_percentage,
            variants=variants_data
        )

        logger.info(f"DEBUG: Fresh mode - Final response: job_status={response.status}, progress={response.progress}, variant_count={len(response.variants)}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting fresh job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/push", response_model=MediaPushResponse)
async def push_to_platform(
    request: MediaPushRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Push selected video variant to connected store's product media.

    Body: {shopId, productId, variantId, publishTargets, publishOptions}
    """
    try:
        # Validate store ownership and permissions
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if request.shop_id not in store_ids:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        # Find the store to determine platform
        store_result = await db.execute(select(Store).filter(
            Store.id == request.shop_id,
            Store.owner_id == current_user.id
        ))
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=404,
                detail="Store not found"
            )

        # Use the service method for pushing
        result = await media_service.push_media_to_platforms(
            db=db,
            variant_external_id=request.variant_id,  # Now expects external_id string
            user_id=current_user.id,
            shop_domain=store.shop_domain, # Pass the shop_domain from the store object
            publish_targets=request.publish_targets,
            publish_options=request.publish_options
        )

        platform_name = store.platform.title()
        return MediaPushResponse(
            success=result["success"],
            results=result["results"],
            push_id=f"push_{request.variant_id}_{request.product_id}",
            status="completed" if result["success"] else "failed",
            message=f"Push to {platform_name} {'completed' if result['success'] else 'failed'}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pushing to platform: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=MediaJobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    product_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media generation jobs for the current user.

    Supports filtering by status and product ID, with pagination.
    """
    try:
        # Use the service method for consistency
        result = await media_service.list_user_jobs(
            db=db,
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            status_filter=status_filter,
            product_id=product_id
        )

        # Return the jobs as dictionaries (MediaJobListResponse expects List[Dict[str, Any]])
        return MediaJobListResponse(
            jobs=result["jobs"],
            total=result["total"],
            page=result["page"],
            per_page=result["per_page"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cancel a media generation job.
    """
    try:
        success = await media_service.cancel_job_by_external_id(db, job_id, current_user.id)
        if not success:
            raise HTTPException(status_code=404, detail="Job not found or cannot be cancelled")

        return {"message": "Job cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/jobs/{job_id}/retry")
async def retry_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retry a failed media generation job.
    """
    try:
        new_job = await media_service.retry_job_by_external_id(db, job_id, current_user.id)
        if not new_job:
            raise HTTPException(status_code=404, detail="Job not found or cannot be retried")

        return {
            "message": "Job retry initiated successfully",
            "new_job_id": str(new_job.external_id)  # Return external_id instead of database ID
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/variants/{variant_id}/regenerate")
async def regenerate_variant(
    variant_id: str,
    override_params: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Regenerate a specific media variant with optional parameter overrides.
    """
    try:
        # Get the variant to check ownership
        from sqlalchemy import select
        from modules.media.models import MediaVariant
        from uuid import UUID

        try:
            variant_uuid = UUID(variant_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid variant ID format")

        variant = await db.execute(
            select(MediaVariant).filter(
                MediaVariant.external_id == variant_uuid,
                MediaVariant.user_id == current_user.id
            )
        )
        variant = variant.scalar_one_or_none()

        if not variant:
            raise HTTPException(status_code=404, detail="Variant not found")

        # Get job external_id for regeneration
        job = await media_service.get_job_with_variants_by_external_id(db, str(variant.job.external_id))
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Regenerate the variant
        await media_service.regenerate_variant(
            job_external_id=str(job.external_id),
            variant_external_id=variant_id,
            override_params=override_params
        )

        return {
            "message": "Variant regeneration initiated successfully",
            "variant_id": variant_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error regenerating variant: {e}")
        raise HTTPException(status_code=500, detail=str(e))
