"""
Simple Media Provider Manager.
Direct access to providers by type with auto-initialization.
"""

import asyncio
import json
import logging
import os
from pathlib import Path
from typing import Dict, Optional, Any

from .base import BaseMediaProvider
from .config import ProviderConfig
from core.config import get_settings

# Direct imports of all provider classes
from .image.banana import BananaProvider
from .image.example_image import ExampleImageProvider
from .video.veo3 import Veo3Provider
from .video.example_video import ExampleVideoProvider
from .text.gemini import GeminiProvider
from .text.example_text import ExampleTextProvider


# Load configuration once at module level
config_path = Path(__file__).parent / "configs" / "providers_config.json"
with open(config_path, 'r') as f:
    config = json.load(f)

# Get settings instance for environment variables
settings = get_settings()

logger = logging.getLogger(__name__)
# Provider override mappings for quick access
PROVIDER_OVERRIDES = {
    "image": settings.IMAGE_PROVIDER_OVERRIDE,
    "video": settings.VIDEO_PROVIDER_OVERRIDE,
    "text": settings.TEXT_PROVIDER_OVERRIDE,
}


def _check_provider_override(media_type: str, requested_provider: Optional[str] = None) -> Optional[str]:
    """Check if there's a provider override for the given media type."""
    # Check environment variables directly for dynamic override checking
    env_var_name = f"{media_type.upper()}_PROVIDER_OVERRIDE"
    override_provider = os.environ.get(env_var_name)

    if override_provider:
        logger.info(f"🔧 Using {env_var_name}: {override_provider} for {media_type} generation")
        return override_provider

    return requested_provider

# Replace environment variable placeholders in API keys
for provider_name, provider_config in config["providers"].items():
    api_key = provider_config.get("api_key", "")
    if api_key.startswith("${") and api_key.endswith("}"):
        env_var = api_key[2:-1]
        actual_key = getattr(settings, env_var, None)
        if actual_key:
            provider_config["api_key"] = actual_key

# Simple provider maps by type - initialized on first access
_text_providers: Dict[str, BaseMediaProvider] = {}
_image_providers: Dict[str, BaseMediaProvider] = {}
_video_providers: Dict[str, BaseMediaProvider] = {}

# Provider class mappings
TEXT_PROVIDER_CLASSES = {
    "gemini": GeminiProvider,
    "example_text": ExampleTextProvider,
}

IMAGE_PROVIDER_CLASSES = {
    "banana": BananaProvider,
    "example_image": ExampleImageProvider,
}

VIDEO_PROVIDER_CLASSES = {
    "veo3": Veo3Provider,
    "example_video": ExampleVideoProvider,
}


async def _initialize_all_providers():
    """Initialize all providers at module load time."""
    logger.info(f"Initializing all media providers. [TEXT: {TEXT_PROVIDER_CLASSES.keys()}], IMAGE: {IMAGE_PROVIDER_CLASSES.keys()}, VIDEO: {VIDEO_PROVIDER_CLASSES.keys()}]")

    # Initialize text providers
    for name, cls in TEXT_PROVIDER_CLASSES.items():
        try:
            provider = await _initialize_provider(name, cls)
            if provider:
                _text_providers[name] = provider
        except Exception as e:
            logger.warning(f"Failed to initialize text provider {name}: {e}")

    # Initialize image providers
    for name, cls in IMAGE_PROVIDER_CLASSES.items():
        try:
            provider = await _initialize_provider(name, cls)
            if provider:
                _image_providers[name] = provider
        except Exception as e:
            logger.warning(f"Failed to initialize image provider {name}: {e}")

    # Initialize video providers
    for name, cls in VIDEO_PROVIDER_CLASSES.items():
        try:
            provider = await _initialize_provider(name, cls)
            if provider:
                _video_providers[name] = provider
        except Exception as e:
            logger.warning(f"Failed to initialize video provider {name}: {e}")

    logger.info(f"Successfully initialized providers: [TEXT: {_text_providers.keys()}], IMAGE: {_image_providers.keys()}, VIDEO: {_video_providers.keys()}]")


async def _initialize_provider(provider_name: str, provider_class: type) -> Optional[BaseMediaProvider]:
    """Initialize a single provider with its configuration."""
    try:
        # Get config for this provider
        provider_config_data = config["providers"].get(provider_name)
        if not provider_config_data:
            logger.warning(f"No configuration found for provider: {provider_name}")
            return None

        # Create provider config object
        provider_config = ProviderConfig(name=provider_name, **provider_config_data)

        # Create and initialize provider instance
        provider = provider_class()
        success = await provider.initialize(provider_config)

        if success:
            # Set storage service for direct uploads
            try:
                # Lazy import to avoid circular imports
                from ...storage.storage_service import media_storage_service
                provider.set_storage_service(media_storage_service)
            except Exception as e:
                logger.warning(f"Failed to set storage service for {provider_name}: {e}")
            logger.info(f"Successfully initialized provider: {provider_name}")
            return provider
        else:
            logger.error(f"Failed to initialize provider: {provider_name}")
            return None

    except Exception as e:
        logger.error(f"Error initializing provider {provider_name}: {e}")
        return None


async def get_text_provider(provider_name: str) -> BaseMediaProvider:
    """Get a text provider by name, initializing if needed. Checks for overrides."""
    # Check for provider override
    resolved_provider_name = _check_provider_override("text", provider_name)

    if resolved_provider_name not in _text_providers:
        provider_class = TEXT_PROVIDER_CLASSES.get(resolved_provider_name)
        if not provider_class:
            available = list(TEXT_PROVIDER_CLASSES.keys())
            raise ValueError(f"Text provider '{resolved_provider_name}' not found. Available: {available}")

        provider = await _initialize_provider(resolved_provider_name, provider_class)
        if not provider:
            raise RuntimeError(f"Failed to initialize text provider: {resolved_provider_name}")

        _text_providers[resolved_provider_name] = provider

    return _text_providers[resolved_provider_name]


async def get_image_provider(provider_name: str) -> BaseMediaProvider:
    """Get an image provider by name, initializing if needed. Checks for overrides."""
    # Check for provider override
    resolved_provider_name = _check_provider_override("image", provider_name)

    if resolved_provider_name not in _image_providers:
        provider_class = IMAGE_PROVIDER_CLASSES.get(resolved_provider_name)
        if not provider_class:
            available = list(IMAGE_PROVIDER_CLASSES.keys())
            raise ValueError(f"Image provider '{resolved_provider_name}' not found. Available: {available}")

        provider = await _initialize_provider(resolved_provider_name, provider_class)
        if not provider:
            raise RuntimeError(f"Failed to initialize image provider: {resolved_provider_name}")

        _image_providers[resolved_provider_name] = provider

    return _image_providers[resolved_provider_name]


async def get_video_provider(provider_name: str) -> BaseMediaProvider:
    """Get a video provider by name, initializing if needed. Checks for overrides."""
    # Check for provider override
    resolved_provider_name = _check_provider_override("video", provider_name)
    logger.info(f"PROVIDER_RESOLUTION: get_video_provider - requested: {provider_name}, resolved: {resolved_provider_name}")

    if resolved_provider_name not in _video_providers:
        provider_class = VIDEO_PROVIDER_CLASSES.get(resolved_provider_name)
        if not provider_class:
            available = list(VIDEO_PROVIDER_CLASSES.keys())
            raise ValueError(f"Video provider '{resolved_provider_name}' not found. Available: {available}")

        provider = await _initialize_provider(resolved_provider_name, provider_class)
        if not provider:
            raise RuntimeError(f"Failed to initialize video provider: {resolved_provider_name}")

        _video_providers[resolved_provider_name] = provider

    return _video_providers[resolved_provider_name]


def get_available_providers(media_type: Optional[str] = None) -> Dict[str, list]:
    """Get available providers by media type."""
    if media_type == "text":
        return {"text": list(TEXT_PROVIDER_CLASSES.keys())}
    elif media_type == "image":
        return {"image": list(IMAGE_PROVIDER_CLASSES.keys())}
    elif media_type == "video":
        return {"video": list(VIDEO_PROVIDER_CLASSES.keys())}
    else:
        return {
            "text": list(TEXT_PROVIDER_CLASSES.keys()),
            "image": list(IMAGE_PROVIDER_CLASSES.keys()),
            "video": list(VIDEO_PROVIDER_CLASSES.keys()),
        }


async def get_provider(provider_name: str, media_type: str) -> BaseMediaProvider:
    """Get any provider by name and media type. Checks for overrides."""
    if media_type == "text":
        return await get_text_provider(provider_name)
    elif media_type == "image":
        return await get_image_provider(provider_name)
    elif media_type == "video":
        return await get_video_provider(provider_name)
    else:
        raise ValueError(f"Unsupported media type: {media_type}")


# Backward compatibility - these will be deprecated
provider_registry = None
provider_manager = None

# Skip module-level initialization for now to avoid hanging tests
# Providers will be initialized on-demand when first requested
logger.info("Provider manager loaded - providers will be initialized on-demand")