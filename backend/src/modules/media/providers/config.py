"""
Configuration classes for media providers.
Contains all provider configuration dataclasses and the main ProviderConfig class.
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field


@dataclass
class ProviderCapabilities:
    """Provider capabilities configuration."""
    supported_formats: List[str]
    supported_styles: Optional[List[str]] = None
    supported_categories: Optional[List[str]] = None
    content_types: Optional[List[str]] = None
    supported_languages: Optional[List[str]] = None
    supported_aspect_ratios: Optional[List[str]] = None
    max_duration_seconds: Optional[int] = None
    supported_person_generation: Optional[List[str]] = None


@dataclass
class ProviderLimits:
    """Provider limits and quotas."""
    max_variants_per_request: int
    requests_per_minute: int
    requests_per_hour: int
    token_limits: Optional[Dict[str, int]] = None


@dataclass
class ProviderCosts:
    """Provider cost configuration."""
    cost_per_unit: float
    currency: str = "USD"


@dataclass
class ProviderQuality:
    """Provider quality metrics."""
    quality_score: float
    average_generation_time: int


@dataclass
class ProviderMetadata:
    """Provider metadata information."""
    provider_name: str
    description: str


@dataclass
class AspectRatio:
    """Aspect ratio dimensions."""
    width: int
    height: int


@dataclass
class TextGenerationParams:
    """Common text generation parameters."""
    temperature: float = 0.7
    top_p: Optional[float] = None
    top_k: Optional[int] = None
    max_tokens: Optional[int] = None
    presence_penalty: Optional[float] = None
    frequency_penalty: Optional[float] = None
    stop_sequences: Optional[List[str]] = None


@dataclass
class ImageGenerationParams:
    """Common image generation parameters."""
    quality: Optional[str] = None
    style: Optional[str] = None
    negative_prompt: Optional[str] = None
    guidance_scale: Optional[float] = None
    num_inference_steps: Optional[int] = None


@dataclass
class VideoGenerationParams:
    """Common video generation parameters."""
    resolution: Optional[str] = None
    fps: Optional[int] = None
    duration_seconds: Optional[int] = None
    codec: Optional[str] = None
    bitrate: Optional[str] = None


@dataclass
class ImageConfig:
    """Image-specific configuration."""
    generation_params: Optional[ImageGenerationParams] = None


@dataclass
class VideoConfig:
    """Video-specific configuration."""
    aspect_resolutions: Optional[Dict[str, str]] = None
    generation_params: Optional[VideoGenerationParams] = None


@dataclass
class TextConfig:
    """Text-specific configuration."""
    temperature_settings: Optional[Dict[str, float]] = None
    generation_params: Optional[TextGenerationParams] = None
    estimated_completion_time_per_variant: Optional[int] = None


class ProviderConfig:
    """Standardized configuration for media providers."""

    def __init__(
        self,
        name: str,
        type: str,
        api_key: str,
        timeout: int,
        model: str,
        capabilities: ProviderCapabilities,
        limits: ProviderLimits,
        costs: ProviderCosts,
        quality: ProviderQuality,
        features: List[str],
        metadata: ProviderMetadata,
        image_config: Optional[ImageConfig] = None,
        video_config: Optional[VideoConfig] = None,
        text_config: Optional[TextConfig] = None
    ):
        self.name = name
        self.type = type
        self.api_key = api_key
        self.timeout = timeout
        self.model = model
        self.capabilities = capabilities
        self.limits = limits
        self.costs = costs
        self.quality = quality
        self.features = features
        self.metadata = metadata
        self.image_config = image_config
        self.video_config = video_config
        self.text_config = text_config

    @classmethod
    def from_dict(cls, name: str, config_dict: Dict[str, Any]) -> 'ProviderConfig':
        """Create ProviderConfig from dictionary (for loading from JSON)."""
        capabilities = ProviderCapabilities(
            supported_formats=config_dict.get("capabilities", {}).get("supported_formats", []),
            supported_styles=config_dict.get("capabilities", {}).get("supported_styles"),
            supported_categories=config_dict.get("capabilities", {}).get("supported_categories"),
            content_types=config_dict.get("capabilities", {}).get("content_types"),
            supported_languages=config_dict.get("capabilities", {}).get("supported_languages"),
            supported_aspect_ratios=config_dict.get("capabilities", {}).get("supported_aspect_ratios"),
            max_duration_seconds=config_dict.get("capabilities", {}).get("max_duration_seconds"),
            supported_person_generation=config_dict.get("capabilities", {}).get("supported_person_generation")
        )

        limits = ProviderLimits(
            max_variants_per_request=config_dict.get("limits", {}).get("max_variants_per_request", 4),
            requests_per_minute=config_dict.get("limits", {}).get("requests_per_minute", 10),
            requests_per_hour=config_dict.get("limits", {}).get("requests_per_hour", 100),
            token_limits=config_dict.get("limits", {}).get("token_limits")
        )

        costs = ProviderCosts(
            cost_per_unit=config_dict.get("costs", {}).get("cost_per_unit", 0.0),
            currency=config_dict.get("costs", {}).get("currency", "USD")
        )

        quality = ProviderQuality(
            quality_score=config_dict.get("quality", {}).get("quality_score", 0.8),
            average_generation_time=config_dict.get("quality", {}).get("average_generation_time", 30)
        )

        metadata = ProviderMetadata(
            provider_name=config_dict.get("metadata", {}).get("provider_name", name),
            description=config_dict.get("metadata", {}).get("description", "")
        )

        # Create media-specific configs
        image_config = None
        if config_dict.get("image_config"):
            # Create image generation params
            gen_params = None
            if config_dict["image_config"].get("generation_params"):
                params_dict = config_dict["image_config"]["generation_params"]
                gen_params = ImageGenerationParams(
                    quality=params_dict.get("quality"),
                    style=params_dict.get("style"),
                    negative_prompt=params_dict.get("negative_prompt"),
                    guidance_scale=params_dict.get("guidance_scale"),
                    num_inference_steps=params_dict.get("num_inference_steps")
                )

            image_config = ImageConfig(
                generation_params=gen_params
            )

        video_config = None
        if config_dict.get("video_config"):
            # Create video generation params
            gen_params = None
            if config_dict["video_config"].get("generation_params"):
                params_dict = config_dict["video_config"]["generation_params"]
                gen_params = VideoGenerationParams(
                    resolution=params_dict.get("resolution"),
                    fps=params_dict.get("fps"),
                    duration_seconds=params_dict.get("duration_seconds"),
                    codec=params_dict.get("codec"),
                    bitrate=params_dict.get("bitrate")
                )

            video_config = VideoConfig(
                aspect_resolutions=config_dict["video_config"].get("aspect_resolutions"),
                generation_params=gen_params
            )

        text_config = None
        if config_dict.get("text_config"):
            # Create text generation params
            gen_params = None
            if config_dict["text_config"].get("generation_params"):
                params_dict = config_dict["text_config"]["generation_params"]
                gen_params = TextGenerationParams(
                    temperature=params_dict.get("temperature", 0.7),
                    top_p=params_dict.get("top_p"),
                    top_k=params_dict.get("top_k"),
                    max_tokens=params_dict.get("max_tokens"),
                    presence_penalty=params_dict.get("presence_penalty"),
                    frequency_penalty=params_dict.get("frequency_penalty"),
                    stop_sequences=params_dict.get("stop_sequences")
                )

            text_config = TextConfig(
                temperature_settings=config_dict["text_config"].get("temperature_settings"),
                generation_params=gen_params,
                estimated_completion_time_per_variant=config_dict["text_config"].get("estimated_completion_time_per_variant")
            )

        return cls(
            name=name,
            type=config_dict.get("type", "unknown"),
            api_key=config_dict.get("api_key", ""),
            timeout=config_dict.get("timeout", 300),
            model=config_dict.get("model", ""),
            capabilities=capabilities,
            limits=limits,
            costs=costs,
            quality=quality,
            features=config_dict.get("features", []),
            metadata=metadata,
            image_config=image_config,
            video_config=video_config,
            text_config=text_config
        )