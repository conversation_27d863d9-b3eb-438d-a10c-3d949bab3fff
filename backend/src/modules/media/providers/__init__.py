"""
Media Providers
Simple AI provider access for media generation.
"""

from .manager import (
    get_text_provider,
    get_image_provider,
    get_video_provider,
    get_provider,
    get_available_providers,
    config,
    # Legacy exports for backward compatibility
    provider_registry,
    provider_manager,
)
from .base import (
    BaseMediaProvider,
    ImageProvider,
    TextProvider,
    VideoProvider
)
from .config import ProviderConfig

__all__ = [
    # New simple interface
    "get_text_provider",
    "get_image_provider",
    "get_video_provider",
    "get_provider",
    "get_available_providers",
    "config",
    # Base classes
    "BaseMediaProvider",
    "ImageProvider",
    "TextProvider",
    "VideoProvider",
    "ProviderConfig",
    # Legacy (deprecated)
    "provider_registry",
    "provider_manager",
]