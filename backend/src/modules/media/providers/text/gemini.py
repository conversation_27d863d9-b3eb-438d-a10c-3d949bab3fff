"""
Gemini AI Provider Plugin for Text Generation.
Provides comprehensive text generation and content creation using Google's Gemini AI.
Specialized for product descriptions, marketing copy, SEO content, and social media captions.
"""

import logging
from typing import Dict, List, Optional, Any

import httpx
from google import genai
from google.genai import types

from ..base import TextProvider
from ..config import ProviderConfig
from ...schemas import ProviderMediaRequest, ProviderMediaResult
from ...engines.prompt_engine import prompt_engine
from ...common.context_creation import create_product_context, create_brand_context
from core.config import get_settings

logger = logging.getLogger(__name__)


class GeminiProvider(TextProvider):
    """Gemini AI provider plugin for text generation and content creation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.http_client: Optional[httpx.AsyncClient] = None
        self.config: Optional[ProviderConfig] = None
        self.model = None  # Will be set from config during initialization


    @property
    def provider_name(self) -> str:
        return "gemini"


    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Gemini provider."""
        # Get API key from settings
        settings = get_settings()
        api_key = settings.GEMINI_API_KEY

        if not api_key:
            logger.error("GEMINI_API_KEY not configured in settings")
            return False

        try:
            # Initialize Google GenAI client
            self.client = genai.Client(api_key=api_key)

            # Additional Gemini-specific initialization
            import httpx
            self.http_client = httpx.AsyncClient(timeout=config.timeout)

            # Store config and get model from config - required, no fallback
            self.config = config
            self.model = config.model

            logger.info(f"Initialized Gemini provider with model: {self.model}")
            return True

        except KeyError as e:
            logger.error(f"Missing required configuration: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to initialize Gemini provider: {e}")
            return False

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate text content using Gemini AI."""
        if not self.client or not self.config:
            return ProviderMediaResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            if request.media_type == "text":
                return await self._generate_text_content(request)
            else:
                return ProviderMediaResult(
                    success=False,
                    error_message=f"Unsupported media type: {request.media_type}. This provider only supports text generation."
                )

        except Exception as e:
            logger.error(f"Gemini text generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )

    async def _generate_text_content(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce text content using Gemini."""
        try:
            # Get content types from configuration with fallback
            content_types = self.config.capabilities.content_types or ["product_description"]

            # Generate prompts using the base class method (now supports multiple content types)
            prompts = await self._generate_professional_prompts(request, content_types)

            generated_content = []

            for prompt_data in prompts:
                content_type = prompt_data["content_type"]
                prompt = prompt_data["prompt"]

                contents = [types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)]
                )]

                # Get temperature with safe fallbacks
                temperature = 0.7  # Default temperature
                try:
                    if self.config.text_config and self.config.text_config.generation_params:
                        temperature = self.config.text_config.generation_params.temperature
                    elif self.config.text_config and self.config.text_config.temperature_settings:
                        temp_settings = self.config.text_config.temperature_settings
                        temperature = temp_settings.get(content_type, temp_settings.get('default', 0.7))
                except Exception as e:
                    logger.warning(f"Failed to get temperature from config, using default: {e}")

                # For now, don't set max_output_tokens to avoid the MAX_TOKENS issue
                # The config has token limits, but Gemini 2.5 Flash seems to have issues with them
                config = types.GenerateContentConfig(
                    temperature=temperature,
                    # max_output_tokens will be None - let the model decide
                )

                response = self.client.models.generate_content(
                    model=self.model,
                    contents=contents,
                    config=config
                )

                # Extract text from Gemini response
                generated_text = ""
                try:
                    if response.candidates and len(response.candidates) > 0:
                        candidate = response.candidates[0]
                        if candidate.content and candidate.content.parts:
                            for part in candidate.content.parts:
                                if hasattr(part, 'text') and part.text:
                                    generated_text = part.text
                                    break

                    if not generated_text:
                        logger.error(f"Could not extract text from Gemini response: {response}")
                        generated_text = "Error: Could not extract generated text from response"

                except Exception as e:
                    logger.error(f"Failed to extract text from Gemini response: {e}")
                    generated_text = f"Error: Could not extract generated text from response: {str(e)}"

                if not generated_text.strip():
                    generated_text = "Error: No text was generated"

                generated_content.append({
                    "content_type": content_type,
                    "text": generated_text.strip(),
                    "word_count": len(generated_text.split()),
                    "character_count": len(generated_text),
                    "variant_name": f"{content_type}_variant"
                })

            # Get estimated completion time with safe fallback
            time_per_variant = 10
            try:
                if self.config.text_config:
                    time_per_variant = self.config.text_config.estimated_completion_time_per_variant or 10
            except Exception as e:
                logger.warning(f"Failed to get completion time from config, using default: {e}")

            return ProviderMediaResult(
                success=True,
                provider_job_id=f"gemini_text_{hash(request.product_title)}",
                variants=generated_content,
                estimated_completion_time=len(content_types) * time_per_variant
            )

        except Exception as e:
            logger.error(f"Error generating text with Gemini: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )


    async def download_media(self, media_url: str) -> bytes:
        """Download media from Gemini."""
        if not self.http_client:
            raise ValueError("Provider not initialized")

        response = await self.http_client.get(media_url)
        response.raise_for_status()
        return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Gemini provider information."""
        return {
            "name": self.config.metadata.provider_name,
            "description": self.config.metadata.description,
            "supported_formats": self.config.capabilities.supported_formats,
            "models": [self.config.model],
            "max_text_variants_per_request": self.config.limits.max_variants_per_request,
            "supported_content_types": self.config.capabilities.content_types,
            "supported_languages": self.config.capabilities.supported_languages,
            "features": self.config.features,
            "estimated_cost_per_request": self.config.costs.cost_per_unit,
            "average_generation_time": self.config.quality.average_generation_time,
            "quality_score": self.config.quality.quality_score
        }


    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            self.client = None
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info("Cleaned up Gemini provider")