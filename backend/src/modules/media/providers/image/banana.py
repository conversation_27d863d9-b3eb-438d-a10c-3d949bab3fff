"""
Google Gemini Image Provider Plugin for E-commerce Media Generation.
Provides professional product image generation using Google's Gemini 2.5 Flash Image Preview.
Specialized for e-commerce product photography, lifestyle images, and promotional content.
"""

import asyncio
import base64
import logging
import mimetypes
import os
from typing import Dict, List, Optional, Any
from io import BytesIO

from google import genai
from google.genai import types
from PIL import Image

from ..base import ImageProvider
from ..config import ProviderConfig
from core.config import get_settings
from ...schemas import ProviderMediaRequest, ProviderMediaResult, ProductCategory
from ...engines.context_engine import context_engine
from ...engines.prompt_engine import prompt_engine, MediaType, PromptContext, Platform

logger = logging.getLogger(__name__)


class BananaProvider(ImageProvider):
    """Google Gemini provider plugin for image generation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.config: Optional[ProviderConfig] = None
        self.model = None  # Will be set from config during initialization

    @property
    def provider_name(self) -> str:
        return "banana"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Banana provider."""
        # Get API key from settings
        settings = get_settings()
        api_key = settings.BANANA_API_KEY

        if not api_key:
            logger.error("BANANA_API_KEY not configured in settings")
            return False

        try:
            # Initialize Google GenAI client
            self.client = genai.Client(api_key=api_key)

            # Store config and get model from config - required, no fallback
            self.config = config
            self.model = config.model

            logger.info(f"Initialized Banana provider with model: {self.model}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Banana provider: {e}")
            return False


    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce images using Google Gemini."""
        if not self.client or not self.config:
            return ProviderMediaResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            # Use custom prompt if provided, otherwise generate professional prompts
            if request.custom_prompt:
                prompts = [{"prompt": request.custom_prompt, "style": "custom", "variant_name": "custom_1", "style_type": "professional"}]
            else:
                prompts = await self._generate_professional_prompts(request)

            # Generate images for each prompt variant
            all_images = []
            last_finish_reason = None
            last_response_text = None

            for i, prompt_data in enumerate(prompts):
                # Create content parts
                content_parts = []

                # Add reference images first (if provided)
                if request.reference_images:
                    for image_input in request.reference_images:
                        try:
                            # Decode base64 to bytes
                            image_bytes = base64.b64decode(image_input.bytesBase64Encoded)

                            # Create inline data part
                            inline_data = types.Blob(
                                mime_type=image_input.mimeType,
                                data=image_bytes
                            )
                            content_parts.append(types.Part(inline_data=inline_data))
                            logger.info(f"Added reference image with MIME type: {image_input.mimeType}")
                        except Exception as e:
                            logger.error(f"Failed to add reference image: {e}")
                            continue

                # Add text prompt
                content_parts.append(types.Part.from_text(text=prompt_data["prompt"]))

                # Create content for Gemini
                contents = [
                    types.Content(
                        role="user",
                        parts=content_parts,
                    ),
                ]

                generate_content_config = types.GenerateContentConfig(
                    response_modalities=[
                        "IMAGE",
                        "TEXT",
                    ],
                )

                # Generate image using streaming
                image_data = None
                file_index = 0
                finish_reason = None
                response_text = None

                for chunk in self.client.models.generate_content_stream(
                    model=self.model,
                    contents=contents,
                    config=generate_content_config,
                ):
                    # Capture finish reason if available
                    if chunk.candidates and chunk.candidates[0].finish_reason:
                        finish_reason = chunk.candidates[0].finish_reason
                        last_finish_reason = finish_reason  # Store for error reporting

                    # Capture any text response
                    if (chunk.candidates and chunk.candidates[0].content and
                        chunk.candidates[0].content.parts):
                        for part in chunk.candidates[0].content.parts:
                            if hasattr(part, 'text') and part.text:
                                if response_text is None:
                                    response_text = ""
                                response_text += part.text
                                last_response_text = response_text  # Store for error reporting

                    if (
                        chunk.candidates is None
                        or chunk.candidates[0].content is None
                        or chunk.candidates[0].content.parts is None
                    ):
                        continue

                    if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                        inline_data = chunk.candidates[0].content.parts[0].inline_data
                        data_buffer = inline_data.data

                        # Upload to storage instead of creating data URL
                        file_extension = mimetypes.guess_extension(inline_data.mime_type) or ".png"
                        filename = f"banana_image_{i}_{file_index}{file_extension}"

                        try:
                            # Upload main image to storage
                            image_url = await self._upload_media_to_storage(
                                media_content=data_buffer,
                                filename=filename,
                                content_type=inline_data.mime_type,
                                tenant_id=getattr(request, 'tenant_id', 1),
                                metadata={
                                    "provider": "gemini",
                                    "model": self.model,
                                    "style_type": prompt_data["style_type"],
                                    "prompt": prompt_data["prompt"]
                                }
                            )

                            # For now, use same URL for thumbnail (can be processed later)
                            thumbnail_url = image_url

                            image_data = {
                                "image_url": image_url,
                                "thumbnail_url": thumbnail_url,
                                "width": self._get_width_for_aspect(request.aspect_ratio),
                                "height": self._get_height_for_aspect(request.aspect_ratio),
                                "style": prompt_data["style"],
                                "variant_name": prompt_data["variant_name"],
                                "prompt_used": prompt_data["prompt"],
                                "generation_metadata": {
                                    "provider": "gemini",
                                    "model": self.model,
                                    "style_type": prompt_data["style_type"],
                                    "target_audience": prompt_data.get("target_audience"),
                                    "usage_context": prompt_data.get("usage_context"),
                                    "reference_images_used": len(request.reference_images) if request.reference_images else 0
                                }
                            }
                            break
                        except Exception as upload_error:
                            logger.error(f"Failed to upload image to storage: {upload_error}")
                            continue

                if image_data:
                    all_images.append(image_data)

                # Add small delay between requests to respect rate limits
                await asyncio.sleep(0.5)

            # Check if any images were actually generated
            if not all_images:
                # Try to get more details about why generation failed
                error_details = []
                error_details.append("🚨 EXACT ERROR: API call succeeded but no images were generated!")

                # Add captured response details
                if last_finish_reason:
                    error_details.append(f"🔍 Finish Reason: {last_finish_reason}")
                if last_response_text:
                    error_details.append(f"📝 API Response: {last_response_text[:200]}...")

                # Add information about the request
                error_details.append(f"🖼️  Reference images provided: {len(request.reference_images) if request.reference_images else 0}")
                error_details.append(f"📝 Prompt: {prompts[0]['prompt'][:100]}...")
                error_details.append(f"🤖 Model: {self.model}")

                error_details.append("\n💡 POSSIBLE CAUSES:")
                error_details.append("• Content safety filtering (images may violate policies)")
                error_details.append("• Model doesn't support image generation with multiple references")
                error_details.append("• API key lacks image generation permissions")
                error_details.append("• Images are too large or in unsupported format")
                error_details.append("• Rate limiting or quota exceeded")

                detailed_error = "\n".join(error_details)

                logger.error(f"IMAGE GENERATION FAILED: {detailed_error}")
                return ProviderMediaResult(
                    success=False,
                    error_message=detailed_error,
                    provider_job_id=f"gemini_batch_{hash(request.product_title)}",
                    estimated_completion_time=len(prompts) * 30
                )

            # Log generation statistics
            logger.info(f"🎯 Generation Summary: Expected {len(prompts)} images, Generated {len(all_images)} images")
            for i, img_data in enumerate(all_images):
                logger.info(f"✅ Image {i+1}: variant_name='{img_data.get('variant_name')}', has_url={bool(img_data.get('image_url'))}")

            return ProviderMediaResult(
                success=True,
                provider_job_id=f"gemini_batch_{hash(request.product_title)}",
                variants=all_images[:request.num_images],  # Limit to requested number
                estimated_completion_time=len(prompts) * 30
            )

        except Exception as e:
            logger.error(f"Gemini image generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=str(e)
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download media from Gemini."""
        if not self.client:
            raise ValueError("Provider not initialized")

        # For data URLs, extract the binary data
        if media_url.startswith("data:"):
            header, encoded = media_url.split(",", 1)
            return base64.b64decode(encoded)

        # For regular URLs, download
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(media_url)
            response.raise_for_status()
            return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Gemini provider information."""
        return {
            "name": self.config.metadata.provider_name,
            "description": self.config.metadata.description,
            "supported_formats": self.config.capabilities.supported_formats,
            "models": [self.config.model],
            "max_images_per_request": self.config.limits.max_variants_per_request,
            "supported_aspect_ratios": self.config.capabilities.supported_aspect_ratios,
            "supported_styles": self.config.capabilities.supported_styles,
            "supported_categories": self.config.capabilities.supported_categories,
            "features": self.config.features,
            "estimated_cost_per_image": self.config.costs.cost_per_unit,
            "average_generation_time": self.config.quality.average_generation_time,
            "quality_score": self.config.quality.quality_score
        }

    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            # Google GenAI client doesn't need explicit cleanup
            self.client = None
            logger.info("Cleaned up Gemini provider")