# Simplified Media Provider System

This is a completely simplified media provider system that replaces the complex registry and manager architecture with direct, type-specific access.

## Key Benefits

- **Simple**: Direct function calls instead of complex initialization flows
- **Type-Safe**: Separate functions for text, image, and video providers
- **Auto-Initialize**: Providers initialize themselves on first access
- **Cached**: Providers are cached after first initialization
- **Clear Errors**: Helpful error messages for missing providers

## Usage

### Basic Usage

```python
from modules.media.providers import (
    get_text_provider,
    get_image_provider,
    get_video_provider,
    get_provider,
    get_available_providers
)

# Get available providers
providers = get_available_providers()
print(providers)
# Output: {
#   "text": ["gemini", "example_text"],
#   "image": ["banana", "example_image"],
#   "video": ["veo3", "example_video"]
# }

# Get specific providers by type
text_provider = await get_text_provider("gemini")
image_provider = await get_image_provider("banana")
video_provider = await get_video_provider("veo3")

# Generic interface
provider = await get_provider("gemini", "text")
```

### Media Generation

```python
from modules.media.schemas import ProviderMediaRequest

# Create request
request = ProviderMediaRequest(
    media_type="text",
    product_title="Running Shoes",
    product_description="Comfortable athletic footwear",
    num_images=1
)

# Generate content
text_provider = await get_text_provider("gemini")
result = await text_provider.generate_media(request)

if result.success:
    print(f"Generated: {result.media_items[0].content}")
```

## Available Providers

### Text Providers

- `gemini` - Google Gemini text generation
- `example_text` - Example/mock text provider

### Image Providers

- `banana` - Banana image generation service
- `example_image` - Example/mock image provider

### Video Providers

- `veo3` - Google Veo3 video generation
- `example_video` - Example/mock video provider

## Configuration

Providers are configured via `configs/providers_config.json`. Each provider needs:

```json
{
  "providers": {
    "gemini": {
      "api_key": "${GEMINI_API_KEY}",
      "base_url": "https://api.gemini.com",
      "enabled": true
    }
  }
}
```

Environment variables in `${VAR_NAME}` format are automatically resolved.

## Adding New Providers

1. Create your provider class extending `BaseMediaProvider`
2. Add it to the appropriate map in `manager.py`:

```python
TEXT_PROVIDER_CLASSES = {
    "gemini": GeminiProvider,
    "example_text": ExampleTextProvider,
    "your_new_provider": YourNewProvider,  # Add here
}
```

3. Add configuration to `providers_config.json`
4. That's it! No complex registration needed.

## Error Handling

The system provides clear error messages:

```python
# Invalid provider name
await get_text_provider("invalid")
# ValueError: Text provider 'invalid' not found. Available: ['gemini', 'example_text']

# Invalid media type
await get_provider("gemini", "invalid")
# ValueError: Unsupported media type: invalid

# Initialization failure
# RuntimeError: Failed to initialize text provider: gemini
```

## Performance

- **First access**: Provider initializes (may take a few ms)
- **Subsequent access**: Instant (cached)
- **Memory**: Only requested providers are loaded
- **Thread-safe**: Safe for concurrent access

This simplified system makes provider management much easier while maintaining all the functionality of the previous complex system.
