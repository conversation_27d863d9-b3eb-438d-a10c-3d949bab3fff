"""
Example Video Provider for demonstration purposes.
Returns test videos instead of calling external APIs.
"""

import os
import base64
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..base import VideoProvider

logger = logging.getLogger(__name__)
from ..config import ProviderConfig
from ...schemas import ProviderMediaRequest, ProviderMediaResult


class ExampleVideoProvider(VideoProvider):
    """Example video provider that returns test videos."""

    def __init__(self):
        super().__init__()

    @property
    def provider_name(self) -> str:
        return "example_video"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize example provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate example videos."""
        try:
            # Read test video
            test_variants = []
            local_dir = Path(__file__).parent
            video_file = local_dir / "veo3_video_0_with_image.mp4"

            # Generate as many variants as requested
            num_variants = request.variants_count or 4

            for i in range(num_variants):
                if video_file.exists():
                    # Read video file
                    with open(video_file, "rb") as f:
                        video_data = f.read()

                    try:
                        # Upload to storage
                        filename = f"example_video_{i}.mp4"
                        video_url = await self._upload_media_to_storage(
                            media_content=video_data,
                            filename=filename,
                            content_type="video/mp4",
                            tenant_id=getattr(request, 'tenant_id', 1),
                            metadata={
                                "provider": "example_video",
                                "model": "example_model",
                                "style_type": "product_showcase"
                            }
                        )

                        # Generate actual thumbnail from video
                        thumbnail_url = await self._generate_video_thumbnail(
                            video_data=video_data,
                            video_url=video_url,
                            filename=f"example_video_{i}_thumb.jpg",
                            tenant_id=getattr(request, 'tenant_id', 1)
                        )

                        # Extract actual duration from video
                        actual_duration = await self._extract_video_duration(video_data)

                        print(f"PROVIDER_DURATION: ExampleVideoProvider - Setting duration: {actual_duration} for variant_{i+1}")
                        logger.info(f"PROVIDER_DURATION: ExampleVideoProvider - Setting duration: {actual_duration} for variant_{i+1}")
                        test_variants.append({
                            "variant_name": f"variant_{i+1}",
                            "video_url": video_url,
                            "thumbnail_url": thumbnail_url,
                            "duration_seconds": actual_duration,
                            "resolution": "1920x1080",
                            "aspect_ratio": "16:9",
                            "style_type": "example_showcase",
                            "prompt_used": f"Example video prompt for {request.product_title}",
                            "generation_metadata": {
                                "provider": "example_video",
                                "model": "example_model",
                                "style_type": "product_showcase",
                                "target_audience": ["example"],
                                "platform_optimized": "web"
                            }
                        })
                    except Exception as upload_error:
                        logger.error(f"Failed to upload example video to storage: {upload_error}")
                        # Skip this video if upload fails
                        continue
                else:
                    logger.warning(f"Test video file not found: {video_file}")
                    break

            return ProviderMediaResult(
                success=True,
                provider_job_id="example_video_job_789",
                variants=test_variants,
                estimated_completion_time=10,
                quality_score=0.88
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Example video generation failed: {str(e)}"
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download example media."""
        if media_url.startswith("/") or media_url.startswith("./"):
            with open(media_url, "rb") as f:
                return f.read()
        else:
            # Mock download for URLs
            return b"example video content"

    async def _generate_video_thumbnail(self, video_data: bytes, video_url: str, filename: str, tenant_id: int) -> str:
        """Generate a thumbnail from video data."""
        try:
            # For now, create a simple placeholder thumbnail
            # In a real implementation, you would use ffmpeg or similar to extract a frame
            from PIL import Image, ImageDraw
            import io

            # Create a simple placeholder image
            img = Image.new('RGB', (320, 180), color='gray')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), "Video Thumbnail", fill='white')

            # Convert to bytes
            thumb_buffer = io.BytesIO()
            img.save(thumb_buffer, format='JPEG')
            thumb_data = thumb_buffer.getvalue()

            # Upload thumbnail to storage
            thumbnail_url = await self._upload_media_to_storage(
                media_content=thumb_data,
                filename=filename,
                content_type="image/jpeg",
                tenant_id=tenant_id,
                metadata={"type": "thumbnail", "source": "video"}
            )

            return thumbnail_url

        except Exception as e:
            logger.warning(f"Failed to generate thumbnail: {e}")
            # Fallback to placeholder URL
            return video_url.replace('.mp4', '_thumb.jpg')

    async def _extract_video_duration(self, video_data: bytes) -> float:
        """Extract duration from video data."""
        try:
            # For now, return a default duration
            # In a real implementation, you would use ffprobe or similar to get actual duration
            return 8.0  # Default duration for test videos
        except Exception as e:
            logger.warning(f"Failed to extract video duration: {e}")
            return 8.0  # Fallback duration

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio."""
        resolution_map = {
            "1:1": "1024x1024",
            "16:9": "1920x1080",
            "9:16": "1080x1920",
            "4:5": "1024x1280",
            "3:4": "1024x1365"
        }
        return resolution_map.get(aspect_ratio, "1920x1080")