"""
Base provider classes for media generation.
Provides common functionality for Image, Text, and Video providers.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod

from ..schemas import ProviderMediaRequest, ProviderMediaResult
from ..engines.prompt_engine import prompt_engine, MediaType, PromptContext, GeneratedPrompt, Platform
from ..engines.context_engine import ProductContext, BrandContext
from ..common.context_creation import create_product_context, create_brand_context
from .config import (
    ProviderConfig, ProviderCapabilities, ProviderLimits, ProviderCosts,
    ProviderQuality, ProviderMetadata, AspectRatio, TextGenerationParams,
    ImageGenerationParams, VideoGenerationParams, ImageConfig, VideoConfig, TextConfig
)
from ...storage.storage_service import MediaStorageService

logger = logging.getLogger(__name__)



class BaseMediaProvider(ABC):
    """Base class for all media providers."""

    def __init__(self):
        self.client = None
        self.config: Optional[ProviderConfig] = None
        self.storage_service: Optional[MediaStorageService] = None

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Return the provider name."""
        pass

    @property
    @abstractmethod
    def supported_media_types(self) -> List[str]:
        """Return list of supported media types."""
        pass

    @abstractmethod
    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the provider."""
        pass

    @abstractmethod
    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate media content."""
        pass

    @abstractmethod
    async def download_media(self, media_url: str) -> bytes:
        """Download media from provider."""
        pass

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information."""
        return {
            "name": self.provider_name,
            "supported_formats": self.supported_media_types,
            "status": "initialized" if self.client else "not_initialized"
        }

    def set_storage_service(self, storage_service: MediaStorageService) -> None:
        """Set the storage service for media uploads."""
        self.storage_service = storage_service
        logger.info(f"Storage service set for {self.provider_name}")

    async def cleanup(self) -> None:
        """Cleanup provider resources."""
        if self.client:
            self.client = None
            logger.info(f"Cleaned up {self.provider_name}")


class ImageProvider(BaseMediaProvider):
    """Base class for image generation providers."""

    @property
    def supported_media_types(self) -> List[str]:
        return ["image"]

    async def _generate_professional_prompts(self, request: ProviderMediaRequest) -> List[Dict[str, Any]]:
        """Generate professional prompts for image generation with different styles."""
        prompts = []

        # If custom prompt is provided, use it
        if request.custom_prompt:
            return [{
                "prompt": request.custom_prompt,
                "style": request.style or "custom",
                "variant_name": "custom",
                "style_type": "custom"
            }]

        # Create product context from request
        product_context = request.product_context or create_product_context(request)

        # Generate different style variants for images
        style_variants = [
            {
                "media_type": MediaType.PRODUCT_PHOTOGRAPHY,
                "style_type": "product_photography",
                "variant_name": "product_shot",
                "platform": None
            },
            {
                "media_type": MediaType.LIFESTYLE_PHOTOGRAPHY,
                "style_type": "lifestyle",
                "variant_name": "lifestyle_shot",
                "platform": None
            },
            {
                "media_type": MediaType.PRODUCT_PHOTOGRAPHY,
                "style_type": "minimalist",
                "variant_name": "minimalist_shot",
                "platform": None
            },
            {
                "media_type": MediaType.LIFESTYLE_PHOTOGRAPHY,
                "style_type": "social_media",
                "variant_name": "social_shot",
                "platform": None
            }
        ]

        # Limit variants based on requested number
        style_variants = style_variants[:request.num_images]

        for variant in style_variants:
            try:
                # Create prompt context
                prompt_context = PromptContext(
                    media_type=variant["media_type"],
                    platform=variant.get("platform"),
                    aspect_ratio=request.aspect_ratio,
                    style_preference=getattr(request, 'content_style', None),
                    campaign_theme=getattr(request, 'campaign_theme', None),
                    call_to_action=getattr(request, 'call_to_action', None)
                )

                # Generate professional prompt
                brand_context = create_brand_context(request)
                generated_prompt = await prompt_engine.generate_prompt(
                    product_context=product_context,
                    prompt_context=prompt_context,
                    brand_context=brand_context
                )

                prompts.append({
                    "prompt": generated_prompt.main_prompt,
                    "negative_prompt": generated_prompt.negative_prompt,
                    "style": variant["style_type"],
                    "variant_name": variant["variant_name"],
                    "style_type": variant["style_type"],
                    "target_audience": [aud.value for aud in (product_context.target_audience or [])],
                    "usage_context": [ctx.value for ctx in (getattr(request, 'usage_context', None) or [])],
                    "quality_score": generated_prompt.estimated_quality_score
                })

            except Exception as e:
                logger.warning(f"Failed to generate prompt for variant {variant['variant_name']}: {e}")
                # Fallback to simple prompt
                prompts.append(self._create_fallback_prompt(request, variant))

        return prompts

    def _create_fallback_prompt(self, request: ProviderMediaRequest, variant: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback prompt when prompt engine fails."""
        base_prompt = f"Professional {variant['style_type']} photograph of {request.product_title}"

        if request.product_description:
            base_prompt += f", {request.product_description[:100]}"

        style_additions = {
            "product_photography": "clean white background, studio lighting, high resolution, commercial photography",
            "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment",
            "minimalist": "minimal background, clean composition, simple elegant styling",
            "social_media": "trendy, eye-catching, social media optimized, vibrant colors"
        }

        style_addition = style_additions.get(variant["style_type"], "professional photography")
        base_prompt += f", {style_addition}, 8K resolution, sharp focus, professional lighting"

        return {
            "prompt": base_prompt,
            "negative_prompt": "blurry, low quality, distorted, oversaturated, poor lighting, amateur",
            "style": variant["style_type"],
            "variant_name": variant["variant_name"],
            "style_type": variant["style_type"],
            "quality_score": 0.7
        }

    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio (default implementation)."""
        aspect_map = {
            "1:1": 1024,
            "16:9": 1024,
            "9:16": 576,
            "4:5": 768,
            "3:4": 768
        }
        return aspect_map.get(aspect_ratio, 1024)

    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio (default implementation)."""
        aspect_map = {
            "1:1": 1024,
            "16:9": 576,
            "9:16": 1024,
            "4:5": 960,
            "3:4": 1024
        }
        return aspect_map.get(aspect_ratio, 1024)

    def _get_settings(self):
        """Get settings (to be implemented by subclasses)."""
        # This should be implemented by subclasses that need settings
        raise NotImplementedError("Subclasses must implement _get_settings")

    async def _upload_media_to_storage(
        self,
        media_content: bytes,
        filename: str,
        content_type: str,
        tenant_id: int = 1,  # Default tenant ID
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Upload media content to storage and return the public URL."""
        if not self.storage_service:
            raise ValueError("Storage service not configured")

        media_file = await self.storage_service.upload_media(
            tenant_id=tenant_id,
            media_content=media_content,
            filename=filename,
            content_type=content_type,
            metadata=metadata
        )

        return media_file.public_url


class TextProvider(BaseMediaProvider):
    """Base class for text generation providers."""

    @property
    def supported_media_types(self) -> List[str]:
        return ["text"]

    async def _generate_professional_prompts(self, request: ProviderMediaRequest, content_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Generate professional prompts for text generation."""
        # Create product context
        product_context = request.product_context or create_product_context(request)
        brand_context = create_brand_context(request)

        # Get content types - support multiple types for comprehensive text generation
        if not content_types:
            # Fallback to single content type
            content_type = getattr(request, 'content_type', 'product_description')
            content_types = [content_type]

        prompts = []

        for content_type in content_types:
            # Generate text prompt using prompt engine
            try:
                text_prompt = await prompt_engine.generate_text_prompt(
                    product_context=product_context,
                    content_type=content_type,
                    brand_context=brand_context,
                    language=getattr(request, 'language', 'en')
                )

                prompts.append({
                    "prompt": text_prompt,
                    "content_type": content_type,
                    "language": getattr(request, 'language', 'en'),
                    "variant_name": f"{content_type}_variant",
                    "quality_score": 0.8
                })
            except Exception as e:
                logger.warning(f"Failed to generate text prompt for {content_type}: {e}")
                prompts.append(self._create_text_fallback_prompt(request, content_type))

        return prompts

    def _create_text_fallback_prompt(self, request: ProviderMediaRequest, content_type: str) -> Dict[str, Any]:
        """Create fallback text prompt."""
        base_prompt = f"Write a {content_type} for {request.product_title}"

        if request.product_description:
            base_prompt += f". Product description: {request.product_description[:200]}"

        base_prompt += ". Make it engaging, professional, and optimized for e-commerce."

        return {
            "prompt": base_prompt,
            "content_type": content_type,
            "language": "en",
            "variant_name": f"{content_type}_fallback",
            "quality_score": 0.6
        }


class VideoProvider(BaseMediaProvider):
    """Base class for video generation providers."""

    @property
    def supported_media_types(self) -> List[str]:
        return ["video"]

    async def _upload_media_to_storage(
        self,
        media_content: bytes,
        filename: str,
        content_type: str,
        tenant_id: int = 1,  # Default tenant ID
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Upload media content to storage and return the public URL."""
        if not self.storage_service:
            raise ValueError("Storage service not configured")

        media_file = await self.storage_service.upload_media(
            tenant_id=tenant_id,
            media_content=media_content,
            filename=filename,
            content_type=content_type,
            metadata=metadata
        )

        return media_file.public_url


    async def _generate_professional_prompts(self, request: ProviderMediaRequest) -> List[Dict[str, Any]]:
        """Generate professional prompts for video generation."""
        # Create product context
        product_context = request.product_context or create_product_context(request)
        brand_context = create_brand_context(request)

        # Video-specific variants
        video_variants = [
            {
                "media_type": MediaType.PRODUCT_VIDEO,
                "style_type": "product_showcase",
                "variant_name": "product_showcase",
                "aspect_ratio": "16:9",
                "duration_seconds": getattr(request, 'duration_seconds', 30),
                "platform": None
            },
            {
                "media_type": MediaType.LIFESTYLE_VIDEO,
                "style_type": "lifestyle_integration",
                "variant_name": "lifestyle_video",
                "aspect_ratio": "9:16",
                "duration_seconds": getattr(request, 'duration_seconds', 30),
                "platform": Platform.INSTAGRAM
            },
            {
                "media_type": MediaType.SOCIAL_VIDEO,
                "style_type": "social_media",
                "variant_name": "social_video",
                "aspect_ratio": "1:1",
                "duration_seconds": getattr(request, 'duration_seconds', 15),
                "platform": Platform.INSTAGRAM
            }
        ]

        # Limit to requested number
        video_variants = video_variants[:getattr(request, 'num_videos', 1)]

        prompts = []
        for variant in video_variants:
            try:
                # Create prompt context
                prompt_context = PromptContext(
                    media_type=variant["media_type"],
                    platform=variant.get("platform"),
                    aspect_ratio=variant["aspect_ratio"],
                    duration_seconds=variant["duration_seconds"],
                    style_preference=getattr(request, 'content_style', None),
                    campaign_theme=getattr(request, 'campaign_theme', None),
                    call_to_action=getattr(request, 'call_to_action', None)
                )

                # Generate video prompt
                generated_prompt = await prompt_engine.generate_prompt(
                    product_context=product_context,
                    prompt_context=prompt_context,
                    brand_context=brand_context
                )

                prompts.append({
                    "prompt": generated_prompt.main_prompt,
                    "style": variant["style_type"],
                    "variant_name": variant["variant_name"],
                    "duration_seconds": variant["duration_seconds"],
                    "aspect_ratio": variant["aspect_ratio"],
                    "style_type": self._get_style_type_for_concept(variant["style_type"]),
                    "camera_movements": self._get_camera_movements_for_concept(variant["style_type"]),
                    "lighting": self._get_lighting_for_concept(variant["style_type"]),
                    "music_style": self._get_music_style_for_concept(variant["style_type"]),
                    "quality_score": generated_prompt.estimated_quality_score
                })

            except Exception as e:
                logger.warning(f"Failed to generate video prompt for variant {variant['variant_name']}: {e}")
                prompts.append(self._create_video_fallback_prompt(request, variant))

        return prompts

    def _get_style_type_for_concept(self, concept_type: str) -> str:
        """Get style type for video concept."""
        style_map = {
            "product_showcase": "professional_showcase",
            "lifestyle_integration": "lifestyle_natural",
            "social_media": "dynamic_engaging",
            "360_showcase": "product_rotation"
        }
        return style_map.get(concept_type, "professional")

    def _get_camera_movements_for_concept(self, concept_type: str) -> str:
        """Get camera movements for video concept."""
        movement_map = {
            "product_showcase": "smooth zoom and pan",
            "lifestyle_integration": "handheld natural movement",
            "social_media": "dynamic quick cuts",
            "360_showcase": "360-degree rotation"
        }
        return movement_map.get(concept_type, "smooth")

    def _get_lighting_for_concept(self, concept_type: str) -> str:
        """Get lighting setup for video concept."""
        lighting_map = {
            "product_showcase": "professional studio lighting",
            "lifestyle_integration": "natural ambient lighting",
            "social_media": "bright vibrant lighting",
            "360_showcase": "even diffused lighting"
        }
        return lighting_map.get(concept_type, "professional")

    def _get_music_style_for_concept(self, concept_type: str) -> str:
        """Get music style for video concept."""
        music_map = {
            "product_showcase": "upbeat corporate",
            "lifestyle_integration": "ambient lifestyle",
            "social_media": "trendy upbeat",
            "360_showcase": "minimal ambient"
        }
        return music_map.get(concept_type, "upbeat")

    def _create_video_fallback_prompt(self, request: ProviderMediaRequest, variant: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback video prompt."""
        base_prompt = f"Create a professional {variant['style_type']} video for {request.product_title}"

        if request.product_description:
            base_prompt += f". Product details: {request.product_description[:150]}"

        base_prompt += f". Duration: {variant['duration_seconds']} seconds. Make it engaging and professional."

        return {
            "prompt": base_prompt,
            "style": variant["style_type"],
            "variant_name": variant["variant_name"],
            "duration_seconds": variant["duration_seconds"],
            "aspect_ratio": variant.get("aspect_ratio", "16:9"),
            "style_type": self._get_style_type_for_concept(variant["style_type"]),
            "camera_movements": self._get_camera_movements_for_concept(variant["style_type"]),
            "lighting": self._get_lighting_for_concept(variant["style_type"]),
            "music_style": self._get_music_style_for_concept(variant["style_type"]),
            "quality_score": 0.7
        }

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio (default implementation)."""
        resolution_map = {
            "1:1": "1024x1024",
            "16:9": "1920x1080",
            "9:16": "1080x1920",
            "4:5": "1024x1280",
            "3:4": "1024x1365"
        }
        return resolution_map.get(aspect_ratio, "1920x1080")