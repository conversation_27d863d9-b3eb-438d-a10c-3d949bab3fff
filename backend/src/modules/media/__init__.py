"""
Media Module
Provides AI-powered media services for ProductVideo platform.
Generic module that uses provider plugins for different AI services.
"""

# Lazy imports to avoid circular dependencies
def __getattr__(name):
    if name == "media_service":
        from .service import media_service
        return media_service
    elif name == "context_engine":
        from .engines.context_engine import context_engine
        return context_engine
    elif name == "prompt_engine":
        from .engines.prompt_engine import prompt_engine
        return prompt_engine
    elif name == "quality_engine":
        from .engines.quality_engine import quality_engine
        return quality_engine
    elif name == "provider_registry":
        from .providers.manager import provider_registry
        return provider_registry
    elif name == "provider_manager":
        from .providers.manager import provider_manager
        return provider_manager
    elif name == "config":
        from .providers.manager import config
        return config
    elif name == "models":
        import importlib
        return importlib.import_module(".models", __name__)
    elif name == "schemas":
        import importlib
        return importlib.import_module(".schemas", __name__)
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    # Services
    "media_service",

    # Engines
    "context_engine",
    "prompt_engine",
    "quality_engine",

    # Provider system
    "provider_registry",
    "provider_manager",
    "config",

    # Modules
    "models",
    "schemas",
]
