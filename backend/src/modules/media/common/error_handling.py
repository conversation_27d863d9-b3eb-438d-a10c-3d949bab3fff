"""
Enhanced Error Handling for Media Generation System.
Provides comprehensive error handling, retry logic, and monitoring.
"""

import logging
import traceback
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timezone
import asyncio

logger = logging.getLogger(__name__)


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories for classification."""
    PROVIDER_ERROR = "provider_error"
    NETWORK_ERROR = "network_error"
    VALIDATION_ERROR = "validation_error"
    QUOTA_ERROR = "quota_error"
    AUTHENTICATION_ERROR = "authentication_error"
    CONTENT_POLICY_ERROR = "content_policy_error"
    SYSTEM_ERROR = "system_error"
    TIMEOUT_ERROR = "timeout_error"


@dataclass
class MediaError:
    """Structured error information."""
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    provider: Optional[str] = None
    product_id: Optional[str] = None
    job_id: Optional[str] = None
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc)


class MediaErrorHandler:
    """Enhanced error handler for media generation operations."""
    
    def __init__(self):
        self.error_patterns = self._load_error_patterns()
        self.retry_strategies = self._load_retry_strategies()
    
    def classify_error(self, error: Exception, context: Dict[str, Any] = None) -> MediaError:
        """Classify an error and determine appropriate handling strategy."""
        error_message = str(error)
        error_type = type(error).__name__
        
        # Classify based on error patterns
        category = self._determine_category(error_message, error_type, context)
        severity = self._determine_severity(category, error_message)
        
        return MediaError(
            category=category,
            severity=severity,
            message=error_message,
            provider=context.get("provider") if context else None,
            product_id=context.get("product_id") if context else None,
            job_id=context.get("job_id") if context else None,
            error_code=getattr(error, 'code', None),
            details={
                "error_type": error_type,
                "traceback": traceback.format_exc(),
                "context": context or {}
            }
        )
    
    def _determine_category(self, message: str, error_type: str, context: Dict[str, Any] = None) -> ErrorCategory:
        """Determine error category based on message and type."""
        message_lower = message.lower()
        
        # Network-related errors
        if any(keyword in message_lower for keyword in ["connection", "timeout", "network", "dns"]):
            return ErrorCategory.NETWORK_ERROR
        
        # Authentication errors
        if any(keyword in message_lower for keyword in ["unauthorized", "authentication", "api key", "forbidden"]):
            return ErrorCategory.AUTHENTICATION_ERROR
        
        # Quota/rate limiting errors
        if any(keyword in message_lower for keyword in ["quota", "rate limit", "too many requests", "429"]):
            return ErrorCategory.QUOTA_ERROR
        
        # Content policy violations
        if any(keyword in message_lower for keyword in ["content policy", "safety", "inappropriate", "blocked"]):
            return ErrorCategory.CONTENT_POLICY_ERROR
        
        # Validation errors
        if any(keyword in message_lower for keyword in ["validation", "invalid", "required", "missing"]):
            return ErrorCategory.VALIDATION_ERROR
        
        # Timeout errors
        if any(keyword in message_lower for keyword in ["timeout", "timed out"]):
            return ErrorCategory.TIMEOUT_ERROR
        
        # Provider-specific errors
        if context and context.get("provider"):
            return ErrorCategory.PROVIDER_ERROR
        
        return ErrorCategory.SYSTEM_ERROR
    
    def _determine_severity(self, category: ErrorCategory, message: str) -> ErrorSeverity:
        """Determine error severity."""
        if category in [ErrorCategory.AUTHENTICATION_ERROR, ErrorCategory.SYSTEM_ERROR]:
            return ErrorSeverity.CRITICAL
        elif category in [ErrorCategory.PROVIDER_ERROR, ErrorCategory.QUOTA_ERROR]:
            return ErrorSeverity.HIGH
        elif category in [ErrorCategory.NETWORK_ERROR, ErrorCategory.TIMEOUT_ERROR]:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def should_retry(self, error: MediaError) -> bool:
        """Determine if an error should be retried."""
        if error.retry_count >= error.max_retries:
            return False
        
        # Don't retry authentication or validation errors
        if error.category in [ErrorCategory.AUTHENTICATION_ERROR, ErrorCategory.VALIDATION_ERROR]:
            return False
        
        # Don't retry content policy violations
        if error.category == ErrorCategory.CONTENT_POLICY_ERROR:
            return False
        
        return True
    
    def get_retry_delay(self, error: MediaError) -> float:
        """Get retry delay in seconds with exponential backoff."""
        base_delay = self.retry_strategies.get(error.category, 1.0)
        return base_delay * (2 ** error.retry_count)
    
    async def handle_error_with_retry(
        self,
        operation: Callable,
        context: Dict[str, Any],
        max_retries: int = 3
    ) -> Any:
        """Execute operation with automatic retry handling."""
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                return await operation()
            except Exception as e:
                media_error = self.classify_error(e, context)
                media_error.retry_count = attempt
                media_error.max_retries = max_retries
                
                last_error = media_error
                
                # Log the error
                logger.warning(
                    f"Operation failed (attempt {attempt + 1}/{max_retries + 1}): "
                    f"{media_error.category.value} - {media_error.message}",
                    extra={
                        "error_category": media_error.category.value,
                        "error_severity": media_error.severity.value,
                        "provider": media_error.provider,
                        "product_id": media_error.product_id,
                        "job_id": media_error.job_id
                    }
                )
                
                # Check if we should retry
                if not self.should_retry(media_error) or attempt == max_retries:
                    break
                
                # Wait before retry
                delay = self.get_retry_delay(media_error)
                await asyncio.sleep(delay)
        
        # All retries exhausted
        if last_error:
            logger.error(
                f"Operation failed after {max_retries + 1} attempts: {last_error.message}",
                extra={
                    "error_category": last_error.category.value,
                    "error_severity": last_error.severity.value,
                    "final_error": True
                }
            )
            raise Exception(f"Operation failed after retries: {last_error.message}")
    
    def _load_error_patterns(self) -> Dict[str, List[str]]:
        """Load error patterns for classification."""
        return {
            "provider_errors": [
                "provider unavailable",
                "service temporarily unavailable",
                "internal server error"
            ],
            "quota_errors": [
                "quota exceeded",
                "rate limit exceeded",
                "too many requests"
            ],
            "auth_errors": [
                "invalid api key",
                "unauthorized access",
                "authentication failed"
            ]
        }
    
    def _load_retry_strategies(self) -> Dict[ErrorCategory, float]:
        """Load retry strategies with base delays."""
        return {
            ErrorCategory.NETWORK_ERROR: 2.0,
            ErrorCategory.TIMEOUT_ERROR: 3.0,
            ErrorCategory.PROVIDER_ERROR: 5.0,
            ErrorCategory.QUOTA_ERROR: 10.0,
            ErrorCategory.SYSTEM_ERROR: 1.0
        }


# Global error handler instance
media_error_handler = MediaErrorHandler()
