"""
Language utilities for media generation.
Centralized language instruction mappings and utilities.
"""

import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

# Centralized language instruction mappings
LANGUAGE_INSTRUCTIONS: Dict[str, str] = {
    "en": "Write in English using natural, engaging language.",
    "es": "Escribe en español usando un lenguaje natural y atractivo.",
    "fr": "Écrivez en français en utilisant un langage naturel et engageant.",
    "de": "Schreiben Sie auf Deutsch mit natürlicher, ansprechender Sprache.",
    "it": "<PERSON><PERSON><PERSON> in italiano usando un linguaggio naturale e coinvolgente.",
    "pt": "Escreva em português usando linguagem natural e envolvente.",
    "ja": "自然で魅力的な言語を使って日本語で書いてください。",
    "ko": "자연스럽고 매력적인 언어를 사용하여 한국어로 작성하세요.",
    "zh": "使用自然、引人入胜的语言用中文写作。",
    "ar": "اكتب باللغة العربية باستخدام لغة طبيعية وجذابة.",
    "hi": "प्राकृतिक, आकर्षक भाषा का उपयोग करके हिंदी में लिखें।"
}


def get_language_instruction(language: str, fallback_language: str = "en") -> str:
    """
    Get language-specific instruction with fallback support.

    Args:
        language: Target language code
        fallback_language: Fallback language code if target is not available

    Returns:
        Language instruction string
    """
    instruction = LANGUAGE_INSTRUCTIONS.get(language)

    if not instruction:
        if fallback_language != language:
            instruction = LANGUAGE_INSTRUCTIONS.get(fallback_language)
            if instruction:
                logger.info(f"Using fallback language instruction for {fallback_language}")
            else:
                instruction = LANGUAGE_INSTRUCTIONS["en"]
                logger.warning(f"No instruction found for {language} or {fallback_language}, using English")
        else:
            instruction = LANGUAGE_INSTRUCTIONS["en"]
            logger.warning(f"No instruction found for {language}, using English")

    # Add fallback note if using different language
    if language != "en" and fallback_language == "en":
        instruction += f" If unable to generate in {language}, fall back to English."

    return instruction


def validate_language_code(language_code: str) -> bool:
    """
    Validate if a language code is supported.

    Args:
        language_code: Language code to validate

    Returns:
        True if supported, False otherwise
    """
    return language_code in LANGUAGE_INSTRUCTIONS


def get_supported_languages() -> list[str]:
    """
    Get list of supported language codes.

    Returns:
        List of supported language codes
    """
    return list(LANGUAGE_INSTRUCTIONS.keys())


def get_language_display_name(language_code: str) -> str:
    """
    Get human-readable display name for language code.

    Args:
        language_code: Language code

    Returns:
        Display name or the code itself if not found
    """
    display_names = {
        "en": "English",
        "es": "Spanish",
        "fr": "French",
        "de": "German",
        "it": "Italian",
        "pt": "Portuguese",
        "ja": "Japanese",
        "ko": "Korean",
        "zh": "Chinese",
        "ar": "Arabic",
        "hi": "Hindi"
    }

    return display_names.get(language_code, language_code.upper())