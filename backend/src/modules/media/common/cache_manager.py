"""
Intelligent Caching System for Media Generation.
Implements multi-level caching with TTL, LRU eviction, and cache warming.
"""

import hashlib
import json
import logging
import pickle
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import redis.asyncio as redis
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl_seconds: Optional[int] = None
    size_bytes: int = 0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.ttl_seconds is None:
            return False
        return (datetime.utcnow() - self.created_at).total_seconds() > self.ttl_seconds
    
    @property
    def age_seconds(self) -> float:
        """Get age of cache entry in seconds."""
        return (datetime.utcnow() - self.created_at).total_seconds()


class MediaCacheManager:
    """Intelligent cache manager for media generation results."""
    
    def __init__(
        self,
        redis_url: Optional[str] = None,
        local_cache_size: int = 1000,
        default_ttl: int = 3600  # 1 hour
    ):
        self.redis_client = None
        self.local_cache: Dict[str, CacheEntry] = {}
        self.local_cache_size = local_cache_size
        self.default_ttl = default_ttl
        
        if redis_url:
            self.redis_client = redis.from_url(redis_url)
    
    def _generate_cache_key(
        self,
        product_context: Dict[str, Any],
        generation_params: Dict[str, Any],
        provider: str
    ) -> str:
        """Generate deterministic cache key from inputs."""
        # Create a normalized representation of the inputs
        cache_data = {
            "product": {
                "title": product_context.get("title", ""),
                "category": product_context.get("category", ""),
                "colors": sorted(product_context.get("colors", [])),
                "materials": sorted(product_context.get("materials", [])),
                "key_features": sorted(product_context.get("key_features", [])),
                "price_tier": product_context.get("price_tier", ""),
            },
            "params": {
                "media_type": generation_params.get("media_type", ""),
                "aspect_ratio": generation_params.get("aspect_ratio", ""),
                "style": generation_params.get("style", ""),
                "template_id": generation_params.get("template_id", ""),
                "quality": generation_params.get("quality", ""),
            },
            "provider": provider
        }
        
        # Create hash of normalized data
        cache_string = json.dumps(cache_data, sort_keys=True)
        cache_hash = hashlib.sha256(cache_string.encode()).hexdigest()
        
        return f"media_cache:{provider}:{cache_hash[:16]}"
    
    async def get(
        self,
        product_context: Dict[str, Any],
        generation_params: Dict[str, Any],
        provider: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached media generation result."""
        cache_key = self._generate_cache_key(product_context, generation_params, provider)

        logger.debug(f"DEBUG: CacheManager.get() called for key: {cache_key}")

        # Try local cache first
        local_entry = self.local_cache.get(cache_key)
        if local_entry and not local_entry.is_expired:
            local_entry.last_accessed = datetime.utcnow()
            local_entry.access_count += 1
            logger.debug(f"Cache hit (local): {cache_key}")
            return local_entry.value

        # Try Redis cache
        if self.redis_client:
            try:
                cached_data = await self.redis_client.get(cache_key)
                if cached_data:
                    result = pickle.loads(cached_data)

                    # Update local cache
                    self._update_local_cache(cache_key, result)

                    logger.debug(f"Cache hit (Redis): {cache_key}")
                    return result
            except Exception as e:
                logger.warning(f"Redis cache error: {e}")

        logger.debug(f"Cache miss: {cache_key}")
        return None
    
    async def set(
        self,
        product_context: Dict[str, Any],
        generation_params: Dict[str, Any],
        provider: str,
        result: Dict[str, Any],
        ttl_seconds: Optional[int] = None
    ):
        """Cache media generation result."""
        cache_key = self._generate_cache_key(product_context, generation_params, provider)
        ttl = ttl_seconds or self.default_ttl
        
        # Update local cache
        self._update_local_cache(cache_key, result, ttl)
        
        # Update Redis cache
        if self.redis_client:
            try:
                cached_data = pickle.dumps(result)
                await self.redis_client.setex(cache_key, ttl, cached_data)
                logger.debug(f"Cached result (Redis): {cache_key}")
            except Exception as e:
                logger.warning(f"Redis cache error: {e}")
    
    def _update_local_cache(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None
    ):
        """Update local cache with LRU eviction."""
        now = datetime.utcnow()
        
        # Create cache entry
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=now,
            last_accessed=now,
            ttl_seconds=ttl_seconds,
            size_bytes=len(pickle.dumps(value))
        )
        
        # Add to cache
        self.local_cache[key] = entry
        
        # Evict if cache is full
        if len(self.local_cache) > self.local_cache_size:
            self._evict_lru()
    
    def _evict_lru(self):
        """Evict least recently used entries."""
        # Sort by last accessed time
        sorted_entries = sorted(
            self.local_cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        # Remove oldest 10% of entries
        evict_count = max(1, len(sorted_entries) // 10)
        for i in range(evict_count):
            key, _ = sorted_entries[i]
            del self.local_cache[key]
        
        logger.debug(f"Evicted {evict_count} cache entries")
    
    async def invalidate_by_product(self, product_id: str):
        """Invalidate all cache entries for a specific product."""
        # For local cache, we'd need to track product associations
        # For now, we'll implement a simple pattern-based invalidation
        
        if self.redis_client:
            try:
                # Get all keys matching the pattern
                pattern = f"media_cache:*:*{product_id}*"
                keys = await self.redis_client.keys(pattern)
                
                if keys:
                    await self.redis_client.delete(*keys)
                    logger.info(f"Invalidated {len(keys)} cache entries for product {product_id}")
            except Exception as e:
                logger.warning(f"Cache invalidation error: {e}")
    
    async def invalidate_by_provider(self, provider: str):
        """Invalidate all cache entries for a specific provider."""
        # Remove from local cache
        keys_to_remove = [
            key for key, entry in self.local_cache.items()
            if key.startswith(f"media_cache:{provider}:")
        ]
        
        for key in keys_to_remove:
            del self.local_cache[key]
        
        # Remove from Redis
        if self.redis_client:
            try:
                pattern = f"media_cache:{provider}:*"
                keys = await self.redis_client.keys(pattern)
                
                if keys:
                    await self.redis_client.delete(*keys)
                    logger.info(f"Invalidated {len(keys)} cache entries for provider {provider}")
            except Exception as e:
                logger.warning(f"Cache invalidation error: {e}")
    
    async def warm_cache(
        self,
        popular_products: List[Dict[str, Any]],
        common_params: List[Dict[str, Any]],
        providers: List[str]
    ):
        """Pre-warm cache with popular product combinations."""
        logger.info("Starting cache warming...")
        
        warm_count = 0
        for product in popular_products:
            for params in common_params:
                for provider in providers:
                    cache_key = self._generate_cache_key(product, params, provider)
                    
                    # Check if already cached
                    if cache_key in self.local_cache:
                        continue
                    
                    if self.redis_client:
                        exists = await self.redis_client.exists(cache_key)
                        if exists:
                            continue
                    
                    # This would trigger actual generation in a real implementation
                    # For now, we'll just log the cache warming opportunity
                    logger.debug(f"Cache warming opportunity: {cache_key}")
                    warm_count += 1
        
        logger.info(f"Cache warming completed. {warm_count} opportunities identified.")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        now = datetime.utcnow()

        total_entries = len(self.local_cache)
        expired_entries = sum(1 for entry in self.local_cache.values() if entry.is_expired)
        total_size = sum(entry.size_bytes for entry in self.local_cache.values())

        # Calculate hit rate (simplified)
        total_accesses = sum(entry.access_count for entry in self.local_cache.values())

        # Log cache contents for debugging
        logger.debug(f"DEBUG: Cache stats - total_entries: {total_entries}, expired: {expired_entries}")
        if total_entries > 0:
            logger.debug("DEBUG: Cache keys:")
            for key in self.local_cache.keys():
                logger.debug(f"  - {key}")

        return {
            "local_cache": {
                "total_entries": total_entries,
                "expired_entries": expired_entries,
                "active_entries": total_entries - expired_entries,
                "total_size_bytes": total_size,
                "total_accesses": total_accesses,
                "cache_utilization": total_entries / self.local_cache_size
            },
            "redis_connected": self.redis_client is not None
        }
    
    async def cleanup_expired(self):
        """Remove expired entries from local cache."""
        expired_keys = [
            key for key, entry in self.local_cache.items()
            if entry.is_expired
        ]
        
        for key in expired_keys:
            del self.local_cache[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def close(self):
        """Close cache connections."""
        if self.redis_client:
            await self.redis_client.close()


# Global cache manager instance
media_cache_manager = MediaCacheManager()
