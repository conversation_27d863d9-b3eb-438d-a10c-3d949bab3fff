"""
Performance Monitoring for Media Generation System.
Tracks metrics, latency, throughput, and system health.
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data point."""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = "ms"


@dataclass
class ProviderMetrics:
    """Metrics for a specific provider."""
    provider_name: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_latency: float = 0.0
    min_latency: float = float('inf')
    max_latency: float = 0.0
    latency_samples: deque = field(default_factory=lambda: deque(maxlen=1000))
    error_rate: float = 0.0
    throughput_per_minute: float = 0.0
    last_request_time: Optional[datetime] = None
    
    @property
    def avg_latency(self) -> float:
        """Calculate average latency."""
        if self.total_requests == 0:
            return 0.0
        return self.total_latency / self.total_requests
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage."""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def p95_latency(self) -> float:
        """Calculate 95th percentile latency."""
        if not self.latency_samples:
            return 0.0
        return statistics.quantiles(list(self.latency_samples), n=20)[18]  # 95th percentile
    
    @property
    def p99_latency(self) -> float:
        """Calculate 99th percentile latency."""
        if not self.latency_samples:
            return 0.0
        return statistics.quantiles(list(self.latency_samples), n=100)[98]  # 99th percentile


class MediaPerformanceMonitor:
    """Comprehensive performance monitoring for media generation."""
    
    def __init__(self, metrics_retention_hours: int = 24):
        self.provider_metrics: Dict[str, ProviderMetrics] = {}
        self.system_metrics: List[PerformanceMetric] = []
        self.metrics_retention = timedelta(hours=metrics_retention_hours)
        self.start_time = datetime.utcnow()

        # Real-time monitoring
        self.active_requests: Dict[str, datetime] = {}
        self.request_queue_size = 0

        # Alerting thresholds
        self.alert_thresholds = {
            "max_latency_ms": 30000,  # 30 seconds
            "min_success_rate": 95.0,  # 95%
            "max_error_rate": 5.0,    # 5%
            "max_queue_size": 100
        }
    
    @asynccontextmanager
    async def track_request(
        self,
        provider: str,
        operation: str,
        product_id: Optional[str] = None
    ):
        """Context manager to track request performance."""
        request_id = f"{provider}_{operation}_{int(time.time() * 1000)}"
        start_time = time.time()
        
        # Track active request
        self.active_requests[request_id] = datetime.utcnow()
        self.request_queue_size += 1
        
        try:
            yield request_id
            
            # Record successful request
            latency_ms = (time.time() - start_time) * 1000
            await self._record_request_success(provider, latency_ms, operation, product_id)
            
        except Exception as e:
            # Record failed request
            latency_ms = (time.time() - start_time) * 1000
            await self._record_request_failure(provider, latency_ms, str(e), operation, product_id)
            raise
        
        finally:
            # Clean up tracking
            if request_id in self.active_requests:
                del self.active_requests[request_id]
            self.request_queue_size = max(0, self.request_queue_size - 1)
    
    async def _record_request_success(
        self,
        provider: str,
        latency_ms: float,
        operation: str,
        product_id: Optional[str] = None
    ):
        """Record successful request metrics."""
        if provider not in self.provider_metrics:
            self.provider_metrics[provider] = ProviderMetrics(provider_name=provider)
        metrics = self.provider_metrics[provider]
        
        metrics.total_requests += 1
        metrics.successful_requests += 1
        metrics.total_latency += latency_ms
        metrics.min_latency = min(metrics.min_latency, latency_ms)
        metrics.max_latency = max(metrics.max_latency, latency_ms)
        metrics.latency_samples.append(latency_ms)
        metrics.last_request_time = datetime.utcnow()
        
        # Update error rate
        metrics.error_rate = (metrics.failed_requests / metrics.total_requests) * 100
        
        # Log performance metric
        self._add_metric(
            name="request_latency",
            value=latency_ms,
            tags={
                "provider": provider,
                "operation": operation,
                "status": "success",
                "product_id": product_id or "unknown"
            }
        )
        
        # Check for performance alerts
        await self._check_performance_alerts(provider, metrics)
    
    async def _record_request_failure(
        self,
        provider: str,
        latency_ms: float,
        error_message: str,
        operation: str,
        product_id: Optional[str] = None
    ):
        """Record failed request metrics."""
        if provider not in self.provider_metrics:
            self.provider_metrics[provider] = ProviderMetrics(provider_name=provider)
        metrics = self.provider_metrics[provider]
        
        metrics.total_requests += 1
        metrics.failed_requests += 1
        metrics.total_latency += latency_ms
        metrics.latency_samples.append(latency_ms)
        metrics.last_request_time = datetime.utcnow()
        
        # Update error rate
        metrics.error_rate = (metrics.failed_requests / metrics.total_requests) * 100
        
        # Log failure metric
        self._add_metric(
            name="request_latency",
            value=latency_ms,
            tags={
                "provider": provider,
                "operation": operation,
                "status": "failure",
                "error": error_message[:100],  # Truncate long errors
                "product_id": product_id or "unknown"
            }
        )
        
        # Check for performance alerts
        await self._check_performance_alerts(provider, metrics)
    
    def _add_metric(self, name: str, value: float, tags: Dict[str, str] = None, unit: str = "ms", timestamp: datetime = None):
        """Add a performance metric."""
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=timestamp or datetime.utcnow(),
            tags=tags or {},
            unit=unit
        )
        
        self.system_metrics.append(metric)
        
        # Clean up old metrics
        cutoff_time = datetime.utcnow() - self.metrics_retention
        self.system_metrics = [
            m for m in self.system_metrics
            if m.timestamp > cutoff_time
        ]
    
    async def _check_performance_alerts(self, provider: str, metrics: ProviderMetrics):
        """Check for performance issues and trigger alerts."""
        alerts = []
        
        # Check latency
        if metrics.max_latency > self.alert_thresholds["max_latency_ms"]:
            alerts.append(f"High latency detected for {provider}: {metrics.max_latency:.2f}ms")
        
        # Check success rate
        if metrics.success_rate < self.alert_thresholds["min_success_rate"]:
            alerts.append(f"Low success rate for {provider}: {metrics.success_rate:.2f}%")
        
        # Check error rate
        if metrics.error_rate > self.alert_thresholds["max_error_rate"]:
            alerts.append(f"High error rate for {provider}: {metrics.error_rate:.2f}%")
        
        # Check queue size
        if self.request_queue_size > self.alert_thresholds["max_queue_size"]:
            alerts.append(f"High queue size: {self.request_queue_size} requests")
        
        # Log alerts
        for alert in alerts:
            logger.warning(f"Performance Alert: {alert}")
    
    def get_provider_summary(self, provider: str) -> Dict[str, Any]:
        """Get performance summary for a provider."""
        if provider not in self.provider_metrics:
            return {"error": "Provider not found"}
        
        metrics = self.provider_metrics[provider]
        
        return {
            "provider": provider,
            "total_requests": metrics.total_requests,
            "successful_requests": metrics.successful_requests,
            "failed_requests": metrics.failed_requests,
            "success_rate_percent": round(metrics.success_rate, 2),
            "error_rate_percent": round(metrics.error_rate, 2),
            "avg_latency_ms": round(metrics.avg_latency, 2),
            "min_latency_ms": round(metrics.min_latency, 2),
            "max_latency_ms": round(metrics.max_latency, 2),
            "p95_latency_ms": round(metrics.p95_latency, 2),
            "p99_latency_ms": round(metrics.p99_latency, 2),
            "last_request": metrics.last_request_time.isoformat() if metrics.last_request_time else None
        }
    
    def get_system_summary(self) -> Dict[str, Any]:
        """Get overall system performance summary."""
        total_requests = sum(m.total_requests for m in self.provider_metrics.values())
        total_successful = sum(m.successful_requests for m in self.provider_metrics.values())
        total_failed = sum(m.failed_requests for m in self.provider_metrics.values())
        
        overall_success_rate = (total_successful / max(1, total_requests)) * 100
        overall_error_rate = (total_failed / max(1, total_requests)) * 100
        
        # Calculate system uptime
        uptime = datetime.utcnow() - self.start_time
        
        return {
            "uptime_seconds": int(uptime.total_seconds()),
            "total_requests": total_requests,
            "successful_requests": total_successful,
            "failed_requests": total_failed,
            "overall_success_rate_percent": round(overall_success_rate, 2),
            "overall_error_rate_percent": round(overall_error_rate, 2),
            "active_requests": len(self.active_requests),
            "queue_size": self.request_queue_size,
            "providers_count": len(self.provider_metrics),
            "metrics_count": len(self.system_metrics)
        }
    
    def get_all_providers_summary(self) -> Dict[str, Any]:
        """Get performance summary for all providers."""
        return {
            "system": self.get_system_summary(),
            "providers": {
                provider: self.get_provider_summary(provider)
                for provider in self.provider_metrics.keys()
            }
        }
    
    async def record_custom_metric(
        self,
        name: str,
        value: float,
        tags: Dict[str, str] = None,
        unit: str = "count"
    ):
        """Record a custom performance metric."""
        self._add_metric(name, value, tags, unit)
    
    def reset_provider_metrics(self, provider: str):
        """Reset metrics for a specific provider."""
        if provider in self.provider_metrics:
            del self.provider_metrics[provider]
            logger.info(f"Reset metrics for provider: {provider}")
    
    def reset_all_metrics(self):
        """Reset all performance metrics."""
        self.provider_metrics.clear()
        self.system_metrics.clear()
        self.active_requests.clear()
        self.request_queue_size = 0
        self.start_time = datetime.utcnow()
        logger.info("Reset all performance metrics")


# Global performance monitor instance
performance_monitor = MediaPerformanceMonitor()
