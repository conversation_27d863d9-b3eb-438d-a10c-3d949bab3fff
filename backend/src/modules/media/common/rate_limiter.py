"""
Rate Limiting and Quota Management for Media Providers.
Implements token bucket and sliding window rate limiting algorithms.
"""

import asyncio
import time
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import redis.asyncio as redis

logger = logging.getLogger(__name__)


@dataclass
class RateLimit:
    """Rate limit configuration."""
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int = 10
    cost_per_request: float = 1.0


@dataclass
class QuotaInfo:
    """Quota usage information."""
    used: int = 0
    limit: int = 0
    reset_time: Optional[datetime] = None
    remaining: int = 0
    
    def __post_init__(self):
        self.remaining = max(0, self.limit - self.used)


class TokenBucket:
    """Token bucket rate limiter implementation."""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """Attempt to consume tokens from the bucket."""
        async with self._lock:
            now = time.time()
            
            # Refill tokens based on elapsed time
            elapsed = now - self.last_refill
            self.tokens = min(self.capacity, self.tokens + elapsed * self.refill_rate)
            self.last_refill = now
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def wait_for_tokens(self, tokens: int = 1) -> float:
        """Calculate wait time for tokens to be available."""
        async with self._lock:
            if self.tokens >= tokens:
                return 0.0
            
            needed_tokens = tokens - self.tokens
            wait_time = needed_tokens / self.refill_rate
            return wait_time


class SlidingWindowRateLimiter:
    """Sliding window rate limiter using Redis."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    async def is_allowed(
        self,
        key: str,
        limit: int,
        window_seconds: int,
        cost: int = 1
    ) -> tuple[bool, QuotaInfo]:
        """Check if request is allowed within sliding window."""
        now = time.time()
        window_start = now - window_seconds
        
        # Use Redis pipeline for atomic operations
        pipe = self.redis.pipeline()
        
        # Remove expired entries
        pipe.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        pipe.zcard(key)
        
        # Add current request
        pipe.zadd(key, {str(now): now})
        
        # Set expiration
        pipe.expire(key, window_seconds)
        
        results = await pipe.execute()
        current_count = results[1]
        
        quota_info = QuotaInfo(
            used=current_count,
            limit=limit,
            remaining=max(0, limit - current_count),
            reset_time=datetime.fromtimestamp(now + window_seconds)
        )
        
        allowed = current_count + cost <= limit
        
        if not allowed:
            # Remove the request we just added since it's not allowed
            await self.redis.zrem(key, str(now))
        
        return allowed, quota_info


class MediaRateLimiter:
    """Comprehensive rate limiter for media generation providers."""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.provider_limits: Dict[str, RateLimit] = {}
        self.token_buckets: Dict[str, TokenBucket] = {}
        self.redis_client = None
        
        if redis_url:
            self.redis_client = redis.from_url(redis_url)
            self.sliding_limiter = SlidingWindowRateLimiter(self.redis_client)
    
    def configure_provider(self, provider_name: str, rate_limit: RateLimit):
        """Configure rate limits for a provider."""
        self.provider_limits[provider_name] = rate_limit
        
        # Create token bucket for burst control
        self.token_buckets[provider_name] = TokenBucket(
            capacity=rate_limit.burst_limit,
            refill_rate=rate_limit.requests_per_minute / 60.0  # tokens per second
        )
    
    async def check_rate_limit(
        self,
        provider_name: str,
        user_id: int,
        cost: float = 1.0
    ) -> tuple[bool, Dict[str, QuotaInfo]]:
        """Check if request is within rate limits."""
        if provider_name not in self.provider_limits:
            # No limits configured, allow request
            return True, {}
        
        rate_limit = self.provider_limits[provider_name]
        quota_info = {}
        
        # Check token bucket (burst control)
        bucket = self.token_buckets.get(provider_name)
        if bucket:
            tokens_needed = int(cost * rate_limit.cost_per_request)
            if not await bucket.consume(tokens_needed):
                quota_info["burst"] = QuotaInfo(
                    used=rate_limit.burst_limit,
                    limit=rate_limit.burst_limit,
                    remaining=0
                )
                return False, quota_info
        
        # Check sliding window limits if Redis is available
        if self.redis_client:
            user_key = f"rate_limit:{provider_name}:{user_id}"
            
            # Check per-minute limit
            allowed, minute_quota = await self.sliding_limiter.is_allowed(
                f"{user_key}:minute",
                rate_limit.requests_per_minute,
                60,
                int(cost)
            )
            quota_info["per_minute"] = minute_quota
            
            if not allowed:
                return False, quota_info
            
            # Check per-hour limit
            allowed, hour_quota = await self.sliding_limiter.is_allowed(
                f"{user_key}:hour",
                rate_limit.requests_per_hour,
                3600,
                int(cost)
            )
            quota_info["per_hour"] = hour_quota
            
            if not allowed:
                return False, quota_info
            
            # Check per-day limit
            allowed, day_quota = await self.sliding_limiter.is_allowed(
                f"{user_key}:day",
                rate_limit.requests_per_day,
                86400,
                int(cost)
            )
            quota_info["per_day"] = day_quota
            
            if not allowed:
                return False, quota_info
        
        return True, quota_info
    
    async def get_wait_time(self, provider_name: str, cost: float = 1.0) -> float:
        """Get estimated wait time before next request is allowed."""
        if provider_name not in self.provider_limits:
            return 0.0
        
        bucket = self.token_buckets.get(provider_name)
        if bucket:
            rate_limit = self.provider_limits[provider_name]
            tokens_needed = int(cost * rate_limit.cost_per_request)
            return await bucket.wait_for_tokens(tokens_needed)
        
        return 0.0
    
    async def get_quota_status(
        self,
        provider_name: str,
        user_id: int
    ) -> Dict[str, QuotaInfo]:
        """Get current quota status for a user and provider."""
        if not self.redis_client or provider_name not in self.provider_limits:
            return {}
        
        rate_limit = self.provider_limits[provider_name]
        user_key = f"rate_limit:{provider_name}:{user_id}"
        quota_status = {}
        
        # Get current usage for each time window
        now = time.time()
        
        # Per-minute quota
        minute_count = await self.redis_client.zcount(
            f"{user_key}:minute", now - 60, now
        )
        quota_status["per_minute"] = QuotaInfo(
            used=minute_count,
            limit=rate_limit.requests_per_minute,
            reset_time=datetime.fromtimestamp(now + 60)
        )
        
        # Per-hour quota
        hour_count = await self.redis_client.zcount(
            f"{user_key}:hour", now - 3600, now
        )
        quota_status["per_hour"] = QuotaInfo(
            used=hour_count,
            limit=rate_limit.requests_per_hour,
            reset_time=datetime.fromtimestamp(now + 3600)
        )
        
        # Per-day quota
        day_count = await self.redis_client.zcount(
            f"{user_key}:day", now - 86400, now
        )
        quota_status["per_day"] = QuotaInfo(
            used=day_count,
            limit=rate_limit.requests_per_day,
            reset_time=datetime.fromtimestamp(now + 86400)
        )
        
        return quota_status
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.redis_client:
            await self.redis_client.close()


# Default rate limits for providers
DEFAULT_RATE_LIMITS = {
    "banana": RateLimit(
        requests_per_minute=10,
        requests_per_hour=100,
        requests_per_day=1000,
        burst_limit=5,
        cost_per_request=1.0
    ),
    "veo3": RateLimit(
        requests_per_minute=5,
        requests_per_hour=50,
        requests_per_day=200,
        burst_limit=3,
        cost_per_request=2.0
    ),
    "gemini": RateLimit(
        requests_per_minute=20,
        requests_per_hour=500,
        requests_per_day=5000,
        burst_limit=10,
        cost_per_request=0.5
    )
}
