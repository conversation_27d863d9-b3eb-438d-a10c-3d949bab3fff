from sqlalchemy import Column, <PERSON><PERSON><PERSON><PERSON>, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import uuid

from core.db.database import Base


class Asset(Base):
    """Asset model for media items (images and videos) from products."""

    __tablename__ = "assets"

    # Primary key
    id = Column(BigInteger, primary_key=True, index=True)
    uuid_external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)

    # Asset type
    type = Column(String, nullable=False)  # image, video, etc.

    # Source information
    product_id = Column(Integer, nullable=False)
    product_external_id = Column(String)
    store_id = Column(Integer, nullable=False)

    # Media data
    src = Column(String, nullable=False, unique=True)  # URL - must be unique
    alt = Column(String)  # Alt text
    external_id = Column(String)  # Original media ID

    # Additional metadata
    width = Column(Integer)
    height = Column(Integer)
    position = Column(Integer, default=0)

    # Source type to distinguish AI-generated vs product images
    source_type = Column(String, default="product")  # "product" or "ai_generated"

    # Raw data
    full_json = Column(Text)  # JSON: Complete media data

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    source_updated_at = Column(DateTime(timezone=True))  # Source system update timestamp