"""
Assets API Router
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.assets.schemas import PaginatedAssetResponse, MediaItem, AssetReassignRequest, AssetReassignResponse
from modules.assets.service import asset_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=PaginatedAssetResponse)
async def get_assets(
    page: int = 1,
    limit: int = 200,  # Default to 200 as per user request
    search: str = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all assets (images and videos) from all products for the current user's stores."""
    try:
        logger.info(f"Assets API called with page={page}, limit={limit}" + (f", search='{search}'" if search else ""))

        # Get assets for user
        assets = await asset_service.get_assets_for_user(db, current_user.id, page, limit, search)
        total_count = await asset_service.get_total_assets_count(db, current_user.id, search)

        # Calculate total pages
        total_pages = (total_count + limit - 1) // limit  # Ceiling division

        logger.info(f"Returning page {page} with {len(assets)} assets out of {total_count} total")

        response = PaginatedAssetResponse(
            items=assets,
            total=total_count,
            page=page,
            limit=limit,
            total_pages=total_pages
        )

        return response

    except Exception as e:
        logger.error(f"Error getting assets: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reassign", response_model=AssetReassignResponse)
async def reassign_asset(
    request: AssetReassignRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Reassign an asset to a different product."""
    try:
        logger.info(f"Reassigning asset {request.asset_id} to product {request.new_product_id} for user {current_user.id}")

        success = await asset_service.reassign_asset_to_product(
            db=db,
            asset_id=request.asset_id,
            new_product_id=request.new_product_id,
            user_id=current_user.id
        )

        if success:
            return AssetReassignResponse(
                success=True,
                message=f"Asset {request.asset_id} successfully reassigned to product {request.new_product_id}"
            )
        else:
            return AssetReassignResponse(
                success=False,
                message="Failed to reassign asset. Please check that both the asset and target product exist and belong to your account."
            )

    except Exception as e:
        logger.error(f"Error reassigning asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))