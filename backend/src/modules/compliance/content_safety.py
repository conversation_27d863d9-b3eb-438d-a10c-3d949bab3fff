"""
Content Safety and Legal Compliance Module
Handles brand filtering, logo detection, and rights validation for generated content.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)


class ContentFlag(str, Enum):
    """Content safety flags."""
    COMPETITOR_BRAND = "competitor_brand"
    LOGO_DETECTED = "logo_detected"
    TRADEMARK_VIOLATION = "trademark_violation"
    COPYRIGHT_CONCERN = "copyright_concern"
    INAPPROPRIATE_CONTENT = "inappropriate_content"
    MEDICAL_CLAIMS = "medical_claims"
    FALSE_ADVERTISING = "false_advertising"


class ContentSafetyService:
    """Service for content safety and legal compliance checks."""
    
    def __init__(self):
        self.competitor_brands = self._load_competitor_brands()
        self.trademark_patterns = self._load_trademark_patterns()
        self.medical_claim_patterns = self._load_medical_claim_patterns()
    
    def _load_competitor_brands(self) -> List[str]:
        """Load list of competitor brands to filter."""
        return [
            "nike", "adidas", "apple", "samsung", "gucci", "louis vuitton",
            "chanel", "prada", "rolex", "omega", "coca-cola", "pepsi",
            "mcdonalds", "starbucks", "amazon", "google", "microsoft",
            "facebook", "instagram", "tiktok", "youtube", "twitter",
            "spotify", "netflix", "disney", "marvel", "dc comics"
        ]
    
    def _load_trademark_patterns(self) -> List[str]:
        """Load trademark violation patterns."""
        return [
            r"®", r"™", r"©", r"\bcopyright\b", r"\btrademark\b",
            r"\bpatented\b", r"\bregistered\b", r"\bofficial\b"
        ]
    
    def _load_medical_claim_patterns(self) -> List[str]:
        """Load medical claim patterns to flag."""
        return [
            r"\bcures?\b", r"\btreat(s|ment)?\b", r"\bheals?\b",
            r"\bmedical(ly)?\b", r"\btherapeutic\b", r"\bclinical(ly)?\b",
            r"\bFDA approved\b", r"\bdoctor recommended\b",
            r"\bprescription\b", r"\bmedicine\b", r"\bdrug\b",
            r"\bdiagnose(s|d)?\b", r"\bprevent(s|ion)?\b"
        ]
    
    async def check_content_safety(
        self, 
        content: str, 
        content_type: str = "text",
        product_category: Optional[str] = None
    ) -> Tuple[bool, List[ContentFlag], Dict[str, Any]]:
        """
        Comprehensive content safety check.
        
        Returns:
            Tuple of (is_safe, flags, details)
        """
        flags = []
        details = {}
        
        # Check for competitor brand mentions
        brand_flags = self._check_competitor_brands(content)
        if brand_flags:
            flags.append(ContentFlag.COMPETITOR_BRAND)
            details["competitor_brands"] = brand_flags
        
        # Check for trademark violations
        trademark_flags = self._check_trademark_violations(content)
        if trademark_flags:
            flags.append(ContentFlag.TRADEMARK_VIOLATION)
            details["trademark_issues"] = trademark_flags
        
        # Check for medical claims
        medical_flags = self._check_medical_claims(content)
        if medical_flags:
            flags.append(ContentFlag.MEDICAL_CLAIMS)
            details["medical_claims"] = medical_flags
        
        # Category-specific checks
        if product_category:
            category_flags = self._check_category_specific(content, product_category)
            flags.extend(category_flags)
        
        # Logo detection for images (placeholder - would integrate with vision AI)
        if content_type == "image":
            logo_detected = await self._detect_logos(content)
            if logo_detected:
                flags.append(ContentFlag.LOGO_DETECTED)
                details["logos_detected"] = logo_detected
        
        is_safe = len(flags) == 0
        
        return is_safe, flags, details
    
    def _check_competitor_brands(self, content: str) -> List[str]:
        """Check for competitor brand mentions."""
        found_brands = []
        content_lower = content.lower()
        
        for brand in self.competitor_brands:
            # Use word boundaries to avoid false positives
            pattern = rf"\b{re.escape(brand.lower())}\b"
            if re.search(pattern, content_lower):
                found_brands.append(brand)
        
        return found_brands
    
    def _check_trademark_violations(self, content: str) -> List[str]:
        """Check for trademark violation patterns."""
        violations = []
        
        for pattern in self.trademark_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                violations.extend(matches)
        
        return violations
    
    def _check_medical_claims(self, content: str) -> List[str]:
        """Check for medical claims that could be problematic."""
        claims = []
        
        for pattern in self.medical_claim_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                claims.extend(matches)
        
        return claims
    
    def _check_category_specific(self, content: str, category: str) -> List[ContentFlag]:
        """Check category-specific content issues."""
        flags = []
        content_lower = content.lower()
        
        if category == "beauty_cosmetics":
            # Check for FDA-related claims
            if re.search(r"\bfda\b", content_lower) and not re.search(r"fda compliant", content_lower):
                flags.append(ContentFlag.FALSE_ADVERTISING)
        
        elif category == "children_baby":
            # Check for safety-related content
            unsafe_terms = ["small parts", "choking hazard", "not suitable for children"]
            for term in unsafe_terms:
                if term in content_lower:
                    flags.append(ContentFlag.INAPPROPRIATE_CONTENT)
        
        elif category == "electronics":
            # Check for false technical claims
            false_claims = ["unlimited battery", "never breaks", "indestructible"]
            for claim in false_claims:
                if claim in content_lower:
                    flags.append(ContentFlag.FALSE_ADVERTISING)
        
        return flags
    
    async def _detect_logos(self, image_content: str) -> List[str]:
        """
        Detect logos in images using AI vision.
        This is a placeholder - would integrate with Google Vision API or similar.
        """
        try:
            # Placeholder for logo detection
            # In production, this would use:
            # - Google Vision API Logo Detection
            # - AWS Rekognition
            # - Custom trained model
            
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.warning(f"Logo detection failed: {e}")
            return []
    
    def filter_content(self, content: str, flags: List[ContentFlag]) -> str:
        """Filter content based on safety flags."""
        filtered_content = content
        
        if ContentFlag.COMPETITOR_BRAND in flags:
            # Remove competitor brand mentions
            for brand in self.competitor_brands:
                pattern = rf"\b{re.escape(brand)}\b"
                filtered_content = re.sub(pattern, "[brand name]", filtered_content, flags=re.IGNORECASE)
        
        if ContentFlag.MEDICAL_CLAIMS in flags:
            # Remove medical claims
            for pattern in self.medical_claim_patterns:
                filtered_content = re.sub(pattern, "", filtered_content, flags=re.IGNORECASE)
        
        if ContentFlag.TRADEMARK_VIOLATION in flags:
            # Remove trademark symbols
            for pattern in self.trademark_patterns:
                filtered_content = re.sub(pattern, "", filtered_content, flags=re.IGNORECASE)
        
        # Clean up extra spaces
        filtered_content = re.sub(r'\s+', ' ', filtered_content).strip()
        
        return filtered_content
    
    async def validate_rights(
        self, 
        content_type: str,
        media_url: Optional[str] = None,
        audio_used: Optional[str] = None,
        music_used: Optional[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        Validate rights for media content.
        
        Returns:
            Tuple of (rights_valid, issues)
        """
        issues = []
        
        if content_type == "video":
            # Check music/audio rights
            if music_used:
                music_issues = await self._check_music_rights(music_used)
                issues.extend(music_issues)
            
            if audio_used:
                audio_issues = await self._check_audio_rights(audio_used)
                issues.extend(audio_issues)
        
        elif content_type == "image":
            # Check image rights
            if media_url:
                image_issues = await self._check_image_rights(media_url)
                issues.extend(image_issues)
        
        rights_valid = len(issues) == 0
        return rights_valid, issues
    
    async def _check_music_rights(self, music_identifier: str) -> List[str]:
        """Check music licensing rights."""
        # Placeholder for music rights checking
        # Would integrate with:
        # - Music licensing databases
        # - Royalty-free music libraries
        # - Copyright detection services
        return []
    
    async def _check_audio_rights(self, audio_identifier: str) -> List[str]:
        """Check audio/voice rights."""
        # Placeholder for audio rights checking
        return []
    
    async def _check_image_rights(self, image_url: str) -> List[str]:
        """Check image copyright and usage rights."""
        # Placeholder for image rights checking
        # Would integrate with:
        # - Reverse image search
        # - Copyright databases
        # - Stock photo verification
        return []


# Global instance
content_safety_service = ContentSafetyService()
